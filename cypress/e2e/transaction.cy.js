describe('Transaction', () => {
	beforeEach(() => {
    cy.login('<EMAIL>', 'testing1')
  })
  // it('creates new transaction', () => {
	// 	cy.getByData('newTransaction').click();
	// 	cy.getByData('firstName').type('Jim')
	// 	cy.getByData('lastName').type('Olsen')
	// 	cy.getByData('email').type('<EMAIL>')
	// 	cy.getByData('submit').click();
	// 	cy.url().should('include', '/documents')
  // })
	it('requires first name, last name, email, and transaction name', () => {
		cy.getByData('newTransaction').click();
		cy.getByData('middleName').type('Middle')
		cy.getByData('submit').click();

		cy.getByData('firstName').next().contains('required')
		cy.getByData('lastName').next().contains('required')
		cy.getByData('email').next().contains('required')
		cy.getByData('transactionTitle').next().contains('You must name')
  })

})