import { add, format } from "date-fns"

describe('Tasks', () => {
	beforeEach(() => {
    cy.login('<EMAIL>', 'testing1')
		cy.goToTransaction('Olsen')
		cy.goToTasksTrans()
  })
	it('adds a task', () => {
		cy.getByData('add-task-btn').click();
		cy.getByData('task-name-input').type('Inspection DL')
		cy.getByData('task-submit-btn').click();
		cy.get('.react-datepicker-wrapper').next().contains('You must provide')
		cy.get('.react-datepicker-wrapper').find('input').eq(0).type(format(add(new Date(),{
			days: 1
		}), "MM-dd-yyyy"))
		cy.getByData('task-submit-btn').click();
		cy.get('table').contains('td', 'Inspection DL').should('be.visible')
		// Check task appears on 'All' page
		cy.getByData('tasks-trans-all-nav').click();
		cy.get('table').contains('td', 'Inspection DL').should('be.visible')
		// Check task appears on Transaction Overview page
		cy.getByData('overview-trans-nav').click();
		cy.get('table').contains('td', 'Inspection DL').should('be.visible')
		cy.get('.calendar-event').contains('Inspection DL').should('be.visible')
		// Check task appears on Transaction Calendar page
		cy.getByData('calendar-trans-nav').click();
		cy.get('.rbc-event-content').contains('Inspection DL').should('be.visible')
		// Check task appears on Overview (All) page
		cy.getByData('overview-all-nav').click();
		cy.get('table').contains('td', 'Inspection DL').should('be.visible')
		cy.get('.calendar-event').contains('Inspection DL - Olsen').should('be.visible')
		// Check task appears on Tasks (All) page
		cy.getByData('tasks-all-nav').click();
		cy.get('table').contains('td', 'Inspection DL').should('be.visible')
		// Check task appears on Calendar (All) page
		cy.getByData('calendar-all-nav').click();
		cy.get('.rbc-event-content').contains('Inspection DL - Olsen').should('be.visible')
  })
	it('edits a task', () => {
		cy.getByData('task-action-buttons-dropdown').first().click();
		cy.getByData('task-edit').first().click();
		cy.getByData('task-name-input').clear()
		cy.getByData('task-name-input').type('Appraisal DL')
		cy.getByData('task-submit-btn').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on 'All' page
		cy.getByData('tasks-trans-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on Transaction Overview page
		cy.getByData('overview-trans-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		cy.get('.calendar-event').contains('Appraisal DL').should('be.visible')
		// Check task appears on Transaction Calendar page
		cy.getByData('calendar-trans-nav').click();
		cy.get('.rbc-event-content').contains('Appraisal DL').should('be.visible')
		// Check task appears on Overview (All) page
		cy.getByData('overview-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		cy.get('.calendar-event').contains('Appraisal DL - Olsen').should('be.visible')
		// Check task appears on Tasks (All) page
		cy.getByData('tasks-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on Calendar (All) page
		cy.getByData('calendar-all-nav').click();
		cy.get('.rbc-event-content').contains('Appraisal DL - Olsen').should('be.visible')
  })
	it('archives a task', () => {
		cy.getByData('task-action-buttons-dropdown').first().click();
		cy.getByData('task-archive').first().click();
		cy.get('table').should('not.exist')
		// Check task appears on 'All' page
		cy.getByData('tasks-trans-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task does not appear on Transaction Overview page
		cy.getByData('overview-trans-nav').click();
		cy.get('p').contains('There are no upcoming deadlines').should('be.visible')
		cy.get('.calendar-event').should('not.exist')
		// Check task does not appear on Transaction Calendar page
		cy.getByData('calendar-trans-nav').click();
		cy.get('.rbc-event-content').should('not.exist')
		// Check task does not appear on Overview (All) page
		cy.getByData('overview-all-nav').click();
		cy.get('p').contains('There are no upcoming deadlines').should('be.visible')
		cy.get('.calendar-event').should('not.exist')
		// Check task does not appear on Tasks (All) page
		cy.getByData('tasks-all-nav').click();
		cy.get('table').should('not.exist')
		// Check task does not appear on Calendar (All) page
		cy.getByData('calendar-all-nav').click();
		cy.get('.rbc-event-content').should('not.exist')
  })
	it('unarchives a task', () => {
		cy.getByData('tasks-trans-all-nav').click();
		cy.getByData('task-action-buttons-dropdown').first().click();
		cy.getByData('task-make-active').first().click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on 'Active' page
		cy.getByData('tasks-trans-active-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on Transaction Overview page
		cy.getByData('overview-trans-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		cy.get('.calendar-event').contains('Appraisal DL').should('be.visible')
		// Check task appears on Transaction Calendar page
		cy.getByData('calendar-trans-nav').click();
		cy.get('.rbc-event-content').contains('Appraisal DL').should('be.visible')
		// Check task appears on Overview (All) page
		cy.getByData('overview-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		cy.get('.calendar-event').contains('Appraisal DL - Olsen').should('be.visible')
		// Check task appears on Tasks (All) page
		cy.getByData('tasks-all-nav').click();
		cy.get('table').contains('td', 'Appraisal DL').should('be.visible')
		// Check task appears on Calendar (All) page
		cy.getByData('calendar-all-nav').click();
		cy.get('.rbc-event-content').contains('Appraisal DL - Olsen').should('be.visible')
  })
	it('deletes a task', () => {
		cy.getByData('task-action-buttons-dropdown').first().click();
		cy.getByData('task-delete').first().click();
		cy.get('.modals').first().find('.primary').click()
		cy.get('table').should('not.exist')
		// Check task does not appear on 'All' page
		cy.getByData('tasks-trans-all-nav').click();
		cy.get('table').should('not.exist')
		// Check task does not appear on Transaction Overview page
		cy.getByData('overview-trans-nav').click();
		cy.get('p').contains('There are no upcoming deadlines').should('be.visible')
		cy.get('.calendar-event').should('not.exist')
		// Check task does not appear on Transaction Calendar page
		cy.getByData('calendar-trans-nav').click();
		cy.get('.rbc-event-content').should('not.exist')
		// Check task does not appear on Overview (All) page
		cy.getByData('overview-all-nav').click();
		cy.get('p').contains('There are no upcoming deadlines').should('be.visible')
		cy.get('.calendar-event').should('not.exist')
		// Check task does not appear on Tasks (All) page
		cy.getByData('tasks-all-nav').click();
		cy.get('table').should('not.exist')
		// Check task does not appear on Calendar (All) page
		cy.getByData('calendar-all-nav').click();
		cy.get('.rbc-event-content').should('not.exist')
  })
})