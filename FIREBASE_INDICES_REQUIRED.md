# Firebase Firestore Indices Required for Archived Document Labels

To support the new archived document labeling feature, you need to add the following composite indices to your Firebase Firestore database:

## Required Composite Indices

### 1. Documents Collection - Transaction + Archived + Updated At
```
Collection: documents
Fields: 
- transactionId (Ascending)
- archived (Ascending) 
- updatedAt (Descending)
```

### 2. Documents Collection - Transaction + Archived + Archived Label + Updated At
```
Collection: documents
Fields:
- transactionId (Ascending)
- archived (Ascending)
- archivedLabel (Ascending)
- updatedAt (Descending)
```

### 3. Documents Collection - User Access + Archived + Archived Label
```
Collection: documents
Fields:
- userId (Ascending)
- archived (Ascending)
- archivedLabel (Ascending)
- updatedAt (Descending)
```

### 4. Documents Collection - TC Access + Archived + Archived Label
```
Collection: documents
Fields:
- tcId (Ascending)
- archived (Ascending)
- archivedLabel (Ascending)
- updatedAt (Descending)
```

### 5. Documents Collection - Manager Access + Archived + Archived Label
```
Collection: documents
Fields:
- managerId (Ascending)
- archived (Ascending)
- archivedLabel (Ascending)
- updatedAt (Descending)
```

## How to Add These Indices

1. Go to the Firebase Console
2. Navigate to Firestore Database
3. Click on the "Indexes" tab
4. Click "Create Index"
5. Select "documents" as the collection
6. Add the fields as specified above with their sort orders
7. Click "Create"

## Why These Indices Are Needed

The current document queries in `fetchDocsTransFromDb()` filter by:
- User access fields (userId, tcId, managerId, etc.)
- transactionId
- Order by updatedAt

With the new archived label feature, we're adding:
- Filtering by `archived` status
- Grouping by `archivedLabel` field
- Maintaining the same ordering by `updatedAt`

These composite indices ensure efficient querying when:
1. Fetching all documents for a transaction (existing functionality)
2. Filtering archived vs non-archived documents (existing functionality)
3. Grouping archived documents by their labels (new functionality)
4. Maintaining proper sort order within each group (new functionality)

## Alternative Approach

If you prefer to minimize the number of indices, you could use a single-field index on `archivedLabel` and handle the grouping entirely in the client code. However, the composite indices above will provide better performance for large document collections.
