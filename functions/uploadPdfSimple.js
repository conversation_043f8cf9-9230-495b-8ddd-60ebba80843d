const functions = require("firebase-functions");
const axios = require("axios");
const { getStorage } = require("firebase-admin/storage");
const { getFirestore } = require("firebase-admin/firestore");

const db = getFirestore();

// Simplified version that avoids signed URL authentication issues
exports.uploadPdfFromUrlSimple = functions.https.onCall(async (data, context) => {
  try {
    // Validate user authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const { url, filename, transactionId, userId } = data;

    // Validate required parameters
    if (!url || !filename || !transactionId || !userId) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }

    // Validate URL format
    if (!url.includes('ctmecontracts.com')) {
      throw new functions.https.HttpsError('invalid-argument', 'Only CTMecontracts.com URLs are supported');
    }

    console.log("Downloading PDF from URL:", url);

    // Download the PDF
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.data || response.status !== 200) {
      throw new functions.https.HttpsError('internal', 'Failed to download PDF');
    }

    // Generate document ID and storage path
    const docId = db.collection("documents").doc().id;
    const cleanFilename = filename.replace(/[^\w\s\-_.()]/g, '').trim();
    const docPath = `users/${userId}/${transactionId}/${docId}/${cleanFilename}`;

    // Upload to Firebase Storage
    const bucket = getStorage().bucket();
    const file = bucket.file(docPath);
    
    await file.save(Buffer.from(response.data), {
      metadata: {
        contentType: 'application/pdf',
        metadata: {
          originalUrl: url,
          uploadedAt: new Date().toISOString(),
          uploadedBy: context.auth.uid
        }
      }
    });

    console.log("PDF uploaded successfully:", docPath);

    return {
      success: true,
      docId: docId,
      docPath: docPath,
      filename: cleanFilename,
      fileSize: response.data.byteLength
    };

  } catch (error) {
    console.error("Error uploading PDF:", error);
    
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    // Handle specific error types
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new functions.https.HttpsError('unavailable', 'Unable to connect to PDF source');
    } else if (error.code === 'ETIMEDOUT') {
      throw new functions.https.HttpsError('deadline-exceeded', 'Request timed out');
    } else if (error.response && error.response.status === 404) {
      throw new functions.https.HttpsError('not-found', 'PDF not found at URL');
    } else if (error.response && error.response.status === 403) {
      throw new functions.https.HttpsError('permission-denied', 'Access denied to PDF URL');
    }
    
    throw new functions.https.HttpsError('internal', 'Failed to upload PDF');
  }
});
