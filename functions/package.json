{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"axios": "^1.3.4", "axios-cookiejar-support": "^4.0.5", "date-fns": "^3.6.0", "firebase-admin": "^11.5.0", "firebase-functions": "^4.2.1", "fix-esm": "^1.0.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "pdf2json": "^2.0.2", "tough-cookie": "^4.1.2", "xml2js": "^0.4.23"}, "devDependencies": {"eslint": "^7.6.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^0.2.0"}, "private": true}