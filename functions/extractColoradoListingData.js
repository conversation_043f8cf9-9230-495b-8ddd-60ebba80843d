function extractColoradoListingData(pdfTextRaw) {
  const text = pdfTextRaw
    .replace(/\r/g, "")
    .replace(/\u00a0/g, " ")
    .replace(/[ ]{2,}/g, " ");

  const matchOne = (regex) => {
    const m = text.match(regex);
    return m && m[1] ? m[1].trim() : null;
  };

  const seller_3_1 = matchOne(/3\.1\.\s*Seller:\s*(.+)/i);
  const county_3_4 = matchOne(/County of\s+([A-Za-z\s]+?),\s*[\n ]*Colorado:/i);
  const legalDescription_3_4 = matchOne(
    /Colorado:\s*([\s\S]*?)\s*known as No\./i
  );
  const knownAsNo_3_4 = matchOne(
    /known as No\.\s*([^\n,]+(?:,?\s*[A-Za-z0-9#. ]+)*)\s*,/i
  );
  const listingBegins_3_7 = matchOne(
    /3\.7\.\s*Listing Period[\s\S]*?begins on\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})\s*,/i
  );
  const listingEnds_3_7 = matchOne(
    /3\.7\.\s*Listing Period[\s\S]*?\(2\)\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})\s*,/i
  );

  const signerSellers = [];
  const sellerRegex = /Seller:\s*([^\n]+)/gi;
  let m;
  while ((m = sellerRegex.exec(text)) !== null) {
    const name = m[1].trim();
    if (name && !signerSellers.includes(name)) {
      signerSellers.push(name);
    }
  }

  return {
    seller_3_1,
    county_3_4,
    legalDescription_3_4,
    knownAsNo_3_4,
    listingBegins_3_7,
    listingEnds_3_7,
    signerSellers,
  };
}

module.exports = {
  extractColoradoListingData,
};