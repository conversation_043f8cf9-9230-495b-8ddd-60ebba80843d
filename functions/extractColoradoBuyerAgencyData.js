function extractColoradoBuyerAgencyData(pdfTextRaw) {
  // Normalize text a bit for more reliable regex
  const text = pdfTextRaw
    .replace(/\r/g, "")
    .replace(/\u00a0/g, " ") // non-breaking spaces
    .replace(/[ ]{2,}/g, " "); // collapse extra spaces

  // Helper: safe regex match
  const matchOne = (regex) => {
    const m = text.match(regex);
    return m && m[1] ? m[1].trim() : null;
  };

  // 3.1 Buyer: <PERSON><PERSON>
  // 3.1 Buyer: <PERSON> and <PERSON>. Vessels
  const buyer_3_1 = matchOne(/3\.1\.\s*Buyer:\s*(.+)/i);

  // 3.4 Property. Property means real estate which substantially meets...
  //   1015 E. Stollsteimer or 244 Carols Curves
  //   Real Estate in Archuleta County
  //
  // Capture text from after "3.4.  Property." line down to just before "3.5."
  const propertyDescription_3_4 = matchOne(
    /3\.4\.\s*Property\.[\s\S]*?acceptable to Buyer:\s*([\s\S]*?)\s*3\.5\./i
  );

  // 3.6 Listing Period. begins on 9/21/2025 ... or (2) 12/31/2025
  const listingBegins_3_6 = matchOne(
    /3\.6\.\s*Listing Period[\s\S]*?begins on\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})/i
  );

  const listingEnds_3_6 = matchOne(
    /3\.6\.\s*Listing Period[\s\S]*?\(2\)\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})/i
  );

  // Buyer signers at the end:
  //   Buyer:
  //   Date: 10/21/2025
  //    Buyer: Leonard W. Vessels
  //   Date: 10/21/2025
  //    Buyer: Kimberly S. Vessels
  //
  // This will also pick up the 3.1 line, but that's okay — we de-duplicate.
  const buyerSigners = [];
  const buyerRegex = /Buyer:\s*([^\n]+)/gi;
  let m;
  while ((m = buyerRegex.exec(text)) !== null) {
    const name = m[1].trim();
    if (name && !buyerSigners.includes(name)) {
      buyerSigners.push(name);
    }
  }

  return {
    buyer_3_1,               // raw Buyer line (individual(s) or entity/trust/LLC)
    propertyDescription_3_4,  // any criteria text in §3.4
    listingBegins_3_6,        // start date of the agreement
    listingEnds_3_6,          // end date of the agreement
    buyerSigners,             // array of Buyer signature names at the bottom
  };
}

module.exports = {
  extractColoradoBuyerAgencyData,
};