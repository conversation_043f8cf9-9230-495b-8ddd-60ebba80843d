# CTMecontracts.com PDF Upload Feature

This feature allows agents to upload PDFs directly from CTMecontracts.com email links to their transactions in TransActioner.

## Overview

The system can handle two types of CTMecontracts.com links:
1. **SafeLinks URLs** - These are the protected links that come through Outlook emails
2. **Direct CTMecontracts.com URLs** - Direct links to the PDF files

## How It Works

### Frontend Components
- **DocUrlUpload.jsx** - Main component for URL-based PDF upload
- **DocUrlUploadModal.jsx** - Modal wrapper for the upload component
- **DocUploadModal.jsx** - Updated to include tabs for both file and URL upload

### Backend Components
- **uploadPdfFromUrl** - Firebase Cloud Function that downloads PDFs from external URLs
- **URL parsing utilities** - Functions to parse and validate CTMecontracts.com links

### Key Features
- **URL Validation** - Validates that the provided URL is from CTMecontracts.com
- **SafeLinks Decoding** - Automatically decodes Outlook SafeLinks URLs
- **Filename Extraction** - Extracts meaningful filenames from URL parameters
- **Error Handling** - Comprehensive error handling with user-friendly messages
- **Integration** - Seamlessly integrates with existing document management system

## Usage Instructions

### For End Users
1. Navigate to a transaction in TransActioner
2. Click "Add Documents" 
3. Select the "From Email Link" tab
4. Paste the CTMecontracts.com email link into the text area
5. Click "Upload PDF"
6. The system will automatically download and add the PDF to the transaction

### Supported URL Formats

#### SafeLinks URL Example:
```
https://nam12.safelinks.protection.outlook.com/?url=https%3A%2F%2Fwww.ctmecontracts.com%2Ffiles%2FPDFConvert%2F7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf%3Ffn%3D2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf%26dwn%3D1%26ST%3D&data=...
```

#### Direct URL Example:
```
https://www.ctmecontracts.com/files/PDFConvert/7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf?fn=2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf&dwn=1&ST=
```

## Technical Implementation

### URL Parsing Process
1. **Detection** - Check if URL contains SafeLinks or is direct CTMecontracts.com link
2. **Decoding** - For SafeLinks, extract and decode the embedded URL
3. **Validation** - Verify the URL points to ctmecontracts.com domain
4. **Filename Extraction** - Parse the `fn` parameter to get the document name
5. **Sanitization** - Clean the filename of special characters

### Cloud Function Process
1. **Authentication** - Verify user is authenticated
2. **Validation** - Validate input parameters and URL format
3. **Download** - Download PDF from external URL with timeout protection
4. **Upload** - Upload to Firebase Storage with proper metadata
5. **Database** - Create document record in Firestore
6. **Response** - Return success/error status with details

### Error Handling
- **Network Errors** - Timeout, connection refused, DNS issues
- **HTTP Errors** - 404 not found, 403 forbidden, etc.
- **Validation Errors** - Invalid URL format, missing parameters
- **Authentication Errors** - User not logged in, permission denied
- **File Errors** - Invalid PDF, file too large, etc.

## Security Considerations

- Only CTMecontracts.com URLs are allowed
- User authentication is required
- File size limits are enforced
- Content type validation for PDFs
- Proper error messages without exposing system details

## Testing

Run the test suite:
```bash
npm test -- ctmecontracts.test.js
```

The tests cover:
- URL validation for various formats
- SafeLinks URL parsing
- Direct URL parsing
- Filename extraction and sanitization
- Error handling for invalid inputs

## Future Enhancements

- Support for additional document sources
- Batch URL processing
- Progress indicators for large files
- Retry mechanisms for failed downloads
- Enhanced filename parsing for different document types
