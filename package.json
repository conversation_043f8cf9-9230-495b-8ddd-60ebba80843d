{"name": "transactioner", "version": "0.1.0", "private": true, "dependencies": {"@cantoo/pdf-lib": "^1.20.2", "@pdf-lib/fontkit": "^1.1.1", "@reduxjs/toolkit": "^1.9.1", "@sentry/react": "^7.36.0", "@sentry/tracing": "^7.36.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^1.7.2", "axios-cookiejar-support": "^6.0.3", "connected-react-router": "^6.9.3", "cuid": "^2.1.8", "date-fns": "^2.29.1", "downloadjs": "^1.4.7", "firebase": "^9.15.0", "firebase-admin": "^11.5.0", "firebase-functions": "^4.2.1", "formik": "^2.2.0", "google-map-react": "^2.2.0", "history": "^4.10.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-big-calendar": "^1.5.0", "react-cropper": "^2.1.8", "react-datepicker": "^3.3.0", "react-dom": "^16.14.0", "react-draggable": "^4.4.5", "react-dropzone": "^14.2.2", "react-ga": "^3.3.0", "react-ga4": "^2.0.0", "react-helmet": "^6.1.0", "react-infinite-scroller": "^1.2.4", "react-pdf": "^5.7.2", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.1", "react-responsive": "^9.0.2", "react-rnd": "^10.4.1", "react-router-dom": "^6.3.0", "react-scripts": "3.4.3", "react-toastify": "^6.0.9", "react-widgets": "^4.6.1", "redux-persist": "^6.0.0", "semantic-ui-css": "^2.4.1", "semantic-ui-react": "^2.0.0", "tough-cookie": "^5.1.2", "uuidv4": "^6.2.13", "xml2js": "^0.6.2", "yup": "^0.29.3"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "start-liz": "react-scripts start", "start-ssl": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "deploy": "npm run build && firebase deploy", "start:staging": "env-cmd -f .env.staging react-scripts --openssl-legacy-provider start", "start:production": "env-cmd -f .env.production react-scripts start", "cypress:open": "cypress open"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cypress": "^12.5.1", "env-cmd": "^10.1.0", "eslint-plugin-cypress": "^2.12.1", "redux-devtools-extension": "^2.13.8"}}