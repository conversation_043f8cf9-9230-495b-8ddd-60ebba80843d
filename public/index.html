<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <!-- <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" /> -->
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <script src="https://maps.googleapis.com/maps/api/js?key=%REACT_APP_MAPS_KEY%&libraries=places&callback=Function.prototype"></script>
    <title>TransActioner: Modern transaction management for real estate agents</title>
    <meta
    name="description"
    content="Modern transaction management for real estate agents"
    data-react-helmet="true"
    />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="TransActioner: Modern transaction management for Colorado real estate agents" />
    <meta property="og:description" content="Modern transaction management for real estate agents" />
    <meta property="og:url" content="https://transactioner.com/" />
    <meta property="og:site_name" content="TransActioner: Modern transaction management for real estate agents" />
    <meta property="og:image:width" content="400" />
    <meta property="og:image:height" content="267" />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="TransActioner: Modern transaction management for real estate agents" />
    <meta name="twitter:description" content="Modern transaction management for real estate agents" />
    <meta name=”twitter:url” content=”https://transactioner.com” />
    <meta name="twitter:creator" content="@johnboese" />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
