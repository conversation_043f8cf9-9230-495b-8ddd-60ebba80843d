# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development
.env.staging
.env.test.local
.env.production

ui-debug.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
firestore-debug.log*
firebase-debug.log*
/src/firebase-debug.log*
/src/firestore-debug.log*
/src/ui-debug.log*
