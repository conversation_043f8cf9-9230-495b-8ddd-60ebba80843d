# Rename Validation Implementation Summary

## Overview
Added validation to all rename functionality in the application to ensure that users can only enter alphanumeric characters and underscores when renaming items.

## Changes Made

### 1. Utility Functions Added (`src/app/common/util/util.js`)
- `isValid<PERSON>ame(name)`: Validates that a name contains only letters, numbers, underscores, parentheses, dashes, and spaces
- `sanitize<PERSON><PERSON>(name)`: Removes invalid characters from a name and normalizes multiple spaces to single spaces
- `createNameValidation(fieldName)`: Creates a Yup validation schema for names (for future use)

### 2. Updated Rename Components
All rename modal components have been updated with validation:

#### Document Rename (`src/features/docs/docComponents/DocRenameModal.jsx`)
- Added validation state and error handling
- Shows error message for invalid characters
- Trims whitespace before validation and saving

#### Task Template Rename (`src/features/tasks/taskTemplates/taskTemplatesComponents/TaskTemplatesRename.jsx`)
- Added validation state and error handling
- Shows error message for invalid characters
- Trims whitespace before validation and saving

#### Deadline Template Rename (`src/features/deadlineTemplates/deadlineTemplatesComponents/DeadlineTemplatesRename.jsx`)
- Added validation state and error handling
- Shows error message for invalid characters
- Trims whitespace before validation and saving

#### Email Template Rename (`src/features/emailTemplates/emailTemplatesComponents/EmailTemplatesRename.jsx`)
- Added validation state and error handling
- Shows error message for invalid characters
- Trims whitespace before validation and saving

#### Form Template Rename (`src/features/formTemplates/formTemplatesComponents/FormTemplateRenameModal.jsx`)
- Added validation state and error handling
- Shows error message for invalid characters
- Trims whitespace before validation and saving

### 3. Validation Rules
- **Allowed characters**: Letters (a-z, A-Z), numbers (0-9), underscores (_), parentheses (), dashes (-), and spaces ( )
- **Not allowed**: Special characters (!@#$%^&*+={}[]|\\:";'<>?,./)
- **Empty names**: Not allowed
- **Whitespace**: Automatically trimmed, multiple consecutive spaces normalized to single spaces

### 4. User Experience
- Real-time validation feedback
- Error messages clear when user starts typing
- Visual indication (red border) when input is invalid
- Clear error message: "Name can only contain letters, numbers, underscores, parentheses, dashes, and spaces"

### 5. Testing
- Created comprehensive test suite (`src/app/common/util/__tests__/nameValidation.test.js`)
- Tests cover valid names, invalid characters, edge cases, and sanitization
- All tests passing

## Usage Examples

### Valid Names
- `MyDocument`
- `Task_Template_1`
- `Email123`
- `_private_template`
- `Template_2024`
- `Document(Copy)`
- `Task(Version_2)`
- `(Important)_File`
- `Task-Template`
- `Email-Template-123`
- `Document-(Copy)`
- `My Document`
- `Task Template 123`
- `Document (Copy)`

### Invalid Names (will show error)
- `Email@Template` (contains special character)
- `Template.docx` (contains period)
- `Task#Template` (contains hash)
- `Task/Template` (contains slash)
- `` (empty string)

### Automatic Normalization
- `My  Document` → `My Document` (multiple spaces become single)
- `Task   Template` → `Task Template` (tabs/newlines become single space)
- `Document	(Copy)` → `Document (Copy)` (tab becomes space)

## Technical Implementation
- Uses regex pattern: `/^[a-zA-Z0-9_ ()-]+$/`
- Multiple spaces normalized in real-time as user types
- Validation occurs on form submission
- Error state clears when user starts typing
- Input field shows red border when invalid
- Error message displayed below input field

## Future Enhancements
- The `createNameValidation()` function is available for use in Formik forms if needed
- The `sanitizeName()` function can be used to automatically clean user input
- Validation can be extended to other name fields throughout the application
