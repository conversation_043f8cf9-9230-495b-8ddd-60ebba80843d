import React, { useState } from "react";
import { Input, Message, But<PERSON>, Segment, Header } from "semantic-ui-react";
import { isValidName } from "../../app/common/util/util";

/**
 * Demo component to show how the rename validation works
 * This is for demonstration purposes only
 */
export default function RenameValidationDemo() {
  const [textInput, setTextInput] = useState("");
  const [validationError, setValidationError] = useState("");
  const [submittedName, setSubmittedName] = useState("");

  function handleTextInputChange(event) {
    const value = event.target.value;
    setTextInput(value);
    
    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError("");
    }
  }

  function handleSubmit() {
    // Validate the input
    if (!textInput.trim()) {
      setValidationError("Name cannot be empty");
      return;
    }
    
    if (!isValidName(textInput.trim())) {
      setValidationError("Name can only contain letters, numbers, underscores, parentheses, dashes, and spaces");
      return;
    }

    // If validation passes
    setSubmittedName(textInput.trim());
    setTextInput("");
    setValidationError("");
  }

  return (
    <Segment>
      <Header size="medium" color="blue">
        Rename Validation Demo
      </Header>
      <p>Try entering different names to see the validation in action:</p>
      
      <div style={{ marginBottom: "10px" }}>
        <strong>Valid examples:</strong> MyDocument, Task_Template_1, Email123, _private_template, Document(Copy), Task-Template, My Document
      </div>
      <div style={{ marginBottom: "20px" }}>
        <strong>Invalid examples:</strong> Email@Template, Template.docx, My  Document (multiple spaces become single)
      </div>

      <Input
        fluid
        placeholder="Enter a name to test validation..."
        value={textInput}
        onChange={handleTextInputChange}
        error={!!validationError}
        style={{ marginBottom: "10px" }}
      />
      
      {validationError && (
        <Message negative size="small" style={{ marginBottom: "10px" }}>
          {validationError}
        </Message>
      )}
      
      <Button 
        primary 
        onClick={handleSubmit}
        content="Test Validation"
      />
      
      {submittedName && (
        <Message positive style={{ marginTop: "10px" }}>
          <strong>Success!</strong> Valid name submitted: "{submittedName}"
        </Message>
      )}
    </Segment>
  );
}
