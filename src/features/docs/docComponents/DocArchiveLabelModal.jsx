import React, { useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Header,
  Input,
  Segment,
  Message,
} from "semantic-ui-react";

import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { updateDocInDb } from "../../../app/firestore/firestoreService";
import { isValidName } from "../../../app/common/util/util";
import { toast } from "react-toastify";

export default function DocArchiveLabelModal({ doc }) {
  const dispatch = useDispatch();
  const [textInput, setTextInput] = useState("");
  const [validationError, setValidationError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef(null);

  function handleTextInputChange(event) {
    let value = event.target.value;
    // Replace multiple consecutive spaces with single space
    value = value.replace(/\s+/g, ' ');
    setTextInput(value);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError("");
    }
  }

  async function handleSubmit() {
    // Validate the input
    if (!textInput.trim()) {
      setValidationError("Archive label cannot be empty");
      return;
    }

    if (!isValidName(textInput.trim())) {
      setValidationError("Label can only contain letters, numbers, underscores, parentheses, dashes, and spaces");
      return;
    }

    setIsSubmitting(true);
    try {
      await updateDocInDb(doc.id, { 
        archived: true,
        archivedLabel: textInput.trim()
      });
      toast.success("Document successfully archived with label");
      dispatch(
        closeModal({
          modalType: "DocArchiveLabelModal",
        })
      );
    } catch (error) {
      toast.error(error.message);
      setValidationError("Failed to archive document. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }

  async function handleArchiveWithoutLabel() {
    setIsSubmitting(true);
    try {
      await updateDocInDb(doc.id, { 
        archived: true
      });
      toast.success("Document successfully archived");
      dispatch(
        closeModal({
          modalType: "DocArchiveLabelModal",
        })
      );
    } catch (error) {
      toast.error(error.message);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <ModalWrapper size="small">
        <Segment>
          <Grid>
            <Grid.Column>
              <Header size="large" color="blue">
                Archive Document
              </Header>
              <Divider />
              <p>
                You can optionally add a label to organize this archived document.
                Documents with the same label will be grouped together in the archived section.
              </p>
              <Input
                fluid
                ref={inputRef}
                placeholder="Enter archive label (optional)"
                value={textInput}
                onChange={handleTextInputChange}
                error={!!validationError}
                disabled={isSubmitting}
              />
              {validationError && (
                <Message negative size="small">
                  {validationError}
                </Message>
              )}
              <Divider />
              <Grid>
                <Grid.Column width={8}>
                  <Button
                    fluid
                    color="blue"
                    onClick={handleSubmit}
                    disabled={isSubmitting || !textInput.trim()}
                    loading={isSubmitting}
                  >
                    Archive with Label
                  </Button>
                </Grid.Column>
                <Grid.Column width={8}>
                  <Button
                    fluid
                    onClick={handleArchiveWithoutLabel}
                    disabled={isSubmitting}
                    loading={isSubmitting}
                  >
                    Archive without Label
                  </Button>
                </Grid.Column>
              </Grid>
            </Grid.Column>
          </Grid>
        </Segment>
      </ModalWrapper>
    </>
  );
}
