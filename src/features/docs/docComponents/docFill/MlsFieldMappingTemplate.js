/**
 * MLS Field Mapping Template
 * 
 * This file provides a template structure for mapping MLS data fields to PDF form field names.
 * Each form can have its own specific mapping configuration.
 * 
 * To add a new form mapping:
 * 1. Add a new entry to the formMappings object below
 * 2. Use the exact form title as the key
 * 3. Map each MLS field to the corresponding PDF field name from the form's field data
 * 
 * MLS Fields Available:
 * - inclusions: Property inclusions (appliances, fixtures, etc.)
 * - exclusions: Property exclusions 
 * - metroDistrictWebsite: Metro district website URL
 * - emHolder: Earnest money holder
 * - titleCo: Title company
 * - emDepositAmount: Earnest money deposit amount
 * - associationFee: HOA/Association fee amount
 * - associationFeeFreq: HOA/Association fee frequency
 * - ownerName: Property owner name
 * 
 * How to find PDF field names:
 * 1. Look in src/features/docs/docComponents/docFill/formFieldData/[state]/[formname].js
 * 2. Find the field with the "name" property that matches what you want to populate
 * 3. Use that exact "name" value in the mapping below
 */

export const formMappings = {
  
  // TEMPLATE - Copy this structure for new forms
  "TEMPLATE_FORM_NAME": {
    // Address
    address: "PDF_FIELD_NAME_FOR_ADDRESS",
    // Property Details
    county: "PDF_FIELD_NAME_FOR_COUNTY",
    subdivisionName: "PDF_FIELD_NAME_FOR_SUBDIVISION",
    yearBuilt: "PDF_FIELD_NAME_FOR_YEAR_BUILT",
    parcelNumber: "PDF_FIELD_NAME_FOR_PARCEL_NUMBER",
    legalDescription: "PDF_FIELD_NAME_FOR_LEGAL_DESCRIPTION",
    inclusions: "PDF_FIELD_NAME_FOR_INCLUSIONS",
    exclusions: "PDF_FIELD_NAME_FOR_EXCLUSIONS",
    metroDistrictWebsite: "PDF_FIELD_NAME_FOR_METRO_DISTRICT",
    emHolder: "PDF_FIELD_NAME_FOR_EM_HOLDER",
    // Custom Fields
    titleCo: "PDF_FIELD_NAME_FOR_TITLE_COMPANY",
    emDepositAmount: "PDF_FIELD_NAME_FOR_EM_AMOUNT",
    associationFee: "PDF_FIELD_NAME_FOR_ASSOCIATION_FEE",
    associationFeeFreq: "PDF_FIELD_NAME_FOR_ASSOCIATION_FREQ",
    ownerName: "PDF_FIELD_NAME_FOR_OWNER_NAME",
    // Listing Agent Fields
    listingAgentName: "PDF_FIELD_NAME_FOR_LISTING_AGENT_NAME",
    listingAgentFirstName: "PDF_FIELD_NAME_FOR_LISTING_AGENT_FIRST_NAME",
    listingAgentLastName: "PDF_FIELD_NAME_FOR_LISTING_AGENT_LAST_NAME",
    listingAgentEmail: "PDF_FIELD_NAME_FOR_LISTING_AGENT_EMAIL",
    listingAgentPhone: "PDF_FIELD_NAME_FOR_LISTING_AGENT_PHONE",
    listingAgentLicense: "PDF_FIELD_NAME_FOR_LISTING_AGENT_LICENSE",
    listingAgentBrokerage: "PDF_FIELD_NAME_FOR_LISTING_AGENT_BROKERAGE",
  },

  // Colorado Contract to Buy and Sell Residential 2024
  "Contract to Buy and Sell, Residential 2024": {
    // Address
    address: "known as", 
    // Property Details
    county: "Property The Property is the following legally described real estate in the County of", 
    // subdivisionName: "Subdivision", // Update with actual PDF field name
    // yearBuilt: "Year Built", // Update with actual PDF field name
    // parcelNumber: "Parcel Number", // Update with actual PDF field name
    legalDescription: "Text1", 
    inclusions: "Text2", 
    exclusions: "Text6", 
    metroDistrictWebsite: "undefined_9", 
    emHolder: "Earnest Money Holder", 
    // Custom Fields
    titleCo: "Title Company", // Update with actual PDF field name
    emDepositAmount: "Earnest Money", 
    // associationFee: "Association Fee", // Update with actual PDF field name
    // associationFeeFreq: "Association Fee Frequency", // Update with actual PDF field name
    ownerName: "Seller", // Update with actual PDF field name
    // Listing Agent Fields
    listingAgentName: "1_5", // Full name
    // listingAgentFirstName: "1_5", // First name only
    // listingAgentLastName: "1_5", // Last name only
    listingAgentEmail: "5",
    listingAgentPhone: "3_2",
    listingAgentLicense: "2_6",
    listingAgentBrokerage: "Brokerage Firms compensation or commission is to be paid by",
    listingAgentLicenseNumber: "2_6",
    listingAgentAddressStreet: "1_6",
    listingAgentAddressCityStateZip: "2_7",
    colistingAgent: "1_7",
  },

  // Colorado Contract to Buy and Sell Residential (older version)
  "Contract to Buy and Sell, Residential": {
    inclusions: "Inclusions", // Update with actual PDF field name
    exclusions: "Exclusions", // Update with actual PDF field name
    metroDistrictWebsite: "undefined_9", 
    emHolder: "Earnest Money Holder", // Update with actual PDF field name
    titleCo: "Title Company", // Update with actual PDF field name
    emDepositAmount: "Earnest Money Amount", // Update with actual PDF field name
    associationFee: "Association Fee", // Update with actual PDF field name
    associationFeeFreq: "Association Fee Frequency", // Update with actual PDF field name
    ownerName: "Seller", // Update with actual PDF field name
  },

  // Colorado Contract to Buy and Sell Residential 2023
  "Contract to Buy and Sell, Residential 2023": {
    inclusions: "Inclusions", // Update with actual PDF field name
    exclusions: "Exclusions", // Update with actual PDF field name
    metroDistrictWebsite: "Metro District URL", // Update with actual PDF field name
    emHolder: "Earnest Money Holder", // Update with actual PDF field name
    titleCo: "Title Company", // Update with actual PDF field name
    emDepositAmount: "Earnest Money Amount", // Update with actual PDF field name
    associationFee: "Association Fee", // Update with actual PDF field name
    associationFeeFreq: "Association Fee Frequency", // Update with actual PDF field name
    ownerName: "Seller", // Update with actual PDF field name
  },

  // Colorado Contract to Buy and Sell Income-Residential
  "Contract to Buy and Sell, Income-Residential": {
    inclusions: "Inclusions", // Update with actual PDF field name
    exclusions: "Exclusions", // Update with actual PDF field name
    metroDistrictWebsite: "Metro District URL", // Update with actual PDF field name
    emHolder: "Earnest Money Holder", // Update with actual PDF field name
    titleCo: "Title Company", // Update with actual PDF field name
    emDepositAmount: "Earnest Money Amount", // Update with actual PDF field name
    associationFee: "Association Fee", // Update with actual PDF field name
    associationFeeFreq: "Association Fee Frequency", // Update with actual PDF field name
    ownerName: "Seller", // Update with actual PDF field name
  },

  // Colorado Contract to Buy and Sell Land
  "Contract to Buy and Sell, Land": {
    inclusions: "Inclusions", // Update with actual PDF field name
    exclusions: "Exclusions", // Update with actual PDF field name
    metroDistrictWebsite: "Metro District URL", // Update with actual PDF field name
    emHolder: "Earnest Money Holder", // Update with actual PDF field name
    titleCo: "Title Company", // Update with actual PDF field name
    emDepositAmount: "Earnest Money Amount", // Update with actual PDF field name
    associationFee: "Association Fee", // Update with actual PDF field name
    associationFeeFreq: "Association Fee Frequency", // Update with actual PDF field name
    ownerName: "Seller", // Update with actual PDF field name
  },

  // Add more forms as needed...
  // 
  // "Another Form Name": {
  //   inclusions: "Actual_PDF_Field_Name_For_Inclusions",
  //   exclusions: "Actual_PDF_Field_Name_For_Exclusions",
  //   // ... other mappings
  // },

};

/**
 * Get the field mapping for a specific form
 * @param {string} formTitle - The title of the form
 * @returns {object|null} Field mapping object or null if not found
 */
export function getFormMapping(formTitle) {
  return formMappings[formTitle] || null;
}

/**
 * Get all available form mappings
 * @returns {object} All form mappings
 */
export function getAllFormMappings() {
  return formMappings;
}

/**
 * Check if a form has MLS import mapping configured
 * @param {string} formTitle - The title of the form
 * @returns {boolean} Whether the form has mapping configured
 */
export function hasFormMapping(formTitle) {
  return formMappings.hasOwnProperty(formTitle);
}

/**
 * Get available MLS fields that can be mapped
 * @returns {array} Array of MLS field objects with metadata
 */
export function getAvailableMlsFields() {
  return [
    {
      key: 'inclusions',
      label: 'Inclusions',
      description: 'Property inclusions (appliances, fixtures, etc.)',
      category: 'Property Details'
    },
    {
      key: 'exclusions',
      label: 'Exclusions',
      description: 'Property exclusions',
      category: 'Property Details'
    },
    {
      key: 'metroDistrictWebsite',
      label: 'Metro District Website',
      description: 'Metro district website URL',
      category: 'Property Details'
    },
    {
      key: 'emHolder',
      label: 'Earnest Money Holder',
      description: 'Entity holding earnest money',
      category: 'Transaction Details'
    },
    {
      key: 'titleCo',
      label: 'Title Company',
      description: 'Title company name',
      category: 'Transaction Details'
    },
    {
      key: 'emDepositAmount',
      label: 'EM Deposit Amount',
      description: 'Earnest money deposit amount',
      category: 'Transaction Details'
    },
    {
      key: 'associationFee',
      label: 'Association Fee',
      description: 'HOA/Association fee amount',
      category: 'Property Details'
    },
    {
      key: 'associationFeeFreq',
      label: 'Association Fee Frequency',
      description: 'HOA/Association fee frequency (monthly, annually, etc.)',
      category: 'Property Details'
    },
    {
      key: 'ownerName',
      label: 'Owner Name',
      description: 'Property owner name',
      category: 'Property Details'
    }
  ];
}

/**
 * Instructions for adding new form mappings
 */
export const MAPPING_INSTRUCTIONS = `
To add MLS import support for a new form:

1. Find the form's field data file:
   - Navigate to src/features/docs/docComponents/docFill/formFieldData/[state]/
   - Find the file for your form (e.g., contracttoBuyandSellResidential2024.js)

2. Identify the PDF field names:
   - Look for objects with "name" properties
   - These are the exact field names you need to use in the mapping

3. Add the mapping to this file:
   - Copy the TEMPLATE_FORM_NAME structure above
   - Replace "TEMPLATE_FORM_NAME" with the exact form title
   - Replace each "PDF_FIELD_NAME_FOR_*" with the actual PDF field name

4. Test the mapping:
   - The form must have canImportFromListing: true in the database
   - Use the Import MLS button to test the field mapping

Example PDF field from contracttoBuyandSellResidential2024.js:
{
  page: 0,
  name: "Inclusions", 
  type: "text",
  // ... other properties
}

In this case, you would use "Inclusions" as the PDF field name in the mapping.
`;
