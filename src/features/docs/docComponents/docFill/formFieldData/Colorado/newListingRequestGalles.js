export function newListingRequestGalles() {
return [   //2024 Release 2025-11-15 18:38:23
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Listing Broker 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 162.68,
   left: 108.72,
   width: 198.60,
   height: 11.531
}
,
{
   page: 0,
   name: "Listing Broker 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 162.68,
   left: 384.48,
   width: 199.80,
   height: 11.531
}
,
{
   page: 0,
   name: "Listing Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 183.56,
   left: 102.72,
   width: 235.32,
   height: 11.531
}
,
{
   page: 0,
   name: "<PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 183.56,
   left: 366.96,
   width: 97.44,
   height: 11.531
}
,
{
   page: 0,
   name: "<PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 183.56,
   left: 489.12,
   width: 92.28,
   height: 11.531
}
,
{
   page: 0,
   name: "Buyer Broker Compensation", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 204.44,
   left: 159.36,
   width: 55.56,
   height: 11.531
}
,
{
   page: 0,
   name: "Date Listing Term Begins", 
   fontSize: 9,
   type: "date",
   top: 204.44,
   left: 330.48,
   width: 69.72,
   height: 11.531
}
,
{
   page: 0,
   name: "Date Expiration Date", 
   fontSize: 9,
   type: "date",
   top: 204.44,
   left: 472.32,
   width: 111.96,
   height: 11.531
}
,
{
   page: 0,
   name: "Check Box1", 
   isText: false,
   type: "checkbox",
   top: 373.165,
   left: 120.128,
   width: 12.768,
   height: 12.768
}
,
{
   page: 0,
   name: "Check Box2", 
   isText: false,
   type: "checkbox",
   top: 374.474,
   left: 224.856,
   width: 12.767,
   height: 12.768
}
,
{
   page: 0,
   name: "Check Box6", 
   isText: false,
   type: "checkbox",
   top: 373.82,
   left: 331.547,
   width: 12.768,
   height: 12.768
}
,
{
   page: 0,
   name: "Is There A Lockbox", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 395.72,
   left: 124.8,
   width: 83.04,
   height: 11.531
}
,
{
   page: 0,
   name: "Other Notes", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 395.72,
   left: 264.0,
   width: 315.72,
   height: 11.531
}
,
{
   page: 0,
   name: "Showing Contact Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.6,
   left: 139.92,
   width: 190.20,
   height: 11.531
}
,
{
   page: 0,
   name: "Showing Contact Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.24,
   left: 142.08,
   width: 127.56,
   height: 11.531
}
,
{
   page: 0,
   name: "Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.24,
   left: 302.64,
   width: 280.68,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 1 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 458.12,
   left: 104.4,
   width: 285.48,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 1 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 478.76,
   left: 104.64,
   width: 285.36,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 1 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 479.0,
   left: 460.8,
   width: 123.0,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 2 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 499.64,
   left: 104.4,
   width: 285.48,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 2 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 520.28,
   left: 102.72,
   width: 285.72,
   height: 11.531
}
,
{
   page: 0,
   name: "Owner 2 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 520.52,
   left: 461.52,
   width: 122.28,
   height: 11.531
}
,
{
   page: 0,
   name: "Check Box3", 
   isText: false,
   type: "checkbox",
   top: 223.583,
   left: 159.401,
   width: 14.727,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box4", 
   isText: false,
   type: "checkbox",
   top: 224.237,
   left: 323.038,
   width: 14.727,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box5", 
   isText: false,
   type: "checkbox",
   top: 223.583,
   left: 551.475,
   width: 14.727,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box16", 
   isText: false,
   type: "checkbox",
   top: 287.074,
   left: 140.419,
   width: 14.727,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 287.074,
   left: 357.074,
   width: 14.728,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box18", 
   isText: false,
   type: "checkbox",
   top: 287.074,
   left: 542.311,
   width: 14.728,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 309.329,
   left: 146.964,
   width: 14.728,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 308.674,
   left: 357.074,
   width: 14.728,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 309.983,
   left: 541.657,
   width: 14.727,
   height: 13.418
}
,
{
   page: 0,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 413.402,
   left: 368.583,
   width: 14.073,
   height: 14.727
}
,
{
   page: 0,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 413.402,
   left: 426.802,
   width: 14.073,
   height: 14.727
}
,
{
   page: 0,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 413.402,
   left: 485.675,
   width: 14.073,
   height: 14.727
}
,
{
   page: 0,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 455.911,
   left: 496.184,
   width: 14.727,
   height: 14.727
}
,
{
   page: 0,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 455.911,
   left: 544.621,
   width: 14.727,
   height: 14.727
}
,
{
   page: 0,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 496.184,
   left: 496.493,
   width: 15.382,
   height: 15.382
}
,
{
   page: 0,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 496.184,
   left: 545.584,
   width: 15.382,
   height: 15.382
}
,
{
   page: 0,
   name: "Text29", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 581.239,
   left: 37.3092,
   width: 548.6198,
   height: 191.528
}
] }
