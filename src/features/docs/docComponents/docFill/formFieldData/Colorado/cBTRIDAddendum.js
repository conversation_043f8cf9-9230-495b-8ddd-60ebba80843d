export function cBTRIDAddendum() {
    return [   //2023
    // {
    //    page: 0,
    //    name: "Buyer", 
    //    type: "textarea",
    //    fontName: "Helvetica",
    //    fontSize: 10,
    //    top: 495.309,
    //    left: 96.72,
    //    width: 306.36,
    //    height: 19.731
    // }
    // ,
    // {
    //    page: 0,
    //    name: "Date", 
    //    fontSize: 9,
    //    type: "date",
    //    top: 494.301,
    //    left: 432.6,
    //    width: 83.52,
    //    height: 20.739
    // }
    // ,
    {
       page: 0,
       name: "DateField Date Executed", 
       fontSize: 9,
       type: "date",
       top: 463.726,
       left: 101.829,
       width: 83.520,
       height: 12.678
    }
    // ,
    // {
    //    page: 0,
    //    name: "Buyer2 Signature", 
    //    type: "textarea",
    //    fontName: "Helvetica",
    //    fontSize: 10,
    //    top: 535.23,
    //    left: 98.1268,
    //    width: 306.3602,
    //    height: 19.731
    // }
    // ,
    // {
    //    page: 0,
    //    name: "Date Buyer 2 Signed", 
    //    fontSize: 9,
    //    type: "date",
    //    top: 534.223,
    //    left: 434.007,
    //    width: 83.520,
    //    height: 20.738
    // }
    ,
    {
       page: 0,
       name: "Buyer Broker Signature", 
       type: "signature",
       fontSize: 18,
       top: 655.682,
       left: 69.005,
       width: 306.360,
       height: 19.731
    }
    ] }
    