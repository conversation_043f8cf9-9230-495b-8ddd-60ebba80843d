export function sellerAddendum2022FRASCONA() {
return [   //2024 Release 2025-05-07 14:10:11
 
{
   page: 0,
   name: "In the event of a conflict between this Addendum and the indicated CREC approved form this", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 174.36,
   left: 146.04,
   width: 426.36,
   height: 12.12
}
,
{
   page: 0,
   name: "Text8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 77.928,
   left: 191.128,
   width: 235.746,
   height: 12.181
}
,
{
   page: 0,
   name: "Check Box9", 
   isText: false,
   type: "checkbox",
   top: 535.143,
   left: 208.801,
   width: 11.078,
   height: 10.605
}
,
{
   page: 0,
   name: "Check Box10", 
   isText: false,
   type: "checkbox",
   top: 534.766,
   left: 405.82,
   width: 11.079,
   height: 10.605
}
,
{
   page: 0,
   name: "<PERSON> Box11", 
   isText: false,
   type: "checkbox",
   top: 548.475,
   left: 206.183,
   width: 11.078,
   height: 10.605
}
,
{
   page: 1,
   name: "commission indicated in the Seller Listing Contract or if this box is checked", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 174.196,
   left: 144.72,
   width: 34.92,
   height: 10.844
}
,
{
   page: 1,
   name: "Check Box12", 
   isText: false,
   type: "checkbox",
   top: 439.235,
   left: 37.3454,
   width: 11.0182,
   height: 11.455
}
,
{
   page: 1,
   name: "Check Box13", 
   isText: false,
   type: "checkbox",
   top: 494.399,
   left: 37.3454,
   width: 11.0182,
   height: 11.454
}
,
{
   page: 1,
   name: "Check Box14", 
   isText: false,
   type: "checkbox",
   top: 560.471,
   left: 37.3454,
   width: 11.0182,
   height: 11.455
}
,
{
   page: 1,
   name: "Text15", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 77.273,
   left: 183.274,
   width: 250.145,
   height: 14.146
}
,
{
   page: 2,
   name: "any amount owed by Brokerage Firm to the selling cooperating brokers in connection", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 239.967,
   left: 237.24,
   width: 48.96,
   height: 12.153
}
,
{
   page: 2,
   name: "Text16", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 76.273,
   left: 178.037,
   width: 255.382,
   height: 14.800
}
,
{
   page: 2,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 266.747,
   left: 335.474,
   width: 13.418,
   height: 14.072
}
,
{
   page: 3,
   name: "signature Broker", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 509.48,
   left: 85.8,
   width: 195.84,
   height: 13.484
}
,
{
   page: 3,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 509.36,
   left: 315.88,
   width: 121.418,
   height: 13.484
}
,
{
   page: 3,
   name: "Text17", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 456.02,
   left: 140.073,
   width: 179.455,
   height: 14.138
}
,
{
   page: 3,
   name: "Text18", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 482.893,
   left: 58.9093,
   width: 149.9997,
   height: 13.484
}
,
{
   page: 3,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 280.183,
   left: 107.346,
   width: 13.418,
   height: 12.764
}
,
{
   page: 3,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 280.183,
   left: 180.655,
   width: 13.419,
   height: 12.764
}
,
{
   page: 3,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 347.256,
   left: 107.346,
   width: 13.418,
   height: 12.764
}
,
{
   page: 3,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 347.256,
   left: 181.31,
   width: 13.418,
   height: 12.764
}
,
{
   page: 3,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 413.711,
   left: 110.619,
   width: 13.418,
   height: 12.764
}
,
{
   page: 3,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 413.711,
   left: 179.346,
   width: 13.418,
   height: 12.764
}
,
{
   page: 3,
   name: "Text25", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 293.692,
   left: 109.31,
   width: 280.255,
   height: 11.527
}
,
{
   page: 3,
   name: "Text26", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 360.147,
   left: 109.31,
   width: 280.255,
   height: 11.527
}
,
{
   page: 3,
   name: "Text27", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 428.111,
   left: 109.31,
   width: 280.255,
   height: 11.527
}
,
{
   page: 3,
   name: "Text28", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 12,
   top: 76.2,
   left: 180.655,
   width: 261.928,
   height: 14.146
}
] }
