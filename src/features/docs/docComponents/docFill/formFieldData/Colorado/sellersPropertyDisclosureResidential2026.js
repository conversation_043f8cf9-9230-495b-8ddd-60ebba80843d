export function sellersPropertyDisclosureResidential2026() {
return [   // Release 2025-12-10 12:41:25
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date SPD completed by <PERSON><PERSON>", 
   fontSize: 9,
   type: "date",
   top: 527.52,
   left: 178.0,
   width: 165.0,
   height: 11.269
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 545.52,
   left: 90.16,
   width: 475.08,
   height: 11.269
}
,
{
   page: 0,
   name: "<PERSON><PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 563.52,
   left: 79.12,
   width: 489.96,
   height: 11.269
}
,
{
   page: 0,
   name: "Listing Year Built", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 581.52,
   left: 97.72,
   width: 469.92,
   height: 11.269
}
,
{
   page: 0,
   name: "Year Seller Acquired Property", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 599.52,
   left: 177.64,
   width: 390.00,
   height: 11.269
}
,
{
   page: 0,
   name: "date Last Occupied", 
   fontSize: 9,
   type: "date",
   top: 635.52,
   left: 397.96,
   width: 168.12,
   height: 11.269
}
,
{
   page: 0,
   name: "Check Box865", 
   isText: false,
   type: "checkbox",
   top: 652.661,
   left: 375.581,
   width: 13.680,
   height: 13.680
}
,
{
   page: 0,
   name: "Check Box866", 
   isText: false,
   type: "checkbox",
   top: 666.275,
   left: 375.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 0,
   name: "Seller is occupying", 
   isText: false,
   type: "checkbox",
   top: 617.097,
   left: 77.9009,
   width: 13.6800,
   height: 13.680
}
,
{
   page: 0,
   name: "Seller is not occupying", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 113.033,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "CommentsStructural problems with improvements", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 121.08,
   left: 168,
   width: 425.16,
   height: 70.44
}
,
{
   page: 1,
   name: "CommentsStructural supports or reinforcements added", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 193.32,
   left: 168,
   width: 425.16,
   height: 59.16
}
,
{
   page: 1,
   name: "CommentsMoisture andor water including but not limited to leakageseepage in the basementcrawlspace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 254.28,
   left: 168,
   width: 425.16,
   height: 70.68
}
,
{
   page: 1,
   name: "CommentsDamage due to termites other insects birds animals or rodents", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 326.76,
   left: 168,
   width: 425.16,
   height: 58.92
}
,
{
   page: 1,
   name: "CommentsDamage due to hail wind fire flood or other casualty", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 387.48,
   left: 168,
   width: 425.16,
   height: 70.68
}
,
{
   page: 1,
   name: "a Foundations", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 475.8,
   left: 168.0,
   width: 425.16,
   height: 57.24
}
,
{
   page: 1,
   name: "b Floors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 534.36,
   left: 168.0,
   width: 425.16,
   height: 43.56
}
,
{
   page: 1,
   name: "c Interior Walls", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 579.24,
   left: 168.0,
   width: 425.16,
   height: 43.56
}
,
{
   page: 1,
   name: "d Exterior Walls", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 624.12,
   left: 168.0,
   width: 425.16,
   height: 43.32
}
,
{
   page: 1,
   name: "e Driveways", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.76,
   left: 168.0,
   width: 425.16,
   height: 43.56
}
,
{
   page: 1,
   name: "f Sidewalks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 713.64,
   left: 168.0,
   width: 425.16,
   height: 43.56
}
,
{
   page: 1,
   name: "Check Box705", 
   isText: false,
   type: "checkbox",
   top: 125.727,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box706", 
   isText: false,
   type: "checkbox",
   top: 198.601,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box707", 
   isText: false,
   type: "checkbox",
   top: 257.861,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box708", 
   isText: false,
   type: "checkbox",
   top: 330.735,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box709", 
   isText: false,
   type: "checkbox",
   top: 393.198,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box710", 
   isText: false,
   type: "checkbox",
   top: 481.288,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box711", 
   isText: false,
   type: "checkbox",
   top: 537.344,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box712", 
   isText: false,
   type: "checkbox",
   top: 584.592,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box713", 
   isText: false,
   type: "checkbox",
   top: 627.035,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box714", 
   isText: false,
   type: "checkbox",
   top: 670.279,
   left: 148.349,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box715", 
   isText: false,
   type: "checkbox",
   top: 715.1245,
   left: 148.349,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 2,
   name: "Commentsg Patios", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 66.36,
   left: 168,
   width: 425.16,
   height: 47.64
}
,
{
   page: 2,
   name: "Commentsh Retaining Walls", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 115.8,
   left: 168,
   width: 425.16,
   height: 47.64
}
,
{
   page: 2,
   name: "Commentsi Other", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 164.88,
   left: 167.76,
   width: 425.64,
   height: 29.16
}
,
{
   page: 2,
   name: "CommentsWindow leaks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 196.2,
   left: 168,
   width: 425.16,
   height: 56.76
}
,
{
   page: 2,
   name: "CommentsExterior Artificial Stucco EIFS", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 254.4,
   left: 167.76,
   width: 425.64,
   height: 29.16
}
,
{
   page: 2,
   name: "CommentsSubfloors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 285.72,
   left: 168,
   width: 425.16,
   height: 43.08
}
,
{
   page: 2,
   name: "Subfloors10", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 330.24,
   left: 33.12,
   width: 110.28,
   height: 29.16
}
,
{
   page: 2,
   name: "Comments10", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 330.24,
   left: 167.76,
   width: 425.64,
   height: 29.16
}
,
{
   page: 2,
   name: "CommentsIndicate age of roof in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 453.12,
   left: 158.64,
   width: 434.76,
   height: 30.00
}
,
{
   page: 2,
   name: "CommentsIndicate roof material in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 487.08,
   left: 158.64,
   width: 434.76,
   height: 29.88
}
,
{
   page: 2,
   name: "CommentsRoof is under warranty", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 520.92,
   left: 158.64,
   width: 434.76,
   height: 29.88
}
,
{
   page: 2,
   name: "Commentsa Date of warranty expiration", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.76,
   left: 158.64,
   width: 434.76,
   height: 29.88
}
,
{
   page: 2,
   name: "Yesb Warranty is transferable", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 602.385,
   left: 158.64,
   width: 434.76,
   height: 29.880
}
,
{
   page: 2,
   name: "i Yes i No i UnknownRoof work done while under current roof warranty", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 635.52,
   left: 158.88,
   width: 434.28,
   height: 55.20
}
,
{
   page: 2,
   name: "i Yes i No i Unknowna Date work completed", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 693.48,
   left: 158.64,
   width: 434.76,
   height: 29.88
}
,
{
   page: 2,
   name: "a Date work completed5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 725.16,
   left: 33.12,
   width: 101.40,
   height: 28.80
}
,
{
   page: 2,
   name: "i Yes i No i Unknown5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 725.16,
   left: 158.64,
   width: 434.76,
   height: 28.80
}
,
{
   page: 2,
   name: "Check Box716", 
   isText: false,
   type: "checkbox",
   top: 72.073,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box717", 
   isText: false,
   type: "checkbox",
   top: 120.922,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box718", 
   isText: false,
   type: "checkbox",
   top: 168.17,
   left: 148.951,
   width: 13.680,
   height: 13.68
}
,
{
   page: 2,
   name: "Check Box719", 
   isText: false,
   type: "checkbox",
   top: 200.203,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box720", 
   isText: false,
   type: "checkbox",
   top: 260.264,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box721", 
   isText: false,
   type: "checkbox",
   top: 289.093,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box722", 
   isText: false,
   type: "checkbox",
   top: 333.137,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box723", 
   isText: false,
   type: "checkbox",
   top: 454.861,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box724", 
   isText: false,
   type: "checkbox",
   top: 489.296,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box725", 
   isText: false,
   type: "checkbox",
   top: 522.93,
   left: 138.74,
   width: 13.68,
   height: 13.68
}
,
{
   page: 2,
   name: "Check Box726", 
   isText: false,
   type: "checkbox",
   top: 557.365,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box727", 
   isText: false,
   type: "checkbox",
   top: 590.999,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box728", 
   isText: false,
   type: "checkbox",
   top: 589.397,
   left: 163.366,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box729", 
   isText: false,
   type: "checkbox",
   top: 588.596,
   left: 201.004,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box730", 
   isText: false,
   type: "checkbox",
   top: 588.796,
   left: 237.04,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box731", 
   isText: false,
   type: "checkbox",
   top: 639.047,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box732", 
   isText: false,
   type: "checkbox",
   top: 695.905,
   left: 138.74,
   width: 13.68,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box733", 
   isText: false,
   type: "checkbox",
   top: 728.7383,
   left: 138.74,
   width: 13.68,
   height: 13.6800
}
,
{
   page: 3,
   name: "CommentsRoof leak", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 107.52,
   left: 158.88,
   width: 434.28,
   height: 73.68
}
,
{
   page: 3,
   name: "CommentsDamage to roof", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 182.88,
   left: 158.88,
   width: 434.28,
   height: 73.68
}
,
{
   page: 3,
   name: "CommentsDamage to skylight", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 258.24,
   left: 158.88,
   width: 434.28,
   height: 50.64
}
,
{
   page: 3,
   name: "CommentsDamage to gutter or downspout", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 310.56,
   left: 158.88,
   width: 434.28,
   height: 55.20
}
,
{
   page: 3,
   name: "CommentsOther roof problems issues or concerns", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 367.44,
   left: 158.88,
   width: 434.28,
   height: 68.88
}
,
{
   page: 3,
   name: "Other roof problems issues or concerns11", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.76,
   left: 33.12,
   width: 101.40,
   height: 27.84
}
,
{
   page: 3,
   name: "Comments11", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.76,
   left: 158.64,
   width: 434.76,
   height: 27.84
}
,
{
   page: 3,
   name: "Age if knownBuiltin vacuum system  accessories", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.28,
   left: 145.68,
   width: 29.16,
   height: 26.52
}
,
{
   page: 3,
   name: "CommentsBuiltin vacuum system  accessories", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 553.625,
   left: 177.12,
   width: 416.28,
   height: 26.520
}
,
{
   page: 3,
   name: "YesClothes dryer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 594.465,
   left: 177.12,
   width: 416.28,
   height: 40.921
}
,
{
   page: 3,
   name: "Age if knownClothes dryer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 582.48,
   left: 145.8,
   width: 28.92,
   height: 54.24
}
,
{
   page: 3,
   name: "Age if knownClothes washer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 638.4,
   left: 145.8,
   width: 28.92,
   height: 56.4
}
,
{
   page: 3,
   name: "i Gas i ElectricClothes washer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 638.52,
   left: 177.36,
   width: 415.80,
   height: 56.28
}
,
{
   page: 3,
   name: "Age if knownDishwasher", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 696.48,
   left: 145.8,
   width: 28.92,
   height: 56.40
}
,
{
   page: 3,
   name: "i Gas i ElectricDishwasher", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 696.6,
   left: 177.36,
   width: 415.80,
   height: 56.28
}
,
{
   page: 3,
   name: "Check Box734", 
   isText: false,
   type: "checkbox",
   top: 112.114,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box735", 
   isText: false,
   type: "checkbox",
   top: 190.593,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box736", 
   isText: false,
   type: "checkbox",
   top: 261.064,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box737", 
   isText: false,
   type: "checkbox",
   top: 314.719,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box738", 
   isText: false,
   type: "checkbox",
   top: 372.377,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box739", 
   isText: false,
   type: "checkbox",
   top: 441.247,
   left: 139.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box740", 
   isText: false,
   type: "checkbox",
   top: 556.564,
   left: 125.126,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box741", 
   isText: false,
   type: "checkbox",
   top: 586.194,
   left: 125.126,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box742", 
   isText: false,
   type: "checkbox",
   top: 643.052,
   left: 125.126,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box743", 
   isText: false,
   type: "checkbox",
   top: 699.9091,
   left: 125.126,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 3,
   name: "Check Box744", 
   isText: false,
   type: "checkbox",
   top: 580.588,
   left: 179.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box745", 
   isText: false,
   type: "checkbox",
   top: 580.588,
   left: 217.821,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "AgeDisposal", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 61.2,
   left: 145.8,
   width: 28.92,
   height: 40.08
}
,
{
   page: 4,
   name: "CommentsDisposal", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 61.32,
   left: 177.36,
   width: 415.80,
   height: 39.96
}
,
{
   page: 4,
   name: "AgeFreezer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 102.96,
   left: 145.8,
   width: 28.92,
   height: 49.44
}
,
{
   page: 4,
   name: "CommentsFreezer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 103.08,
   left: 177.36,
   width: 415.80,
   height: 49.32
}
,
{
   page: 4,
   name: "AgeGas grill", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 154.08,
   left: 145.8,
   width: 28.92,
   height: 49.44
}
,
{
   page: 4,
   name: "CommentsGas grill", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 154.2,
   left: 177.36,
   width: 415.80,
   height: 49.32
}
,
{
   page: 4,
   name: "AgeRange ventilation system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 205.2,
   left: 145.8,
   width: 28.92,
   height: 49.44
}
,
{
   page: 4,
   name: "CommentsRange ventilation system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 205.32,
   left: 177.36,
   width: 415.80,
   height: 49.32
}
,
{
   page: 4,
   name: "AgeMicrowave oven", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.32,
   left: 145.8,
   width: 28.92,
   height: 52.56
}
,
{
   page: 4,
   name: "Free standing Built in", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 269.604,
   left: 177.36,
   width: 415.80,
   height: 39.276
}
,
{
   page: 4,
   name: "AgeOven", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 310.56,
   left: 145.8,
   width: 28.92,
   height: 55.68
}
,
{
   page: 4,
   name: "Gas Electric Single Double", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 324.535,
   left: 177.36,
   width: 415.80,
   height: 41.705
}
,
{
   page: 4,
   name: "YesRangeStove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 380.978,
   left: 177.36,
   width: 415.80,
   height: 49.342
}
,
{
   page: 4,
   name: "AgeRangeStove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 367.92,
   left: 145.8,
   width: 28.92,
   height: 63.84
}
,
{
   page: 4,
   name: "AgeRefrigerator", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 433.44,
   left: 145.8,
   width: 28.92,
   height: 81.60
}
,
{
   page: 4,
   name: "Gas Electric Free Standing DropInRefrigerator", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 433.56,
   left: 177.36,
   width: 415.80,
   height: 81.48
}
,
{
   page: 4,
   name: "AgeTV antenna", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 516.72,
   left: 145.8,
   width: 28.92,
   height: 60.96
}
,
{
   page: 4,
   name: "Owned Leased If leased provide the name and contact information of entity leased from", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 527.066,
   left: 177.36,
   width: 415.80,
   height: 50.614
}
,
{
   page: 4,
   name: "YesSatellite system or DSS dish", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 589.418,
   left: 177.36,
   width: 415.80,
   height: 49.342
}
,
{
   page: 4,
   name: "AgeSatellite system or DSS dish", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 579.36,
   left: 145.8,
   width: 28.92,
   height: 60.96
}
,
{
   page: 4,
   name: "AgeTrash compactor", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 642,
   left: 145.8,
   width: 28.92,
   height: 49.44
}
,
{
   page: 4,
   name: "Owned Leased If leased provide the name and contact information of entity leased fromTrash compactor", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 642.12,
   left: 177.36,
   width: 415.80,
   height: 49.32
}
,
{
   page: 4,
   name: "Trash compactor16", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 693.24,
   left: 36.48,
   width: 84.12,
   height: 35.40
}
,
{
   page: 4,
   name: "Age16", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 693.12,
   left: 145.8,
   width: 28.92,
   height: 35.52
}
,
{
   page: 4,
   name: "Owned Leased If leased provide the name and contact information of entity leased from16", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 693.24,
   left: 177.36,
   width: 415.80,
   height: 35.40
}
,
{
   page: 4,
   name: "Check Box746", 
   isText: false,
   type: "checkbox",
   top: 62.463,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box747", 
   isText: false,
   type: "checkbox",
   top: 104.906,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box748", 
   isText: false,
   type: "checkbox",
   top: 157.76,
   left: 125.927,
   width: 13.680,
   height: 13.68
}
,
{
   page: 4,
   name: "Check Box749", 
   isText: false,
   type: "checkbox",
   top: 208.211,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box750", 
   isText: false,
   type: "checkbox",
   top: 257.861,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box751", 
   isText: false,
   type: "checkbox",
   top: 256.26,
   left: 180.983,
   width: 13.680,
   height: 13.68
}
,
{
   page: 4,
   name: "Check Box752", 
   isText: false,
   type: "checkbox",
   top: 255.459,
   left: 261.865,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box753", 
   isText: false,
   type: "checkbox",
   top: 314.719,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box754", 
   isText: false,
   type: "checkbox",
   top: 310.715,
   left: 180.183,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box755", 
   isText: false,
   type: "checkbox",
   top: 310.715,
   left: 212.215,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box756", 
   isText: false,
   type: "checkbox",
   top: 309.914,
   left: 264.268,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box757", 
   isText: false,
   type: "checkbox",
   top: 310.715,
   left: 312.316,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box758", 
   isText: false,
   type: "checkbox",
   top: 371.576,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box759", 
   isText: false,
   type: "checkbox",
   top: 368.373,
   left: 180.183,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box760", 
   isText: false,
   type: "checkbox",
   top: 368.373,
   left: 212.215,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box761", 
   isText: false,
   type: "checkbox",
   top: 368.373,
   left: 262.666,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box762", 
   isText: false,
   type: "checkbox",
   top: 368.373,
   left: 337.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box763", 
   isText: false,
   type: "checkbox",
   top: 438.044,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box764", 
   isText: false,
   type: "checkbox",
   top: 521.328,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box765", 
   isText: false,
   type: "checkbox",
   top: 514.121,
   left: 180.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box766", 
   isText: false,
   type: "checkbox",
   top: 514.121,
   left: 224.227,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box767", 
   isText: false,
   type: "checkbox",
   top: 582.19,
   left: 125.927,
   width: 13.680,
   height: 13.68
}
,
{
   page: 4,
   name: "Check Box768", 
   isText: false,
   type: "checkbox",
   top: 576.584,
   left: 180.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box769", 
   isText: false,
   type: "checkbox",
   top: 575.783,
   left: 226.63,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box770", 
   isText: false,
   type: "checkbox",
   top: 644.653,
   left: 125.927,
   width: 13.680,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box771", 
   isText: false,
   type: "checkbox",
   top: 695.1042,
   left: 125.927,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 5,
   name: "Age if known220 Volt service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.28,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 5,
   name: "Comments220 Volt service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.28,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 5,
   name: "Amps", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 204.324,
   left: 61.68,
   width: 67.44,
   height: 11.196
}
,
{
   page: 5,
   name: "Age if knownElectrical Service Amps", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 189.96,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 5,
   name: "CommentsElectrical Service Amps", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 189.96,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 5,
   name: "Age if knownLandscape lighting", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 231.96,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 5,
   name: "CommentsLandscape lighting", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 231.96,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 5,
   name: "Age if knownElectric provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 287.64,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 5,
   name: "CommentsElectric provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 287.64,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 5,
   name: "Age if knownCableTV provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 329.64,
   left: 163.44,
   width: 33.24,
   height: 39.96
}
,
{
   page: 5,
   name: "CommentsCableTV provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 329.64,
   left: 199.44,
   width: 384.36,
   height: 39.96
}
,
{
   page: 5,
   name: "Age if knownInternet provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 371.4,
   left: 163.44,
   width: 33.24,
   height: 40.2
}
,
{
   page: 5,
   name: "CommentsInternet provider  provide name in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 371.4,
   left: 199.44,
   width: 384.36,
   height: 40.2
}
,
{
   page: 5,
   name: "YesSolar panels", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 426.92,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 5,
   name: "Age if knownSolar panels", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 413.4,
   left: 163.44,
   width: 33.24,
   height: 54.6
}
,
{
   page: 5,
   name: "Age if knowna Output", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.8,
   left: 163.44,
   width: 33.24,
   height: 40.2
}
,
{
   page: 5,
   name: "Owned Leased If leased provide the name and contact information of entity leased froma Output", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.8,
   left: 199.44,
   width: 384.36,
   height: 40.2
}
,
{
   page: 5,
   name: "Age if knownWind generators", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 511.8,
   left: 163.44,
   width: 33.24,
   height: 40.92
}
,
{
   page: 5,
   name: "Owned Leased If leased provide the name and contact information of entity leased from_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 523.92,
   left: 199.2,
   width: 384.84,
   height: 28.80
}
,
{
   page: 5,
   name: "Age if knownSecurity system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.52,
   left: 163.44,
   width: 33.24,
   height: 54.60
}
,
{
   page: 5,
   name: "Owned Leased If leased provide the name and contact information of entity leased from_3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 567.386,
   left: 199.44,
   width: 384.36,
   height: 41.734
}
,
{
   page: 5,
   name: "Age if knownDoorbell", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 610.92,
   left: 163.44,
   width: 33.24,
   height: 53.16
}
,
{
   page: 5,
   name: "Wired Wireless Smart", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 623.786,
   left: 199.44,
   width: 384.36,
   height: 40.294
}
,
{
   page: 5,
   name: "Age if knownSmokefire detectors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 665.88,
   left: 163.44,
   width: 33.24,
   height: 39.24
}
,
{
   page: 5,
   name: "Battery Hardwire", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 677.338,
   left: 199.2,
   width: 384.84,
   height: 27.782
}
,
{
   page: 5,
   name: "Age if knownCarbon monoxide alarms", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 706.92,
   left: 163.44,
   width: 33.24,
   height: 39.24
}
,
{
   page: 5,
   name: "Battery Hardwire_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 718.3783,
   left: 199.2,
   width: 384.84,
   height: 27.7817
}
,
{
   page: 5,
   name: "Check Box772", 
   isText: false,
   type: "checkbox",
   top: 139.341,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box773", 
   isText: false,
   type: "checkbox",
   top: 192.995,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box774", 
   isText: false,
   type: "checkbox",
   top: 237.04,
   left: 143.544,
   width: 13.680,
   height: 13.68
}
,
{
   page: 5,
   name: "Check Box775", 
   isText: false,
   type: "checkbox",
   top: 291.495,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box776", 
   isText: false,
   type: "checkbox",
   top: 331.536,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box777", 
   isText: false,
   type: "checkbox",
   top: 374.78,
   left: 143.544,
   width: 13.680,
   height: 13.68
}
,
{
   page: 5,
   name: "Check Box778", 
   isText: false,
   type: "checkbox",
   top: 416.422,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box779", 
   isText: false,
   type: "checkbox",
   top: 413.219,
   left: 202.605,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box780", 
   isText: false,
   type: "checkbox",
   top: 413.219,
   left: 249.853,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box781", 
   isText: false,
   type: "checkbox",
   top: 473.279,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box782", 
   isText: false,
   type: "checkbox",
   top: 514.922,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box783", 
   isText: false,
   type: "checkbox",
   top: 511.718,
   left: 202.605,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box784", 
   isText: false,
   type: "checkbox",
   top: 511.718,
   left: 250.654,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box785", 
   isText: false,
   type: "checkbox",
   top: 557.365,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box786", 
   isText: false,
   type: "checkbox",
   top: 554.161,
   left: 201.804,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box787", 
   isText: false,
   type: "checkbox",
   top: 554.161,
   left: 250.654,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box788", 
   isText: false,
   type: "checkbox",
   top: 614.222,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box789", 
   isText: false,
   type: "checkbox",
   top: 611.019,
   left: 201.804,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box790", 
   isText: false,
   type: "checkbox",
   top: 611.019,
   left: 255.459,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box791", 
   isText: false,
   type: "checkbox",
   top: 611.019,
   left: 307.512,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box792", 
   isText: false,
   type: "checkbox",
   top: 668.677,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box793", 
   isText: false,
   type: "checkbox",
   top: 665.474,
   left: 201.804,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box794", 
   isText: false,
   type: "checkbox",
   top: 664.673,
   left: 254.658,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box795", 
   isText: false,
   type: "checkbox",
   top: 709.5189,
   left: 143.544,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 5,
   name: "Check Box796", 
   isText: false,
   type: "checkbox",
   top: 706.3156,
   left: 201.804,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 5,
   name: "Check Box797", 
   isText: false,
   type: "checkbox",
   top: 706.3156,
   left: 255.459,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 6,
   name: "Internet wiring", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 47.4,
   left: 162.88,
   width: 33.371,
   height: 39.72
}
,
{
   page: 6,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 46.8,
   left: 421.2,
   width: 162.0,
   height: 11.76
}
,
{
   page: 6,
   name: "Cable DSL Satellite Fiber Other", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 60,
   left: 199.2,
   width: 384.84,
   height: 27.12
}
,
{
   page: 6,
   name: "14_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 102.3,
   left: 199.2,
   width: 384.84,
   height: 38.74
}
,
{
   page: 6,
   name: "Built in sound system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 88.44,
   left: 162.88,
   width: 33.371,
   height: 51.436
}
,
{
   page: 6,
   name: "Built in sound system15", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 143.4,
   left: 33.12,
   width: 105.72,
   height: 26.52
}
,
{
   page: 6,
   name: "SpeakersBuilt In WiringBuilt In SpeakersWireless15", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 143.4,
   left: 199.2,
   width: 384.84,
   height: 26.52
}
,
{
   page: 6,
   name: "Age if knownSecurity system_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 250.2,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 6,
   name: "CommentsSecurity system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 250.2,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 6,
   name: "Age if knownSmokefire detectors_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 305.88,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 6,
   name: "CommentsSmokefire detectors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 305.88,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 6,
   name: "Age if knownCarbon monoxide alarm", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 347.88,
   left: 163.44,
   width: 33.24,
   height: 39.96
}
,
{
   page: 6,
   name: "CommentsCarbon monoxide alarm", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 347.88,
   left: 199.44,
   width: 384.36,
   height: 39.96
}
,
{
   page: 6,
   name: "Age if knownLight fixtures", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 389.64,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 6,
   name: "CommentsLight fixtures", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 389.64,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 6,
   name: "Age if knownSwitches  outlets", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 431.64,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 6,
   name: "CommentsSwitches  outlets", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 431.64,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 6,
   name: "Age if knownInternet wiring", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 487.32,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 6,
   name: "CommentsInternet wiring", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 487.32,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 6,
   name: "Age if knownInside telephone wiring  blocksjacks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 529.32,
   left: 163.44,
   width: 33.24,
   height: 39.96
}
,
{
   page: 6,
   name: "CommentsInside telephone wiring  blocksjacks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 529.32,
   left: 199.44,
   width: 384.36,
   height: 39.96
}
,
{
   page: 6,
   name: "Age if knownCable TV wiring  jacks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 571.08,
   left: 163.44,
   width: 33.24,
   height: 40.20
}
,
{
   page: 6,
   name: "CommentsCable TV wiring  jacks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 571.08,
   left: 199.44,
   width: 384.36,
   height: 40.20
}
,
{
   page: 6,
   name: "Age if knownCeiling fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 613.08,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 6,
   name: "CommentsCeiling fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 613.08,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 6,
   name: "25", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 143.885,
   left: 163.201,
   width: 33.065,
   height: 25.662
}
,
{
   page: 6,
   name: "Age if knownBathroom vent fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.76,
   left: 163.44,
   width: 33.24,
   height: 53.88
}
,
{
   page: 6,
   name: "CommentsBathroom vent fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.76,
   left: 199.44,
   width: 384.36,
   height: 53.88
}
,
{
   page: 6,
   name: "Check Box798", 
   isText: false,
   type: "checkbox",
   top: 49.65,
   left: 144.146,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box799", 
   isText: false,
   type: "checkbox",
   top: 47.248,
   left: 201.804,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box800", 
   isText: false,
   type: "checkbox",
   top: 48.049,
   left: 243.447,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box801", 
   isText: false,
   type: "checkbox",
   top: 47.248,
   left: 282.686,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box802", 
   isText: false,
   type: "checkbox",
   top: 47.248,
   left: 335.54,
   width: 13.68,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box803", 
   isText: false,
   type: "checkbox",
   top: 46.447,
   left: 382.788,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box804", 
   isText: false,
   type: "checkbox",
   top: 91.292,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box805", 
   isText: false,
   type: "checkbox",
   top: 88.89,
   left: 201.804,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box806", 
   isText: false,
   type: "checkbox",
   top: 88.89,
   left: 293.898,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box807", 
   isText: false,
   type: "checkbox",
   top: 88.89,
   left: 370.776,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box808", 
   isText: false,
   type: "checkbox",
   top: 147.349,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box809", 
   isText: false,
   type: "checkbox",
   top: 252.256,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box810", 
   isText: false,
   type: "checkbox",
   top: 308.312,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box811", 
   isText: false,
   type: "checkbox",
   top: 351.556,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box812", 
   isText: false,
   type: "checkbox",
   top: 392.398,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box813", 
   isText: false,
   type: "checkbox",
   top: 434.04,
   left: 144.146,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box814", 
   isText: false,
   type: "checkbox",
   top: 490.897,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box815", 
   isText: false,
   type: "checkbox",
   top: 531.739,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box816", 
   isText: false,
   type: "checkbox",
   top: 573.381,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box817", 
   isText: false,
   type: "checkbox",
   top: 615.023,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box818", 
   isText: false,
   type: "checkbox",
   top: 671.08,
   left: 144.146,
   width: 13.680,
   height: 13.68
}
,
{
   page: 7,
   name: "26", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 48.48,
   left: 199.56,
   width: 390.60,
   height: 53.88
}
,
{
   page: 7,
   name: "remoteopeners", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 83.4,
   left: 37.92,
   width: 45.00,
   height: 11.16
}
,
{
   page: 7,
   name: "Garage door opener and remote control  of remoteopeners", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 47.4,
   left: 162.88,
   width: 33.371,
   height: 54.36
}
,
{
   page: 7,
   name: "27", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 104.16,
   left: 199.56,
   width: 390.60,
   height: 52.571
}
,
{
   page: 7,
   name: "Garage door keyless entry", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 103.08,
   left: 162.88,
   width: 33.371,
   height: 54.36
}
,
{
   page: 7,
   name: "28", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 158.4,
   left: 199.56,
   width: 390.60,
   height: 39.48
}
,
{
   page: 7,
   name: "Built in intercom system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 158.76,
   left: 162.88,
   width: 33.371,
   height: 40.44
}
,
{
   page: 7,
   name: "29", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 201.6,
   left: 200.215,
   width: 390.600,
   height: 52.571
}
,
{
   page: 7,
   name: "Doorbell", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 200.52,
   left: 162.88,
   width: 33.371,
   height: 54.36
}
,
{
   page: 7,
   name: "30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 257.28,
   left: 199.56,
   width: 390.60,
   height: 51.916
}
,
{
   page: 7,
   name: "Built in sound system_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.2,
   left: 162.88,
   width: 33.371,
   height: 54.36
}
,
{
   page: 7,
   name: "Built in sound system31", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 312.12,
   left: 33.12,
   width: 105.72,
   height: 26.76
}
,
{
   page: 7,
   name: "32", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 312.621,
   left: 162.88,
   width: 33.371,
   height: 23.357
}
,
{
   page: 7,
   name: "Age if knownElectrical Service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 430.68,
   left: 159.36,
   width: 33.24,
   height: 53.88
}
,
{
   page: 7,
   name: "CommentsElectrical Service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 430.68,
   left: 195.36,
   width: 397.80,
   height: 53.88
}
,
{
   page: 7,
   name: "33", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 312.665,
   left: 199.56,
   width: 390.60,
   height: 25.974
}
,
{
   page: 7,
   name: "Age if knownAluminum wiring at the outlets 110", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 486.36,
   left: 159.36,
   width: 33.24,
   height: 53.88
}
,
{
   page: 7,
   name: "CommentsAluminum wiring at the outlets 110", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 486.36,
   left: 195.36,
   width: 397.80,
   height: 53.88
}
,
{
   page: 7,
   name: "Age if knownSolar panels_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 542.04,
   left: 159.36,
   width: 33.24,
   height: 67.80
}
,
{
   page: 7,
   name: "CommentsSolar panels", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 542.04,
   left: 195.36,
   width: 397.80,
   height: 67.80
}
,
{
   page: 7,
   name: "Age if knownWind generators_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 611.64,
   left: 159.36,
   width: 33.24,
   height: 39.96
}
,
{
   page: 7,
   name: "CommentsWind generators", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 611.64,
   left: 195.36,
   width: 397.80,
   height: 39.96
}
,
{
   page: 7,
   name: "Age if knownElectric wiring or panel", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 653.4,
   left: 159.36,
   width: 33.24,
   height: 53.88
}
,
{
   page: 7,
   name: "CommentsElectric wiring or panel", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 653.4,
   left: 195.36,
   width: 397.80,
   height: 53.88
}
,
{
   page: 7,
   name: "Electric wiring or panel37", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 709.08,
   left: 33.36,
   width: 105.24,
   height: 36.84
}
,
{
   page: 7,
   name: "Age if known37", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 709.08,
   left: 159.36,
   width: 33.24,
   height: 36.84
}
,
{
   page: 7,
   name: "Comments37", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 709.08,
   left: 195.36,
   width: 397.80,
   height: 36.84
}
,
{
   page: 7,
   name: "Check Box819", 
   isText: false,
   type: "checkbox",
   top: 52.053,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box820", 
   isText: false,
   type: "checkbox",
   top: 107.309,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box821", 
   isText: false,
   type: "checkbox",
   top: 164.967,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box822", 
   isText: false,
   type: "checkbox",
   top: 204.207,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box823", 
   isText: false,
   type: "checkbox",
   top: 260.264,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box824", 
   isText: false,
   type: "checkbox",
   top: 315.52,
   left: 143.544,
   width: 13.680,
   height: 13.68
}
,
{
   page: 7,
   name: "Check Box825", 
   isText: false,
   type: "checkbox",
   top: 437.243,
   left: 141.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box826", 
   isText: false,
   type: "checkbox",
   top: 489.296,
   left: 141.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box827", 
   isText: false,
   type: "checkbox",
   top: 545.353,
   left: 141.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box828", 
   isText: false,
   type: "checkbox",
   top: 615.023,
   left: 141.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box829", 
   isText: false,
   type: "checkbox",
   top: 655.064,
   left: 141.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box830", 
   isText: false,
   type: "checkbox",
   top: 711.9213,
   left: 141.943,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 8,
   name: "Age if knownOverhead doors including garage doors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 118.2,
   left: 149.88,
   width: 37.68,
   height: 54.36
}
,
{
   page: 8,
   name: "CommentsOverhead doors including garage doors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 118.2,
   left: 190.44,
   width: 402.24,
   height: 54.36
}
,
{
   page: 8,
   name: "Age if knownEntry gate system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 175.08,
   left: 149.88,
   width: 37.68,
   height: 54.60
}
,
{
   page: 8,
   name: "CommentsEntry gate system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 175.08,
   left: 190.44,
   width: 402.24,
   height: 54.60
}
,
{
   page: 8,
   name: "Age if knownElevator", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 232.2,
   left: 149.88,
   width: 37.68,
   height: 68.52
}
,
{
   page: 8,
   name: "CommentsElevator", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 232.2,
   left: 190.44,
   width: 402.24,
   height: 68.52
}
,
{
   page: 8,
   name: "of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 316.193,
   left: 52.8,
   width: 59.04,
   height: 10.447
}
,
{
   page: 8,
   name: "Age if knownSump pumps  of", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 303.24,
   left: 149.88,
   width: 37.68,
   height: 54.60
}
,
{
   page: 8,
   name: "CommentsSump pumps  of", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 303.24,
   left: 190.44,
   width: 402.24,
   height: 54.60
}
,
{
   page: 8,
   name: "Age if knownRecycle pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 360.36,
   left: 149.88,
   width: 37.68,
   height: 40.68
}
,
{
   page: 8,
   name: "CommentsRecycle pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 360.36,
   left: 190.44,
   width: 402.24,
   height: 40.68
}
,
{
   page: 8,
   name: "Recycle pump6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 403.32,
   left: 32.64,
   width: 92.28,
   height: 27.24
}
,
{
   page: 8,
   name: "Age if known6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 403.32,
   left: 149.64,
   width: 38.16,
   height: 27.24
}
,
{
   page: 8,
   name: "Comments6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 403.32,
   left: 190.2,
   width: 402.72,
   height: 27.24
}
,
{
   page: 8,
   name: "Age if knownFurnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 532.44,
   left: 163.56,
   width: 37.68,
   height: 82.20
}
,
{
   page: 8,
   name: "CommentsFurnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 532.44,
   left: 204.12,
   width: 389.04,
   height: 82.20
}
,
{
   page: 8,
   name: "Yesa Furnace Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 644.708,
   left: 204.12,
   width: 389.04,
   height: 55.132
}
,
{
   page: 8,
   name: "Age if knowna Furnace Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617.16,
   left: 163.56,
   width: 37.68,
   height: 84.36
}
,
{
   page: 8,
   name: "Age if knownb Number of Units", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 704.04,
   left: 163.56,
   width: 37.68,
   height: 50.04
}
,
{
   page: 8,
   name: "Forced Air Gas Forced Air Electric Forced Air Propane Radiant Gravity Flow Other specifyb Number of Units", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 704.04,
   left: 204.12,
   width: 389.04,
   height: 50.04
}
,
{
   page: 8,
   name: "Check Box831", 
   isText: false,
   type: "checkbox",
   top: 123.325,
   left: 129.731,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box832", 
   isText: false,
   type: "checkbox",
   top: 179.382,
   left: 129.731,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box833", 
   isText: false,
   type: "checkbox",
   top: 237.04,
   left: 129.731,
   width: 13.680,
   height: 13.68
}
,
{
   page: 8,
   name: "Check Box834", 
   isText: false,
   type: "checkbox",
   top: 306.711,
   left: 129.731,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box835", 
   isText: false,
   type: "checkbox",
   top: 361.967,
   left: 129.731,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box836", 
   isText: false,
   type: "checkbox",
   top: 405.211,
   left: 129.731,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box837", 
   isText: false,
   type: "checkbox",
   top: 535.743,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box838", 
   isText: false,
   type: "checkbox",
   top: 535.743,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box839", 
   isText: false,
   type: "checkbox",
   top: 620.629,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box840", 
   isText: false,
   type: "checkbox",
   top: 707.1164,
   left: 140.943,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 8,
   name: "Check Box841", 
   isText: false,
   type: "checkbox",
   top: 616.426,
   left: 205.008,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box842", 
   isText: false,
   type: "checkbox",
   top: 616.426,
   left: 289.093,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box843", 
   isText: false,
   type: "checkbox",
   top: 629.438,
   left: 205.008,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box844", 
   isText: false,
   type: "checkbox",
   top: 629.438,
   left: 297.101,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box845", 
   isText: false,
   type: "checkbox",
   top: 629.438,
   left: 346.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box846", 
   isText: false,
   type: "checkbox",
   top: 629.438,
   left: 416.422,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "c Zoned", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 48.24,
   left: 163.56,
   width: 36.916,
   height: 141.36
}
,
{
   page: 9,
   name: "Heating system other than furnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 191.76,
   left: 163.56,
   width: 36.916,
   height: 54.96
}
,
{
   page: 9,
   name: "Location of zone 1 Location of zone 2 Location of zone 3Heating system other than furnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 192.12,
   left: 204.12,
   width: 389.04,
   height: 54.60
}
,
{
   page: 9,
   name: "2Row1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 55.737,
   left: 204.12,
   width: 389.04,
   height: 49.211
}
,
{
   page: 9,
   name: "a TypeFuel", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 248.88,
   left: 163.56,
   width: 36.916,
   height: 54.96
}
,
{
   page: 9,
   name: "Location of zone 1 Location of zone 2 Location of zone 3a TypeFuel", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249.24,
   left: 204.12,
   width: 389.04,
   height: 54.60
}
,
{
   page: 9,
   name: "Fireplace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 306.0,
   left: 163.56,
   width: 36.916,
   height: 54.96
}
,
{
   page: 9,
   name: "Location of zone 1 Location of zone 2 Location of zone 3Fireplace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 306.36,
   left: 204.12,
   width: 389.04,
   height: 54.60
}
,
{
   page: 9,
   name: "3Row1_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 113.032,
   left: 204.12,
   width: 389.04,
   height: 43.320
}
,
{
   page: 9,
   name: "a Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 363.12,
   left: 163.56,
   width: 36.916,
   height: 54.00
}
,
{
   page: 9,
   name: "Masonry Insert Wood Burning Direct Vent Other specify", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 376.564,
   left: 204.12,
   width: 389.04,
   height: 41.357
}
,
{
   page: 9,
   name: "3Row2_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 164.301,
   left: 204.12,
   width: 389.04,
   height: 23.683
}
,
{
   page: 9,
   name: "b Fireplace starter", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 419.28,
   left: 163.56,
   width: 36.916,
   height: 54.24
}
,
{
   page: 9,
   name: "Switch Remote", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 431.924,
   left: 204.12,
   width: 389.04,
   height: 41.596
}
,
{
   page: 9,
   name: "Age if knownFree Standing Heating Stove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 563.88,
   left: 163.56,
   width: 37.68,
   height: 68.28
}
,
{
   page: 9,
   name: "CommentsFree Standing Heating Stove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 563.88,
   left: 204.12,
   width: 389.04,
   height: 68.28
}
,
{
   page: 9,
   name: "Age if knowna Fuel Source", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 634.68,
   left: 163.56,
   width: 37.68,
   height: 57.24
}
,
{
   page: 9,
   name: "Wood Pellet Corn Gas Other specify", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 645.48,
   left: 204.12,
   width: 389.04,
   height: 46.44
}
,
{
   page: 9,
   name: "Age if knownDate fireplacewood stove chimneyflue last cleaned", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.44,
   left: 163.56,
   width: 37.68,
   height: 54.12
}
,
{
   page: 9,
   name: "Do not know", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 705,
   left: 204.12,
   width: 389.04,
   height: 43.56
}
,
{
   page: 9,
   name: "chimneyflue last cleaned", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 718.5928,
   left: 37.92,
   width: 92.40,
   height: 16.7672
}
,
{
   page: 9,
   name: "Check Box473", 
   isText: false,
   type: "checkbox",
   top: 364.028,
   left: 206.245,
   width: 12.908,
   height: 11.890
}
,
{
   page: 9,
   name: "Check Box474", 
   isText: false,
   type: "checkbox",
   top: 364.028,
   left: 256.105,
   width: 12.909,
   height: 11.890
}
,
{
   page: 9,
   name: "Check Box475", 
   isText: false,
   type: "checkbox",
   top: 364.028,
   left: 295.274,
   width: 12.908,
   height: 11.890
}
,
{
   page: 9,
   name: "Check Box476", 
   isText: false,
   type: "checkbox",
   top: 364.028,
   left: 368.083,
   width: 12.909,
   height: 11.890
}
,
{
   page: 9,
   name: "Check Box477", 
   isText: false,
   type: "checkbox",
   top: 364.028,
   left: 428.654,
   width: 12.909,
   height: 11.890
}
,
{
   page: 9,
   name: "Check Box479", 
   isText: false,
   type: "checkbox",
   top: 419.544,
   left: 207.226,
   width: 12.400,
   height: 11.891
}
,
{
   page: 9,
   name: "Check Box480", 
   isText: false,
   type: "checkbox",
   top: 419.544,
   left: 254.032,
   width: 12.399,
   height: 11.891
}
,
{
   page: 9,
   name: "Check Box483", 
   isText: false,
   type: "checkbox",
   top: 633.881,
   left: 206.717,
   width: 12.909,
   height: 12.399
}
,
{
   page: 9,
   name: "Check Box484", 
   isText: false,
   type: "checkbox",
   top: 633.881,
   left: 247.941,
   width: 12.908,
   height: 12.399
}
,
{
   page: 9,
   name: "Check Box485", 
   isText: false,
   type: "checkbox",
   top: 633.881,
   left: 287.655,
   width: 12.908,
   height: 12.399
}
,
{
   page: 9,
   name: "Check Box486", 
   isText: false,
   type: "checkbox",
   top: 633.881,
   left: 324.314,
   width: 12.908,
   height: 12.399
}
,
{
   page: 9,
   name: "Check Box487", 
   isText: false,
   type: "checkbox",
   top: 633.881,
   left: 357.409,
   width: 12.909,
   height: 12.399
}
,
{
   page: 9,
   name: "Check Box489", 
   isText: false,
   type: "checkbox",
   top: 694.4886,
   left: 206.208,
   width: 12.909,
   height: 11.3810
}
,
{
   page: 9,
   name: "Check Box847", 
   isText: false,
   type: "checkbox",
   top: 52.854,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box848", 
   isText: false,
   type: "checkbox",
   top: 195.398,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box849", 
   isText: false,
   type: "checkbox",
   top: 255.459,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box850", 
   isText: false,
   type: "checkbox",
   top: 309.113,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box851", 
   isText: false,
   type: "checkbox",
   top: 367.572,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box852", 
   isText: false,
   type: "checkbox",
   top: 422.828,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box853", 
   isText: false,
   type: "checkbox",
   top: 568.576,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box854", 
   isText: false,
   type: "checkbox",
   top: 639.047,
   left: 141.142,
   width: 13.680,
   height: 13.680
}
,
{
   page: 9,
   name: "Check Box855", 
   isText: false,
   type: "checkbox",
   top: 698.3075,
   left: 141.142,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 10,
   name: "Fuel tanks If leased provide the name and contact information of entity leased from in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 48.24,
   left: 163.56,
   width: 38.226,
   height: 67.92
}
,
{
   page: 10,
   name: "Owned Leased", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 58.92,
   left: 204.12,
   width: 389.04,
   height: 57.24
}
,
{
   page: 10,
   name: "Radiant heating system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 117.44,
   left: 163.68,
   width: 38.226,
   height: 26.28
}
,
{
   page: 10,
   name: "7Row1_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 116.247,
   left: 307.439,
   width: 285.721,
   height: 27.175
}
,
{
   page: 10,
   name: "a Interior Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 145.88,
   left: 163.56,
   width: 38.226,
   height: 54.96
}
,
{
   page: 10,
   name: "Interior Exteriora Interior Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 146.222,
   left: 204.12,
   width: 389.04,
   height: 55.618
}
,
{
   page: 10,
   name: "b Exterior Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 203.0,
   left: 163.56,
   width: 38.226,
   height: 53.651
}
,
{
   page: 10,
   name: "Interior Exteriorb Exterior Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 204.36,
   left: 204.12,
   width: 389.04,
   height: 54.60
}
,
{
   page: 10,
   name: "Age if knownAir Conditioning", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 349.32,
   left: 163.56,
   width: 37.68,
   height: 91.32
}
,
{
   page: 10,
   name: "CommentsAir Conditioning", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 349.32,
   left: 204.12,
   width: 389.04,
   height: 91.32
}
,
{
   page: 10,
   name: "Yesa Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 454.727,
   left: 204.12,
   width: 389.04,
   height: 54.113
}
,
{
   page: 10,
   name: "Age if knowna Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 443.16,
   left: 163.56,
   width: 37.68,
   height: 66.84
}
,
{
   page: 10,
   name: "Age if knownb Number of Units_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 512.52,
   left: 163.56,
   width: 37.68,
   height: 50.04
}
,
{
   page: 10,
   name: "Electric Central Air Other specifyb Number of Units", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 512.52,
   left: 204.12,
   width: 389.04,
   height: 50.04
}
,
{
   page: 10,
   name: "8Row3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 625.233,
   left: 203.324,
   width: 390.372,
   height: 44.487
}
,
{
   page: 10,
   name: "Yesc Zoned", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 573.884,
   left: 203.324,
   width: 390.372,
   height: 44.487
}
,
{
   page: 10,
   name: "Age if knownc Zoned", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 565.08,
   left: 163.56,
   width: 37.68,
   height: 141.00
}
,
{
   page: 10,
   name: "8Row4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 677.771,
   left: 204.12,
   width: 389.04,
   height: 27.6107
}
,
{
   page: 10,
   name: "c ZonedRow1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 708.6,
   left: 33.36,
   width: 100.68,
   height: 36.12
}
,
{
   page: 10,
   name: "Age if knownRow5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 708.6,
   left: 163.56,
   width: 37.68,
   height: 36.12
}
,
{
   page: 10,
   name: "Location of zone 1 Location of zone 2 Location of zone 3Row1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 708.6,
   left: 204.12,
   width: 389.04,
   height: 36.12
}
,
{
   page: 10,
   name: "Check Box491", 
   isText: false,
   type: "checkbox",
   top: 47.351,
   left: 208.681,
   width: 11.890,
   height: 12.909
}
,
{
   page: 10,
   name: "Check Box492", 
   isText: false,
   type: "checkbox",
   top: 47.351,
   left: 253.486,
   width: 11.891,
   height: 12.909
}
,
{
   page: 10,
   name: "Check Box494", 
   isText: false,
   type: "checkbox",
   top: 118.633,
   left: 205.699,
   width: 12.908,
   height: 12.400
}
,
{
   page: 10,
   name: "Check Box495", 
   isText: false,
   type: "checkbox",
   top: 118.633,
   left: 254.505,
   width: 12.908,
   height: 12.400
}
,
{
   page: 10,
   name: "Check Box500", 
   isText: false,
   type: "checkbox",
   top: 441.42,
   left: 208.263,
   width: 12.399,
   height: 13.417
}
,
{
   page: 10,
   name: "Check Box501", 
   isText: false,
   type: "checkbox",
   top: 441.42,
   left: 258.087,
   width: 12.399,
   height: 13.417
}
,
{
   page: 10,
   name: "Check Box502", 
   isText: false,
   type: "checkbox",
   top: 441.42,
   left: 327.387,
   width: 12.400,
   height: 13.417
}
,
{
   page: 10,
   name: "Check Box856", 
   isText: false,
   type: "checkbox",
   top: 53.654,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 10,
   name: "Check Box857", 
   isText: false,
   type: "checkbox",
   top: 120.122,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 10,
   name: "Check Box858", 
   isText: false,
   type: "checkbox",
   top: 150.552,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 10,
   name: "Check Box859", 
   isText: false,
   type: "checkbox",
   top: 207.41,
   left: 140.142,
   width: 13.680,
   height: 13.68
}
,
{
   page: 10,
   name: "Check Box860", 
   isText: false,
   type: "checkbox",
   top: 355.56,
   left: 140.943,
   width: 13.680,
   height: 13.68
}
,
{
   page: 10,
   name: "Check Box861", 
   isText: false,
   type: "checkbox",
   top: 447.654,
   left: 141.744,
   width: 13.680,
   height: 13.679
}
,
{
   page: 10,
   name: "Check Box862", 
   isText: false,
   type: "checkbox",
   top: 515.722,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 10,
   name: "Check Box863", 
   isText: false,
   type: "checkbox",
   top: 568.576,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 10,
   name: "Check Box864", 
   isText: false,
   type: "checkbox",
   top: 711.1205,
   left: 140.943,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 11,
   name: "Age if knownFurnace_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 128.28,
   left: 163.56,
   width: 37.68,
   height: 87.96
}
,
{
   page: 11,
   name: "CommentsFurnace_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 128.28,
   left: 204.12,
   width: 389.04,
   height: 87.96
}
,
{
   page: 11,
   name: "Age if knownHeating System other than Furnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 218.76,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 11,
   name: "CommentsHeating System other than Furnace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 218.76,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 11,
   name: "Age if knownHeat Pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 298.92,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 11,
   name: "CommentsHeat Pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 298.92,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 11,
   name: "Age if knownEvaporative cooler", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 379.08,
   left: 163.56,
   width: 37.68,
   height: 77.40
}
,
{
   page: 11,
   name: "CommentsEvaporative cooler", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 379.08,
   left: 204.12,
   width: 389.04,
   height: 77.40
}
,
{
   page: 11,
   name: "Age if knownWindow air conditioning units", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 459,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 11,
   name: "CommentsWindow air conditioning units", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 459,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 11,
   name: "Age if knownCentral air conditioning", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 539.16,
   left: 163.56,
   width: 37.68,
   height: 91.32
}
,
{
   page: 11,
   name: "CommentsCentral air conditioning", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 539.16,
   left: 204.12,
   width: 389.04,
   height: 91.32
}
,
{
   page: 11,
   name: "Age if knownAttic ventilation system attic only", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 633,
   left: 163.56,
   width: 37.68,
   height: 50.04
}
,
{
   page: 11,
   name: "CommentsAttic ventilation system attic only", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 633,
   left: 204.12,
   width: 389.04,
   height: 50.04
}
,
{
   page: 11,
   name: "Age if knownWhole house fan", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 685.56,
   left: 163.56,
   width: 37.68,
   height: 63.72
}
,
{
   page: 11,
   name: "CommentsWhole house fan", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 685.56,
   left: 204.12,
   width: 389.04,
   height: 63.72
}
,
{
   page: 11,
   name: "Check Box867", 
   isText: false,
   type: "checkbox",
   top: 132.134,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box868", 
   isText: false,
   type: "checkbox",
   top: 222.625,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box869", 
   isText: false,
   type: "checkbox",
   top: 302.707,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box870", 
   isText: false,
   type: "checkbox",
   top: 383.589,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box871", 
   isText: false,
   type: "checkbox",
   top: 464.471,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box872", 
   isText: false,
   type: "checkbox",
   top: 543.751,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box873", 
   isText: false,
   type: "checkbox",
   top: 637.446,
   left: 141.744,
   width: 13.680,
   height: 13.680
}
,
{
   page: 11,
   name: "Check Box874", 
   isText: false,
   type: "checkbox",
   top: 690.299,
   left: 141.744,
   width: 13.680,
   height: 13.6804
}
,
{
   page: 12,
   name: "Age if knownVent fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 86.28,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 12,
   name: "CommentsVent fans", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 86.28,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 12,
   name: "Age if knownHumidifier", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 166.44,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 12,
   name: "CommentsHumidifier", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 166.44,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 12,
   name: "Age if knownAir purifier", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 246.6,
   left: 163.56,
   width: 37.68,
   height: 77.4
}
,
{
   page: 12,
   name: "CommentsAir purifier", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 246.6,
   left: 204.12,
   width: 389.04,
   height: 77.4
}
,
{
   page: 12,
   name: "Age if knownFireplace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 326.52,
   left: 163.56,
   width: 37.68,
   height: 91.56
}
,
{
   page: 12,
   name: "CommentsFireplace", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 326.52,
   left: 204.12,
   width: 389.04,
   height: 91.56
}
,
{
   page: 12,
   name: "Age if knownFireplace insert", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 420.6,
   left: 163.56,
   width: 37.68,
   height: 77.4
}
,
{
   page: 12,
   name: "CommentsFireplace insert", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 420.6,
   left: 204.12,
   width: 389.04,
   height: 77.4
}
,
{
   page: 12,
   name: "Age if knownFireplace starter", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 500.52,
   left: 163.56,
   width: 37.68,
   height: 63.72
}
,
{
   page: 12,
   name: "CommentsFireplace starter", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 500.52,
   left: 204.12,
   width: 389.04,
   height: 63.72
}
,
{
   page: 12,
   name: "Age if knownHeating Stove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 566.76,
   left: 163.56,
   width: 37.68,
   height: 77.64
}
,
{
   page: 12,
   name: "CommentsHeating Stove", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 566.76,
   left: 204.12,
   width: 389.04,
   height: 77.64
}
,
{
   page: 12,
   name: "Age if knownFuel tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 646.92,
   left: 163.56,
   width: 37.68,
   height: 63.72
}
,
{
   page: 12,
   name: "CommentsFuel tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 646.92,
   left: 204.12,
   width: 389.04,
   height: 63.72
}
,
{
   page: 12,
   name: "Fuel tanks25", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 712.92,
   left: 33.12,
   width: 101.16,
   height: 27.24
}
,
{
   page: 12,
   name: "Age if known25", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 712.92,
   left: 163.32,
   width: 38.16,
   height: 27.24
}
,
{
   page: 12,
   name: "Comments25", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 712.92,
   left: 203.88,
   width: 389.52,
   height: 27.24
}
,
{
   page: 12,
   name: "Check Box875", 
   isText: false,
   type: "checkbox",
   top: 92.093,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box876", 
   isText: false,
   type: "checkbox",
   top: 171.374,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box877", 
   isText: false,
   type: "checkbox",
   top: 251.455,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box878", 
   isText: false,
   type: "checkbox",
   top: 330.735,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box879", 
   isText: false,
   type: "checkbox",
   top: 425.231,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box880", 
   isText: false,
   type: "checkbox",
   top: 505.312,
   left: 140.943,
   width: 13.680,
   height: 13.680
}
,
{
   page: 12,
   name: "Check Box881", 
   isText: false,
   type: "checkbox",
   top: 572.58,
   left: 140.943,
   width: 13.680,
   height: 13.68
}
,
{
   page: 12,
   name: "Check Box882", 
   isText: false,
   type: "checkbox",
   top: 651.06,
   left: 140.943,
   width: 13.680,
   height: 13.68
}
,
{
   page: 12,
   name: "Check Box883", 
   isText: false,
   type: "checkbox",
   top: 715.1245,
   left: 140.943,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 13,
   name: "Age if knownWater heater", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 142.8,
   left: 154.56,
   width: 28.80,
   height: 54.0
}
,
{
   page: 13,
   name: "CommentsWater heater", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 142.92,
   left: 186.12,
   width: 407.04,
   height: 53.88
}
,
{
   page: 13,
   name: "Commentsa Number of Water Heaters", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 198.6,
   left: 186.12,
   width: 407.04,
   height: 35.4
}
,
{
   page: 13,
   name: "Age if knownb Fuel Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 235.68,
   left: 154.56,
   width: 28.80,
   height: 54.24
}
,
{
   page: 13,
   name: "Commentsb Fuel Type", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 235.8,
   left: 186.12,
   width: 407.04,
   height: 54.12
}
,
{
   page: 13,
   name: "Age if knownc Capacity", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 291.6,
   left: 154.56,
   width: 28.80,
   height: 49.2
}
,
{
   page: 13,
   name: "Commentsc Capacity", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 291.72,
   left: 186.12,
   width: 407.04,
   height: 49.08
}
,
{
   page: 13,
   name: "Age if knownWater filter system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 342.48,
   left: 154.56,
   width: 28.80,
   height: 52.08
}
,
{
   page: 13,
   name: "Owned Leased If leased provide the name and contact information of entity leased from_4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 354.122,
   left: 186.12,
   width: 407.04,
   height: 40.438
}
,
{
   page: 13,
   name: "YesWater softener", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 408.582,
   left: 186.12,
   width: 407.04,
   height: 38.258
}
,
{
   page: 13,
   name: "Age if knownWater softener", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 396.24,
   left: 154.56,
   width: 28.80,
   height: 52.08
}
,
{
   page: 13,
   name: "Age if knownIndicate location of master water shutoff in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 450,
   left: 154.56,
   width: 28.80,
   height: 54
}
,
{
   page: 13,
   name: "Owned Leased If leased provide the name and contact information of entity leased fromIndicate location of master water shutoff in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 450.12,
   left: 186.12,
   width: 407.04,
   height: 53.88
}
,
{
   page: 13,
   name: "Age if knownType of well", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 505.68,
   left: 154.56,
   width: 28.80,
   height: 54.00
}
,
{
   page: 13,
   name: "Owned Leased If leased provide the name and contact information of entity leased fromType of well", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 505.8,
   left: 186.12,
   width: 407.04,
   height: 53.88
}
,
{
   page: 13,
   name: "Yesa Exempt well outside designated groundwater basin", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 682.211,
   left: 188.629,
   width: 398.106,
   height: 40.9208
}
,
{
   page: 13,
   name: "Age if knowna Exempt well outside designated groundwater basin", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 561.36,
   left: 154.56,
   width: 28.80,
   height: 181.44
}
,
{
   page: 13,
   name: "Permit no", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 571.8,
   left: 230.52,
   width: 355.56,
   height: 14.52
}
,
{
   page: 13,
   name: "Permit no_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 618.6,
   left: 230.52,
   width: 355.56,
   height: 14.52
}
,
{
   page: 13,
   name: "Permit no_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 649.56,
   left: 230.52,
   width: 355.56,
   height: 14.52
}
,
{
   page: 13,
   name: "Permit no_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 723.0583,
   left: 230.52,
   width: 355.56,
   height: 13.5017
}
,
{
   page: 13,
   name: "Check Box884", 
   isText: false,
   type: "checkbox",
   top: 145.748,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box885", 
   isText: false,
   type: "checkbox",
   top: 202.605,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box886", 
   isText: false,
   type: "checkbox",
   top: 238.642,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box887", 
   isText: false,
   type: "checkbox",
   top: 295.499,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box888", 
   isText: false,
   type: "checkbox",
   top: 346.751,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box889", 
   isText: false,
   type: "checkbox",
   top: 401.206,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box890", 
   isText: false,
   type: "checkbox",
   top: 454.861,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box891", 
   isText: false,
   type: "checkbox",
   top: 509.316,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box892", 
   isText: false,
   type: "checkbox",
   top: 563.771,
   left: 132.935,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Text893", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 199.402,
   left: 154.557,
   width: 28.276,
   height: 32.411
}
,
{
   page: 13,
   name: "Check Box894", 
   isText: false,
   type: "checkbox",
   top: 559.767,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box895", 
   isText: false,
   type: "checkbox",
   top: 590.999,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box896", 
   isText: false,
   type: "checkbox",
   top: 638.247,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box897", 
   isText: false,
   type: "checkbox",
   top: 669.478,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box898", 
   isText: false,
   type: "checkbox",
   top: 341.946,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box899", 
   isText: false,
   type: "checkbox",
   top: 340.345,
   left: 233.837,
   width: 13.680,
   height: 13.680
}
,
{
   page: 13,
   name: "Check Box900", 
   isText: false,
   type: "checkbox",
   top: 394.8,
   left: 188.991,
   width: 13.680,
   height: 13.68
}
,
{
   page: 13,
   name: "Check Box901", 
   isText: false,
   type: "checkbox",
   top: 395.601,
   left: 238.642,
   width: 13.680,
   height: 13.680
}
,
{
   page: 14,
   name: "Yesb Small capacity well inside designated groundwater basin", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 121.375,
   left: 187.974,
   width: 402.688,
   height: 42.905
}
,
{
   page: 14,
   name: "Ageb Small capacity well inside designated groundwater basin", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 63.36,
   left: 154.56,
   width: 28.80,
   height: 122.64
}
,
{
   page: 14,
   name: "Permit no_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 89.4,
   left: 229.44,
   width: 357.00,
   height: 14.76
}
,
{
   page: 14,
   name: "Permit no_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 165,
   left: 230.52,
   width: 355.56,
   height: 14.76
}
,
{
   page: 14,
   name: "AgeWell metered", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 187.68,
   left: 154.56,
   width: 28.80,
   height: 54.00
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noWell metered", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 187.8,
   left: 186.12,
   width: 407.04,
   height: 53.88
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noWell Pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 243.48,
   left: 186.12,
   width: 407.04,
   height: 40.20
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noa Brand name and pump number", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 285.48,
   left: 186.12,
   width: 407.04,
   height: 39.96
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit nob Date installed", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 327,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noc Date of last inspection", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.08,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit nod Date of last service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 383.4,
   left: 186.12,
   width: 407.04,
   height: 40.2
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noe Depth", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 425.16,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit nof GPM and date last measured", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 453.24,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noGalvanized pipe", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 481.56,
   left: 186.12,
   width: 407.04,
   height: 40.20
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noPolybutylene pipe", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 523.56,
   left: 186.12,
   width: 407.04,
   height: 39.96
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noCistern water storage", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 565.08,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noa Number of gallons", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 593.16,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noSupplemental water purchased in past 2 years", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 621.48,
   left: 186.12,
   width: 407.04,
   height: 40.20
}
,
{
   page: 14,
   name: "Agea Name and contact information of entity from which supplemental water was purchased", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.36,
   left: 154.473,
   width: 30.218,
   height: 59.76
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit noa Name and contact information of entity from which supplemental water was purchased", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.48,
   left: 186.12,
   width: 407.04,
   height: 59.64
}
,
{
   page: 14,
   name: "a Name and contact information of entity from which supplemental water was purchased12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 724.68,
   left: 37.8,
   width: 87.12,
   height: 26.52
}
,
{
   page: 14,
   name: "Domestic use indoor household use in up to 3 dwellings on the parcel watering of personal livestock limited irrigation area no more than 1 acrefoot per year Permit no Other please explain Permit no12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 724.68,
   left: 185.88,
   width: 407.52,
   height: 26.52
}
,
{
   page: 14,
   name: "Check Box902", 
   isText: false,
   type: "checkbox",
   top: 68.069,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box903", 
   isText: false,
   type: "checkbox",
   top: 61.662,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box904", 
   isText: false,
   type: "checkbox",
   top: 108.109,
   left: 188.991,
   width: 13.680,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box905", 
   isText: false,
   type: "checkbox",
   top: 190.593,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box906", 
   isText: false,
   type: "checkbox",
   top: 245.849,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box907", 
   isText: false,
   type: "checkbox",
   top: 287.491,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box908", 
   isText: false,
   type: "checkbox",
   top: 328.333,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box909", 
   isText: false,
   type: "checkbox",
   top: 360.365,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box910", 
   isText: false,
   type: "checkbox",
   top: 385.991,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box911", 
   isText: false,
   type: "checkbox",
   top: 428.434,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box912", 
   isText: false,
   type: "checkbox",
   top: 456.462,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box913", 
   isText: false,
   type: "checkbox",
   top: 483.69,
   left: 132.736,
   width: 13.679,
   height: 13.68
}
,
{
   page: 14,
   name: "Check Box914", 
   isText: false,
   type: "checkbox",
   top: 526.133,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box915", 
   isText: false,
   type: "checkbox",
   top: 567.775,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box916", 
   isText: false,
   type: "checkbox",
   top: 596.604,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box917", 
   isText: false,
   type: "checkbox",
   top: 623.031,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box918", 
   isText: false,
   type: "checkbox",
   top: 667.076,
   left: 132.736,
   width: 13.679,
   height: 13.680
}
,
{
   page: 14,
   name: "Check Box919", 
   isText: false,
   type: "checkbox",
   top: 727.1367,
   left: 132.736,
   width: 13.679,
   height: 13.6800
}
,
{
   page: 14,
   name: "Text1067", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 243.183,
   left: 154.473,
   width: 30.218,
   height: 38.364
}
,
{
   page: 14,
   name: "Text1068", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 284.729,
   left: 154.473,
   width: 30.218,
   height: 39.018
}
,
{
   page: 14,
   name: "Text1069", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 325.965,
   left: 154.473,
   width: 30.218,
   height: 25.927
}
,
{
   page: 14,
   name: "Text1070", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 353.456,
   left: 154.473,
   width: 30.218,
   height: 26.582
}
,
{
   page: 14,
   name: "Text1071", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 383.565,
   left: 154.473,
   width: 30.218,
   height: 38.364
}
,
{
   page: 14,
   name: "Text1072", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 424.802,
   left: 154.473,
   width: 30.218,
   height: 25.273
}
,
{
   page: 14,
   name: "Text1073", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 454.257,
   left: 154.473,
   width: 30.218,
   height: 23.309
}
,
{
   page: 14,
   name: "Text1074", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 482.402,
   left: 154.473,
   width: 30.218,
   height: 37.709
}
,
{
   page: 14,
   name: "Text1075", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 524.293,
   left: 154.473,
   width: 30.218,
   height: 37.709
}
,
{
   page: 14,
   name: "Text1076", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 565.53,
   left: 154.473,
   width: 30.218,
   height: 23.963
}
,
{
   page: 14,
   name: "Text1077", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 593.33,
   left: 154.473,
   width: 30.218,
   height: 23.964
}
,
{
   page: 14,
   name: "Text1078", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 619.857,
   left: 154.473,
   width: 30.218,
   height: 40.328
}
,
{
   page: 14,
   name: "Text1079", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 726.5486,
   left: 154.473,
   width: 30.218,
   height: 22.0000
}
,
{
   page: 15,
   name: "Age if knownWater heaters", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 115.8,
   left: 154.56,
   width: 33.24,
   height: 54.12
}
,
{
   page: 15,
   name: "CommentsWater heaters", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 115.8,
   left: 190.56,
   width: 402.60,
   height: 54.12
}
,
{
   page: 15,
   name: "Age if knownWater filter system_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 171.72,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsWater filter system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 171.72,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownWater softener_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 227.4,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsWater softener", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 227.4,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownWater system pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 283.08,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsWater system pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 283.08,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownSauna", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 338.76,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsSauna", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 338.76,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownHot tub or spa", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 394.44,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsHot tub or spa", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 394.44,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownSteam roomshower", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 450.12,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsSteam roomshower", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 450.12,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownUnderground sprinkler system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 505.8,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsUnderground sprinkler system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 505.8,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownFire sprinkler system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 561.48,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsFire sprinkler system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 561.48,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownBackflow prevention device", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617.16,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 15,
   name: "CommentsBackflow prevention device", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617.16,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 15,
   name: "Age if knownIrrigation pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 672.84,
   left: 154.56,
   width: 33.24,
   height: 40.20
}
,
{
   page: 15,
   name: "CommentsIrrigation pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 672.84,
   left: 190.56,
   width: 402.60,
   height: 40.20
}
,
{
   page: 15,
   name: "Irrigation pump24", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 714.6,
   left: 37.68,
   width: 87.72,
   height: 26.52
}
,
{
   page: 15,
   name: "Age if known24", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 714.6,
   left: 154.32,
   width: 33.72,
   height: 26.52
}
,
{
   page: 15,
   name: "Comments24", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 714.6,
   left: 190.32,
   width: 403.08,
   height: 26.52
}
,
{
   page: 15,
   name: "Check Box920", 
   isText: false,
   type: "checkbox",
   top: 120.922,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box921", 
   isText: false,
   type: "checkbox",
   top: 176.178,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box922", 
   isText: false,
   type: "checkbox",
   top: 232.235,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box923", 
   isText: false,
   type: "checkbox",
   top: 287.491,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box924", 
   isText: false,
   type: "checkbox",
   top: 344.349,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box925", 
   isText: false,
   type: "checkbox",
   top: 400.406,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box926", 
   isText: false,
   type: "checkbox",
   top: 454.06,
   left: 132.134,
   width: 13.680,
   height: 13.68
}
,
{
   page: 15,
   name: "Check Box927", 
   isText: false,
   type: "checkbox",
   top: 510.117,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box928", 
   isText: false,
   type: "checkbox",
   top: 565.373,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box929", 
   isText: false,
   type: "checkbox",
   top: 622.23,
   left: 132.134,
   width: 13.680,
   height: 13.68
}
,
{
   page: 15,
   name: "Check Box930", 
   isText: false,
   type: "checkbox",
   top: 677.486,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 15,
   name: "Check Box931", 
   isText: false,
   type: "checkbox",
   top: 718.3278,
   left: 132.134,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 16,
   name: "Age if kno wnLeaks backups or other similar problems with any portion of the water or plumbing systems including lines and water pressure or damage therefrom", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.665,
   left: 154.56,
   width: 33.24,
   height: 95.640
}
,
{
   page: 16,
   name: "CommentsLeaks backups or other similar problems with any portion of the water or plumbing systems including lines and water pressure or damage therefrom", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 121.32,
   left: 190.56,
   width: 402.60,
   height: 95.64
}
,
{
   page: 16,
   name: "Age if kno wnWell", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 218.76,
   left: 154.56,
   width: 33.24,
   height: 67.80
}
,
{
   page: 16,
   name: "CommentsWell", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 218.76,
   left: 190.56,
   width: 402.60,
   height: 67.80
}
,
{
   page: 16,
   name: "Age if kno wnPool", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 288.36,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 16,
   name: "CommentsPool", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 288.36,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 16,
   name: "Age if kno wnIrrigation system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 344.04,
   left: 154.56,
   width: 33.24,
   height: 67.56
}
,
{
   page: 16,
   name: "CommentsIrrigation system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 344.04,
   left: 190.56,
   width: 402.60,
   height: 67.56
}
,
{
   page: 16,
   name: "Age if kno wnWater has been tested for potability", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 413.4,
   left: 154.56,
   width: 33.24,
   height: 53.88
}
,
{
   page: 16,
   name: "CommentsWater has been tested for potability", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 413.4,
   left: 190.56,
   width: 402.60,
   height: 53.88
}
,
{
   page: 16,
   name: "Age if kno wna Indicate result of test in comments and provide the most recent records and reports pertaining to such testing", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.08,
   left: 154.56,
   width: 33.24,
   height: 83.64
}
,
{
   page: 16,
   name: "Commentsa Indicate result of test in comments and provide the most recent records and reports pertaining to such testing", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.08,
   left: 190.56,
   width: 402.60,
   height: 83.64
}
,
{
   page: 16,
   name: "a Indicate result of test in comments and provide the most recent records and reports pertaining to such testing30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.28,
   left: 37.68,
   width: 87.72,
   height: 26.52
}
,
{
   page: 16,
   name: "Age if kno wn30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.28,
   left: 154.32,
   width: 33.72,
   height: 26.52
}
,
{
   page: 16,
   name: "Comments30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.28,
   left: 190.32,
   width: 403.08,
   height: 26.52
}
,
{
   page: 16,
   name: "SOURCE OF WATER  WATER SUPPLY Provide the following information regarding the Property", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 665.738,
   left: 41.3227,
   width: 539.1063,
   height: 12.219
}
,
{
   page: 16,
   name: "Is Not provided Well Permit", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 631.8,
   left: 433.8,
   width: 144.0,
   height: 12.84
}
,
{
   page: 16,
   name: "Water Provider Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.88,
   left: 67.2,
   width: 240.12,
   height: 14.1491
}
,
{
   page: 16,
   name: "Water Provider Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 695.88,
   left: 345.6,
   width: 234.0,
   height: 14.1491
}
,
{
   page: 16,
   name: "Water Provider Web Site", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 716.76,
   left: 78.96,
   width: 307.20,
   height: 14.1491
}
,
{
   page: 16,
   name: "Water Provider Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 716.76,
   left: 432.96,
   width: 148.56,
   height: 14.1491
}
,
{
   page: 16,
   name: "Check Box932", 
   isText: false,
   type: "checkbox",
   top: 127.329,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box933", 
   isText: false,
   type: "checkbox",
   top: 224.227,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box934", 
   isText: false,
   type: "checkbox",
   top: 293.898,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box935", 
   isText: false,
   type: "checkbox",
   top: 349.154,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box936", 
   isText: false,
   type: "checkbox",
   top: 418.824,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box937", 
   isText: false,
   type: "checkbox",
   top: 475.682,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box938", 
   isText: false,
   type: "checkbox",
   top: 557.365,
   left: 132.134,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box939", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 123.216,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box940", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 171.076,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box941", 
   isText: false,
   type: "checkbox",
   top: 616.079,
   left: 237.267,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box942", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 279.017,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box943", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 349.79,
   width: 13.68,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box944", 
   isText: false,
   type: "checkbox",
   top: 616.588,
   left: 396.632,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box945", 
   isText: false,
   type: "checkbox",
   top: 634.408,
   left: 265.27,
   width: 13.68,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box946", 
   isText: false,
   type: "checkbox",
   top: 634.408,
   left: 294.801,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box947", 
   isText: false,
   type: "checkbox",
   top: 648.155,
   left: 101.322,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box948", 
   isText: false,
   type: "checkbox",
   top: 648.174,
   left: 135.435,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box949", 
   isText: false,
   type: "checkbox",
   top: 648.665,
   left: 312.622,
   width: 13.680,
   height: 13.680
}
,
{
   page: 16,
   name: "Check Box950", 
   isText: false,
   type: "checkbox",
   top: 649.174,
   left: 358.717,
   width: 13.680,
   height: 13.680
}
,
{
   page: 17,
   name: "CommentsPublic sanitary sewer service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 246.84,
   left: 168,
   width: 425.16,
   height: 53.88
}
,
{
   page: 17,
   name: "Commentsa Name and contact information of public sanitary sewer service provider", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 302.52,
   left: 168,
   width: 425.16,
   height: 53.88
}
,
{
   page: 17,
   name: "Commentsb Date the sewer line was last scoped", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 358.2,
   left: 168,
   width: 425.16,
   height: 40.2
}
,
{
   page: 17,
   name: "CommentsCommunity sanitary sewer service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 400.2,
   left: 168,
   width: 425.16,
   height: 39.96
}
,
{
   page: 17,
   name: "Commentsa Name and contact information of community sanitary sewer service provider", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 441.96,
   left: 168,
   width: 425.16,
   height: 47.64
}
,
{
   page: 17,
   name: "Commentsb Date the sewer line was last scoped_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 491.4,
   left: 168,
   width: 425.16,
   height: 44.76
}
,
{
   page: 17,
   name: "CommentsSeptic System", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 537.96,
   left: 168,
   width: 425.16,
   height: 53.88
}
,
{
   page: 17,
   name: "Yesa Type_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 607.874,
   left: 168.0,
   width: 425.16,
   height: 42.046
}
,
{
   page: 17,
   name: "Tank Leach Lagoonb Date of issuance of the latest Individual Use Permit", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 652.92,
   left: 168,
   width: 425.16,
   height: 35.64
}
,
{
   page: 17,
   name: "Tank Leach Lagoonc Date of the latest inspection", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 690.12,
   left: 167.76,
   width: 425.64,
   height: 26.52
}
,
{
   page: 17,
   name: "Tank Leach Lagoond Date of the latest pumping", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 718.2,
   left: 167.76,
   width: 425.64,
   height: 26.52
}
,
{
   page: 17,
   name: "Text586", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 60.08,
   left: 37.1684,
   width: 555.7976,
   height: 60.696
}
,
{
   page: 17,
   name: "Check Box951", 
   isText: false,
   type: "checkbox",
   top: 47.128,
   left: 39.2729,
   width: 13.6800,
   height: 13.679
}
,
{
   page: 17,
   name: "Check Box952", 
   isText: false,
   type: "checkbox",
   top: 250.037,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box953", 
   isText: false,
   type: "checkbox",
   top: 306.329,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box954", 
   isText: false,
   type: "checkbox",
   top: 361.311,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box955", 
   isText: false,
   type: "checkbox",
   top: 401.893,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box956", 
   isText: false,
   type: "checkbox",
   top: 445.093,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box957", 
   isText: false,
   type: "checkbox",
   top: 494.184,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box958", 
   isText: false,
   type: "checkbox",
   top: 541.311,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box959", 
   isText: false,
   type: "checkbox",
   top: 596.948,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box960", 
   isText: false,
   type: "checkbox",
   top: 593.675,
   left: 170.837,
   width: 13.680,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box961", 
   isText: false,
   type: "checkbox",
   top: 593.021,
   left: 210.11,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box962", 
   isText: false,
   type: "checkbox",
   top: 593.021,
   left: 251.347,
   width: 13.680,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box963", 
   isText: false,
   type: "checkbox",
   top: 655.203,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 17,
   name: "Check Box964", 
   isText: false,
   type: "checkbox",
   top: 692.5121,
   left: 145.31,
   width: 13.68,
   height: 13.6800
}
,
{
   page: 17,
   name: "Check Box965", 
   isText: false,
   type: "checkbox",
   top: 721.3123,
   left: 145.31,
   width: 13.68,
   height: 13.6799
}
,
{
   page: 18,
   name: "e System is under a maintenance agreement pumpedinspected on a regular basis", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 67.12,
   left: 168.0,
   width: 425.16,
   height: 19.08
}
,
{
   page: 18,
   name: "i Maintenance agreement is mandated Name and contact information of entity that mandates the maintenance agreement i Maintenance agreement is not mandated", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 95.88,
   left: 168,
   width: 425.16,
   height: 40.68
}
,
{
   page: 18,
   name: "Other sanitary sewer service", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 149.2,
   left: 168.0,
   width: 425.16,
   height: 33.0
}
,
{
   page: 18,
   name: "TypeGray water storageuse", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 184.68,
   left: 168,
   width: 425.16,
   height: 40.20
}
,
{
   page: 18,
   name: "Gray water storageuse6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 226.44,
   left: 33.12,
   width: 105.24,
   height: 26.52
}
,
{
   page: 18,
   name: "Type6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 226.44,
   left: 167.76,
   width: 425.64,
   height: 26.52
}
,
{
   page: 18,
   name: "CommentsLeaks backups or other similar problems with any portion of the sewage systems or damage therefrom", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 328.2,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 18,
   name: "CommentsLift station sewage ejector pump", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 411.48,
   left: 168,
   width: 425.16,
   height: 53.88
}
,
{
   page: 18,
   name: "Lift station sewage ejector pump9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 466.92,
   left: 33.12,
   width: 105.24,
   height: 26.52
}
,
{
   page: 18,
   name: "Comments9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 466.92,
   left: 167.76,
   width: 425.64,
   height: 26.52
}
,
{
   page: 18,
   name: "CommentsFlooding", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 586.44,
   left: 163.44,
   width: 429.72,
   height: 81.48
}
,
{
   page: 18,
   name: "CommentsDrainage", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 669.72,
   left: 163.44,
   width: 429.72,
   height: 79.80
}
,
{
   page: 18,
   name: "Check Box966", 
   isText: false,
   type: "checkbox",
   top: 49.746,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box967", 
   isText: false,
   type: "checkbox",
   top: 140.728,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box968", 
   isText: false,
   type: "checkbox",
   top: 187.855,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box969", 
   isText: false,
   type: "checkbox",
   top: 229.746,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box970", 
   isText: false,
   type: "checkbox",
   top: 47.128,
   left: 170.183,
   width: 13.680,
   height: 13.679
}
,
{
   page: 18,
   name: "Check Box971", 
   isText: false,
   type: "checkbox",
   top: 83.782,
   left: 170.837,
   width: 13.680,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box972", 
   isText: false,
   type: "checkbox",
   top: 331.201,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box973", 
   isText: false,
   type: "checkbox",
   top: 414.329,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box974", 
   isText: false,
   type: "checkbox",
   top: 469.311,
   left: 145.31,
   width: 13.68,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box975", 
   isText: false,
   type: "checkbox",
   top: 589.748,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 18,
   name: "Check Box976", 
   isText: false,
   type: "checkbox",
   top: 674.839,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Grading", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 47.52,
   left: 163.68,
   width: 430.18,
   height: 67.92
}
,
{
   page: 19,
   name: "Water intrusion in the basement crawl space or other parts of Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 116.88,
   left: 163.68,
   width: 430.18,
   height: 79.876
}
,
{
   page: 19,
   name: "5_7", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 270.338,
   left: 163.68,
   width: 430.18,
   height: 25.473
}
,
{
   page: 19,
   name: "Repairs made to control water intrusion in the basement crawl space or other parts of the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 200.16,
   left: 163.68,
   width: 430.18,
   height: 66.196
}
,
{
   page: 19,
   name: "Repairs made to control water intrusion in the basement crawl space or other parts of the Property6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 269.88,
   left: 33.12,
   width: 101.40,
   height: 26.52
}
,
{
   page: 19,
   name: "CommentsDrainage or retention ponds dams storm water detention basins or other similar facilities", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 371.52,
   left: 159.36,
   width: 433.80,
   height: 88.80
}
,
{
   page: 19,
   name: "Drainage or retention ponds dams storm water detention basins or other similar facilities8", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 462.491,
   left: 33.12,
   width: 101.40,
   height: 31.549
}
,
{
   page: 19,
   name: "Comments8", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 461.836,
   left: 159.12,
   width: 434.28,
   height: 32.204
}
,
{
   page: 19,
   name: "CommentsIncluded fixtures and equipment", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 585.24,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 19,
   name: "CommentsStains on carpet", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.52,
   left: 168.655,
   width: 425.160,
   height: 81.48
}
,
{
   page: 19,
   name: "Check Box977", 
   isText: false,
   type: "checkbox",
   top: 51.055,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box978", 
   isText: false,
   type: "checkbox",
   top: 120.437,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box979", 
   isText: false,
   type: "checkbox",
   top: 204.219,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box980", 
   isText: false,
   type: "checkbox",
   top: 273.601,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box981", 
   isText: false,
   type: "checkbox",
   top: 375.711,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box982", 
   isText: false,
   type: "checkbox",
   top: 464.075,
   left: 141.382,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box983", 
   isText: false,
   type: "checkbox",
   top: 588.439,
   left: 144.655,
   width: 13.680,
   height: 13.680
}
,
{
   page: 19,
   name: "Check Box984", 
   isText: false,
   type: "checkbox",
   top: 672.221,
   left: 144.655,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "3_7", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 117.699,
   left: 168.011,
   width: 424.355,
   height: 24.578
}
,
{
   page: 20,
   name: "Floors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 47.52,
   left: 168.011,
   width: 424.355,
   height: 67.92
}
,
{
   page: 20,
   name: "Floors4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 117,
   left: 33.12,
   width: 105.24,
   height: 26.52
}
,
{
   page: 20,
   name: "CommentsZoning violation variance conditional use violation of an enforceable PUD or nonconforming use", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 261.48,
   left: 168,
   width: 425.16,
   height: 95.16
}
,
{
   page: 20,
   name: "CommentsNotice or threat of condemnation proceedings", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 358.44,
   left: 168,
   width: 425.16,
   height: 95.40
}
,
{
   page: 20,
   name: "CommentsNotice of any adverse conditions from any governmental or quasi governmental agency that have not been resolved", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 455.64,
   left: 168,
   width: 425.16,
   height: 95.40
}
,
{
   page: 20,
   name: "CommentsNotice of zoning action related to the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 552.84,
   left: 168,
   width: 425.16,
   height: 95.16
}
,
{
   page: 20,
   name: "CommentsBuilding code city or county violations", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 649.8,
   left: 168,
   width: 425.16,
   height: 95.4
}
,
{
   page: 20,
   name: "Check Box985", 
   isText: false,
   type: "checkbox",
   top: 52.854,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box986", 
   isText: false,
   type: "checkbox",
   top: 120.122,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box987", 
   isText: false,
   type: "checkbox",
   top: 267.471,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box988", 
   isText: false,
   type: "checkbox",
   top: 362.768,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box989", 
   isText: false,
   type: "checkbox",
   top: 461.267,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box990", 
   isText: false,
   type: "checkbox",
   top: 556.564,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 20,
   name: "Check Box991", 
   isText: false,
   type: "checkbox",
   top: 654.263,
   left: 145.748,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "CommentsViolation of restrictive covenants or owners association rules or regulations", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 65.64,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsAny building or improvements constructed within the past one year before this Date without approval by the owners association or its designated approving body", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 148.92,
   left: 168,
   width: 425.16,
   height: 99.24
}
,
{
   page: 21,
   name: "CommentsAny additions or alterations made with a Building Permit", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249.96,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsAny additions or non aesthetic alterations made without a Building Permit", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 333.24,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsOther legal action", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.52,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsAny part of the Property leased to others written or oral", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 499.8,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsUsed for shortterm rentals in the past year", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 583.08,
   left: 168,
   width: 425.16,
   height: 81.48
}
,
{
   page: 21,
   name: "CommentsGrandfathered conditions or uses", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 666.36,
   left: 168,
   width: 425.16,
   height: 53.88
}
,
{
   page: 21,
   name: "Grandfathered conditions or uses14", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 721.8,
   left: 33.12,
   width: 105.72,
   height: 26.76
}
,
{
   page: 21,
   name: "Comments14", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 721.8,
   left: 167.76,
   width: 425.64,
   height: 26.76
}
,
{
   page: 21,
   name: "Check Box992", 
   isText: false,
   type: "checkbox",
   top: 69.671,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box993", 
   isText: false,
   type: "checkbox",
   top: 152.154,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box994", 
   isText: false,
   type: "checkbox",
   top: 253.056,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box995", 
   isText: false,
   type: "checkbox",
   top: 336.341,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box996", 
   isText: false,
   type: "checkbox",
   top: 421.227,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box997", 
   isText: false,
   type: "checkbox",
   top: 504.511,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box998", 
   isText: false,
   type: "checkbox",
   top: 587.796,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box999", 
   isText: false,
   type: "checkbox",
   top: 669.478,
   left: 144.947,
   width: 13.680,
   height: 13.680
}
,
{
   page: 21,
   name: "Check Box1000", 
   isText: false,
   type: "checkbox",
   top: 723.9335,
   left: 144.947,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 22,
   name: "CommentsAny access problems issues or concerns", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 109.56,
   left: 159.36,
   width: 433.32,
   height: 81.48
}
,
{
   page: 22,
   name: "CommentsRoads trails paths or driveways through the Property used by others", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 192.84,
   left: 159.36,
   width: 433.32,
   height: 67.80
}
,
{
   page: 22,
   name: "CommentsPublic highway or county road bordering the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 262.44,
   left: 159.36,
   width: 433.32,
   height: 81.48
}
,
{
   page: 22,
   name: "CommentsAny proposed or existing transportation project that affects or is expected to affect the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 345.72,
   left: 159.36,
   width: 433.32,
   height: 67.56
}
,
{
   page: 22,
   name: "CommentsEncroachments boundary disputes or unrecorded easements", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 415.08,
   left: 159.36,
   width: 433.32,
   height: 67.80
}
,
{
   page: 22,
   name: "CommentsShared or common areas with adjoining properties including but not limited to walls fences and driveways", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 484.68,
   left: 159.36,
   width: 433.32,
   height: 81.48
}
,
{
   page: 22,
   name: "CommentsRequirements for curb gravelpaving or landscaping", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 567.96,
   left: 159.36,
   width: 433.32,
   height: 67.56
}
,
{
   page: 22,
   name: "CommentsAny limitations on parking or access due to size number of vehicles or type of vehicles in the past year", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 637.32,
   left: 159.36,
   width: 433.32,
   height: 67.80
}
,
{
   page: 22,
   name: "Any limitations on parking or access due to size number of vehicles or type of vehicles in the past year9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 706.92,
   left: 33.36,
   width: 100.92,
   height: 40.20
}
,
{
   page: 22,
   name: "Comments9_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 706.92,
   left: 159.36,
   width: 433.32,
   height: 40.20
}
,
{
   page: 22,
   name: "Check Box1001", 
   isText: false,
   type: "checkbox",
   top: 115.317,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1002", 
   isText: false,
   type: "checkbox",
   top: 196.199,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1003", 
   isText: false,
   type: "checkbox",
   top: 265.869,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1004", 
   isText: false,
   type: "checkbox",
   top: 349.955,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1005", 
   isText: false,
   type: "checkbox",
   top: 418.023,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1006", 
   isText: false,
   type: "checkbox",
   top: 488.495,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1007", 
   isText: false,
   type: "checkbox",
   top: 570.978,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1008", 
   isText: false,
   type: "checkbox",
   top: 639.848,
   left: 139.341,
   width: 13.680,
   height: 13.680
}
,
{
   page: 22,
   name: "Check Box1009", 
   isText: false,
   type: "checkbox",
   top: 711.9213,
   left: 139.341,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 23,
   name: "CommentsHazardous materials on the Property such as radioactive toxic or biohazardous materials asbestos pesticides herbicides wastewater sludge methane mill tailings solvents or petroleum products", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.04,
   left: 168.48,
   width: 424.68,
   height: 88.20
}
,
{
   page: 23,
   name: "CommentsUnderground storage tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 224.04,
   left: 168.48,
   width: 424.68,
   height: 40.20
}
,
{
   page: 23,
   name: "CommentsAboveground storage tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 266.04,
   left: 168.48,
   width: 424.68,
   height: 39.96
}
,
{
   page: 23,
   name: "CommentsUnderground transmission lines", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 307.8,
   left: 168.48,
   width: 424.68,
   height: 40.2
}
,
{
   page: 23,
   name: "CommentsProperty used as situated on or adjoining a dump landfill or municipal solid waste landfill", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 349.8,
   left: 168.48,
   width: 424.68,
   height: 43.8
}
,
{
   page: 23,
   name: "CommentsMonitoring wells or test equipment", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 395.4,
   left: 168.48,
   width: 424.68,
   height: 40.2
}
,
{
   page: 23,
   name: "CommentsSliding settling upheaval movement or instability of earth or expansive soils on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.4,
   left: 168.48,
   width: 424.68,
   height: 67.8
}
,
{
   page: 23,
   name: "CommentsMine shafts tunnels or abandoned wells on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 507,
   left: 168.48,
   width: 424.68,
   height: 39.96
}
,
{
   page: 23,
   name: "CommentsWithin a governmentally designated geological hazard or sensitive area", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 548.76,
   left: 168.48,
   width: 424.68,
   height: 40.20
}
,
{
   page: 23,
   name: "CommentsWithin a governmentally designated floodplain or wetland area", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 590.76,
   left: 168.48,
   width: 424.68,
   height: 39.96
}
,
{
   page: 23,
   name: "CommentsDead diseased or infested trees or shrubs", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 632.52,
   left: 168.48,
   width: 424.68,
   height: 40.20
}
,
{
   page: 23,
   name: "CommentsEnvironmental assessments studies or reports done involving the physical condition of the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 674.52,
   left: 168.48,
   width: 424.68,
   height: 67.56
}
,
{
   page: 23,
   name: "Check Box1010", 
   isText: false,
   type: "checkbox",
   top: 138.54,
   left: 148.15,
   width: 13.68,
   height: 13.68
}
,
{
   page: 23,
   name: "Check Box1011", 
   isText: false,
   type: "checkbox",
   top: 227.43,
   left: 148.15,
   width: 13.68,
   height: 13.68
}
,
{
   page: 23,
   name: "Check Box1012", 
   isText: false,
   type: "checkbox",
   top: 269.873,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1013", 
   isText: false,
   type: "checkbox",
   top: 312.316,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1014", 
   isText: false,
   type: "checkbox",
   top: 353.959,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1015", 
   isText: false,
   type: "checkbox",
   top: 399.605,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1016", 
   isText: false,
   type: "checkbox",
   top: 441.247,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1017", 
   isText: false,
   type: "checkbox",
   top: 511.718,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1018", 
   isText: false,
   type: "checkbox",
   top: 552.56,
   left: 148.15,
   width: 13.68,
   height: 13.68
}
,
{
   page: 23,
   name: "Check Box1019", 
   isText: false,
   type: "checkbox",
   top: 594.202,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1020", 
   isText: false,
   type: "checkbox",
   top: 635.043,
   left: 148.15,
   width: 13.68,
   height: 13.680
}
,
{
   page: 23,
   name: "Check Box1021", 
   isText: false,
   type: "checkbox",
   top: 679.889,
   left: 148.15,
   width: 13.68,
   height: 13.6798
}
,
{
   page: 24,
   name: "Used for any mining graveling or other natural resource extraction operations such as oil and gas wells", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 47.4,
   left: 169.004,
   width: 424.616,
   height: 55.8
}
,
{
   page: 24,
   name: "Smoking inside improvements including garages unfinished space or detached buildings on Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 104.52,
   left: 169.004,
   width: 424.616,
   height: 68.04
}
,
{
   page: 24,
   name: "Animals kept in the residence", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 173.88,
   left: 169.004,
   width: 424.616,
   height: 68.28
}
,
{
   page: 24,
   name: "Other environmental problems issues or concerns", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 243.48,
   left: 169.004,
   width: 424.616,
   height: 54.36
}
,
{
   page: 24,
   name: "17_4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.058,
   left: 169.004,
   width: 424.616,
   height: 24.644
}
,
{
   page: 24,
   name: "Odors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 299.16,
   left: 169.004,
   width: 424.616,
   height: 54.36
}
,
{
   page: 24,
   name: "Odors18", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.08,
   left: 33.12,
   width: 110.28,
   height: 26.52
}
,
{
   page: 24,
   name: "CommentsRadon tests conducted on the Property Provide copies of the most recent records and reports pertaining to radon concentrations within the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 451.8,
   left: 163.92,
   width: 429.24,
   height: 95.16
}
,
{
   page: 24,
   name: "CommentsRadon concentrations detected or mitigation or remediation performed Provide a full description", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 548.76,
   left: 163.92,
   width: 429.24,
   height: 95.40
}
,
{
   page: 24,
   name: "CommentsRadon mitigation system installed on Property Provide all information known by Seller about the radon mitigation system", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 645.96,
   left: 163.92,
   width: 429.24,
   height: 67.56
}
,
{
   page: 24,
   name: "Radon mitigation system installed on Property Provide all information known by Seller about the radon mitigation system4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 715.08,
   left: 33.12,
   width: 105.72,
   height: 26.52
}
,
{
   page: 24,
   name: "Comments4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 715.08,
   left: 163.68,
   width: 429.72,
   height: 26.52
}
,
{
   page: 24,
   name: "Check Box1022", 
   isText: false,
   type: "checkbox",
   top: 52.854,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1023", 
   isText: false,
   type: "checkbox",
   top: 108.91,
   left: 148.951,
   width: 13.680,
   height: 13.68
}
,
{
   page: 24,
   name: "Check Box1024", 
   isText: false,
   type: "checkbox",
   top: 178.581,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1025", 
   isText: false,
   type: "checkbox",
   top: 247.451,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1026", 
   isText: false,
   type: "checkbox",
   top: 303.507,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1027", 
   isText: false,
   type: "checkbox",
   top: 357.162,
   left: 148.951,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1028", 
   isText: false,
   type: "checkbox",
   top: 457.263,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1029", 
   isText: false,
   type: "checkbox",
   top: 551.759,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1030", 
   isText: false,
   type: "checkbox",
   top: 650.259,
   left: 143.544,
   width: 13.680,
   height: 13.680
}
,
{
   page: 24,
   name: "Check Box1031", 
   isText: false,
   type: "checkbox",
   top: 716.7261,
   left: 143.544,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 25,
   name: "CommentsProperty is part of an owners association", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 121.44,
   left: 194.52,
   width: 399.00,
   height: 22.32
}
,
{
   page: 25,
   name: "CommentsSpecial assessments or increases in regular assessments approved by owners association but not yet implemented", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 145.56,
   left: 194.88,
   width: 398.28,
   height: 81.48
}
,
{
   page: 25,
   name: "CommentsProblems or defects in the common elements or limited common elements of the Association Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 228.84,
   left: 194.88,
   width: 398.28,
   height: 67.80
}
,
{
   page: 25,
   name: "CommentsHas the Association made demand or commenced a lawsuit against a builder or contractor alleging defective construction of improvements of the Association Property common area or property owned or controlled by the Association but outside the Sellers Property or unit", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 370.2,
   left: 194.88,
   width: 398.28,
   height: 107.88
}
,
{
   page: 25,
   name: "Has the Association made demand or commenced a lawsuit against a builder or contractor alleging defective construction of improvements of the Association Property common area or property owned or controlled by the Association but outside the Sellers Property or unit5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 479.64,
   left: 36.24,
   width: 134.28,
   height: 26.52
}
,
{
   page: 25,
   name: "Comments5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 479.64,
   left: 194.64,
   width: 398.76,
   height: 26.52
}
,
{
   page: 25,
   name: "Contact InformationOwners Association 1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 586.68,
   left: 194.88,
   width: 398.28,
   height: 53.88
}
,
{
   page: 25,
   name: "Contact InformationOwners Association 2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 642.36,
   left: 194.88,
   width: 398.28,
   height: 47.16
}
,
{
   page: 25,
   name: "Contact InformationOwners Association 3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 691.08,
   left: 194.64,
   width: 398.76,
   height: 26.52
}
,
{
   page: 25,
   name: "Contact InformationOwners Association 4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 719.16,
   left: 194.64,
   width: 398.76,
   height: 26.52
}
,
{
   page: 25,
   name: "Check Box1032", 
   isText: false,
   type: "checkbox",
   top: 124.927,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1033", 
   isText: false,
   type: "checkbox",
   top: 149.752,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1034", 
   isText: false,
   type: "checkbox",
   top: 233.036,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1036", 
   isText: false,
   type: "checkbox",
   top: 374.78,
   left: 175.179,
   width: 13.680,
   height: 13.68
}
,
{
   page: 25,
   name: "Check Box1037", 
   isText: false,
   type: "checkbox",
   top: 482.889,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1038", 
   isText: false,
   type: "checkbox",
   top: 590.999,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1039", 
   isText: false,
   type: "checkbox",
   top: 645.454,
   left: 175.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 25,
   name: "Check Box1040", 
   isText: false,
   type: "checkbox",
   top: 694.3034,
   left: 175.179,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 25,
   name: "Check Box1041", 
   isText: false,
   type: "checkbox",
   top: 722.3318,
   left: 175.179,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 26,
   name: "CommentsProperty is located within the boundaries of a Metropolitan District that was organized on or after January 1 2000", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 121.08,
   left: 163.92,
   width: 429.24,
   height: 54.84
}
,
{
   page: 26,
   name: "CommentsName of Metropolitan District 1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 200.76,
   left: 163.92,
   width: 429.24,
   height: 40.20
}
,
{
   page: 26,
   name: "CommentsOfficial website of the Metropolitan District 1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 242.76,
   left: 163.92,
   width: 429.24,
   height: 40.20
}
,
{
   page: 26,
   name: "CommentsName of Metropolitan District 2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 284.52,
   left: 163.68,
   width: 429.72,
   height: 26.52
}
,
{
   page: 26,
   name: "CommentsOfficial website of Metropolitan District 2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 312.6,
   left: 163.68,
   width: 429.72,
   height: 26.52
}
,
{
   page: 26,
   name: "CommentsWritten reports of any building site roofing soils water sewer mold or engineering investigations or studies of the Property Provide copies of all such reports in possession of Seller", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.28,
   left: 163.92,
   width: 429.24,
   height: 88.20
}
,
{
   page: 26,
   name: "CommentsAny property insurance claim submitted whether paid or not", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 506.28,
   left: 163.92,
   width: 429.24,
   height: 67.80
}
,
{
   page: 26,
   name: "CommentsStructural architectural and engineering plans andor specifications for any existing improvements Provide copies of all such reports in possession of Seller", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 575.88,
   left: 163.92,
   width: 429.24,
   height: 95.16
}
,
{
   page: 26,
   name: "CommentsProperty was previously used as a methamphetamine laboratory and not remediated to state standards", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 672.84,
   left: 163.92,
   width: 429.24,
   height: 67.80
}
,
{
   page: 26,
   name: "Check Box1042", 
   isText: false,
   type: "checkbox",
   top: 125.727,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1043", 
   isText: false,
   type: "checkbox",
   top: 205.008,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1044", 
   isText: false,
   type: "checkbox",
   top: 245.849,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1045", 
   isText: false,
   type: "checkbox",
   top: 287.491,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1046", 
   isText: false,
   type: "checkbox",
   top: 314.719,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1047", 
   isText: false,
   type: "checkbox",
   top: 420.426,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1048", 
   isText: false,
   type: "checkbox",
   top: 509.316,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1049", 
   isText: false,
   type: "checkbox",
   top: 580.588,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 26,
   name: "Check Box1050", 
   isText: false,
   type: "checkbox",
   top: 676.686,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Government special improvements approved but not yet installed that may become a lien against the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 59.4,
   left: 164.444,
   width: 428.543,
   height: 55.8
}
,
{
   page: 27,
   name: "Any litigation alleging negligent construction or defective building products", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 116.52,
   left: 164.444,
   width: 428.543,
   height: 54.36
}
,
{
   page: 27,
   name: "Any award or payment of money in lieu of repairs for defective building products or poor construction", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 172.2,
   left: 164.444,
   width: 428.543,
   height: 68.04
}
,
{
   page: 27,
   name: "Any release signed regarding defective products or poor construction that would limit a future owner from making a claim", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 241.56,
   left: 164.444,
   width: 428.543,
   height: 68.28
}
,
{
   page: 27,
   name: "Pending 1 litigation or 2 other dispute resolution proceeding regarding the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 311.16,
   left: 164.444,
   width: 428.543,
   height: 54.36
}
,
{
   page: 27,
   name: "Property is subject to Deed Restrictions other recorded document restrictions or Affordable Housing Restrictions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 366.84,
   left: 164.444,
   width: 428.543,
   height: 68.04
}
,
{
   page: 27,
   name: "Property is located in a historic district", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 436.2,
   left: 164.444,
   width: 428.543,
   height: 54.6
}
,
{
   page: 27,
   name: "Property is located in a historic district12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 492.6,
   left: 33.36,
   width: 105.24,
   height: 39.96
}
,
{
   page: 27,
   name: "GENERAL  Other Information", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 492.763,
   left: 164.444,
   width: 428.543,
   height: 39.011
}
,
{
   page: 27,
   name: "13_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 585.066,
   left: 181.837,
   width: 410.740,
   height: 11.814
}
,
{
   page: 27,
   name: "US Postal Service delivery available", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 609.677,
   left: 163.92,
   width: 429.24,
   height: 50.694
}
,
{
   page: 27,
   name: "US Postal Service delivery available14", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.48,
   left: 33.36,
   width: 105.24,
   height: 81.48
}
,
{
   page: 27,
   name: "i Property i Post Office i Cluster MailboxLocation and No i Other specify14", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.48,
   left: 163.92,
   width: 429.24,
   height: 81.48
}
,
{
   page: 27,
   name: "Check Box1051", 
   isText: false,
   type: "checkbox",
   top: 63.264,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1052", 
   isText: false,
   type: "checkbox",
   top: 120.922,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1053", 
   isText: false,
   type: "checkbox",
   top: 176.979,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1054", 
   isText: false,
   type: "checkbox",
   top: 245.849,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1055", 
   isText: false,
   type: "checkbox",
   top: 313.918,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1056", 
   isText: false,
   type: "checkbox",
   top: 369.174,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1057", 
   isText: false,
   type: "checkbox",
   top: 438.845,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1058", 
   isText: false,
   type: "checkbox",
   top: 495.702,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1059", 
   isText: false,
   type: "checkbox",
   top: 565.373,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1060", 
   isText: false,
   type: "checkbox",
   top: 561.369,
   left: 165.768,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1061", 
   isText: false,
   type: "checkbox",
   top: 562.17,
   left: 230.032,
   width: 13.680,
   height: 13.68
}
,
{
   page: 27,
   name: "Check Box1062", 
   isText: false,
   type: "checkbox",
   top: 574.983,
   left: 165.768,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1063", 
   isText: false,
   type: "checkbox",
   top: 596.604,
   left: 165.768,
   width: 13.680,
   height: 13.680
}
,
{
   page: 27,
   name: "Check Box1064", 
   isText: false,
   type: "checkbox",
   top: 666.275,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 28,
   name: "Seller 1 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 549.64,
   left: 43.5274,
   width: 374.4326,
   height: 16.571
}
,
{
   page: 28,
   name: "Seller 2 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 595.6,
   left: 43.5274,
   width: 374.4326,
   height: 16.571
}
,
{
   page: 28,
   name: "Seller 3 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 641.44,
   left: 43.5274,
   width: 374.4326,
   height: 16.571
}
,
{
   page: 28,
   name: "Seller 4 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 687.52,
   left: 43.5274,
   width: 374.4326,
   height: 16.5712
}
,
{
   page: 28,
   name: "Text326", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 83.128,
   left: 23.5637,
   width: 552.5473,
   height: 393.129
}
] }
