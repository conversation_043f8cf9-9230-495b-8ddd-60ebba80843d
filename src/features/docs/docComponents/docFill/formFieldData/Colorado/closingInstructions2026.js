export function closingInstructions2026() {
return [   // Release 2025-12-10 19:04:49
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "<PERSON><PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 279.32,
   left: 49.92,
   width: 479.88,
   height: 12.513
}
,
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 238.24,
   left: 465.36,
   width: 120.00,
   height: 12.513
}
,
{
   page: 0,
   name: "Buyer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 295.6,
   left: 66.84,
   width: 470.28,
   height: 12.513
}
,
{
   page: 0,
   name: "Title Company Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 312.88,
   left: 80.52,
   width: 315.00,
   height: 12.513
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 353.44,
   left: 107.4,
   width: 459.96,
   height: 12.513
}
,
{
   page: 0,
   name: "Date Contract Date", 
   fontSize: 9,
   type: "date",
   top: 382.24,
   left: 252.24,
   width: 114.96,
   height: 12.513
}
,
{
   page: 0,
   name: "Check Box115", 
   isText: false,
   type: "checkbox",
   top: 464.075,
   left: 131.564,
   width: 10.080,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box116", 
   isText: false,
   type: "checkbox",
   top: 464.075,
   left: 191.128,
   width: 10.080,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box117", 
   isText: false,
   type: "checkbox",
   top: 495.493,
   left: 169.528,
   width: 10.080,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box118", 
   isText: false,
   type: "checkbox",
   top: 496.148,
   left: 231.056,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Closing Fee", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 112.931,
   left: 323.52,
   width: 100.08,
   height: 12.469
}
,
{
   page: 1,
   name: "Check Box112", 
   isText: false,
   type: "checkbox",
   top: 228.437,
   left: 72.0003,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box113", 
   isText: false,
   type: "checkbox",
   top: 246.765,
   left: 72.0003,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box114", 
   isText: false,
   type: "checkbox",
   top: 264.438,
   left: 72.0003,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 2,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 651.84,
   left: 314.04,
   width: 259.92,
   height: 14.04
}
,
{
   page: 2,
   name: "Text111", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 236.292,
   left: 43.8547,
   width: 544.0383,
   height: 268.765
}
,
{
   page: 3,
   name: "Buyer 1 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 67.8,
   left: 108.72,
   width: 469.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 1 Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 105.84,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Buyer 1 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.6,
   left: 117.36,
   width: 170.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 1 Fax No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.6,
   left: 411.84,
   width: 169.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 1 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 135.6,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Buyer 2 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 158.52,
   left: 108.72,
   width: 469.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 2 Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 196.56,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Buyer 2 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 211.56,
   left: 117.36,
   width: 170.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 2 Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 211.56,
   left: 411.84,
   width: 169.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 2 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 226.56,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Buyer 3 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249.48,
   left: 108.72,
   width: 469.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Buyer 4 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 291.48,
   left: 108.72,
   width: 469.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 1 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 354.84,
   left: 107.52,
   width: 469.80,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 1 Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 392.88,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Seller 1 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 407.64,
   left: 117.36,
   width: 170.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 1 Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 407.64,
   left: 411.84,
   width: 169.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 1 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 422.76,
   left: 117.36,
   width: 465.00,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 2 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 445.8,
   left: 107.52,
   width: 469.80,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 2 Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 483.72,
   left: 117.36,
   width: 465.00,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 2 Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 498.6,
   left: 117.36,
   width: 170.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 2 Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 498.6,
   left: 411.84,
   width: 169.92,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 2 Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 513.6,
   left: 117.36,
   width: 465.00,
   height: 14.04
}
,
{
   page: 3,
   name: "Seller 3 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 536.52,
   left: 107.52,
   width: 469.80,
   height: 14.16
}
,
{
   page: 3,
   name: "Seller 4 Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 578.52,
   left: 107.52,
   width: 470.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Title Company Name2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 643.08,
   left: 155.4,
   width: 426.0,
   height: 14.16
}
,
{
   page: 3,
   name: "Title", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 679.8,
   left: 71.28,
   width: 509.52,
   height: 14.16
}
,
{
   page: 3,
   name: "Title Company Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 695.4,
   left: 117.36,
   width: 465.00,
   height: 14.16
}
,
{
   page: 3,
   name: "Title Company Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 711.24,
   left: 117.36,
   width: 174.96,
   height: 14.16
}
,
{
   page: 3,
   name: "Title Company Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 711.24,
   left: 441.36,
   width: 140.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Title Company Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 726.24,
   left: 117.36,
   width: 465.00,
   height: 13.08
}
] }
