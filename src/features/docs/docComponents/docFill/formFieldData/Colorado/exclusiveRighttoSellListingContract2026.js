export function exclusiveRighttoSellListingContract2026() {
return [   // Release 2025-12-07 13:36:08
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 299.12,
   left: 451.56,
   width: 120.00,
   height: 11.564
}
,
{
   page: 0,
   name: "Firm to serve as the broker of Seller and to perform the services for <PERSON><PERSON> required by this Seller Listing Contract If more than", 
   isText: false,
   type: "checkbox",
   top: 376.2,
   left: 81.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 0,
   name: "References in this Seller Listing Contract to Broker or Brokerage Firm mean both the licensed person and brokerage firm who", 
   isText: false,
   type: "checkbox",
   top: 436.68,
   left: 81.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 0,
   name: "<PERSON><PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 490.88,
   left: 113.88,
   width: 450.00,
   height: 11.564
}
,
{
   page: 0,
   name: "Brokerage Firm", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 507.28,
   left: 158.64,
   width: 408.60,
   height: 11.564
}
,
{
   page: 0,
   name: "Broker Seller Fullname", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 525.8,
   left: 120.0,
   width: 447.24,
   height: 11.564
}
,
{
   page: 0,
   name: "County", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 542.32,
   left: 425.629,
   width: 141.491,
   height: 11.564
}
,
{
   page: 0,
   name: "Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 728.0618,
   left: 102.96,
   width: 459.96,
   height: 12.2182
}
,
{
   page: 0,
   name: "Check Box3", 
   isText: false,
   type: "checkbox",
   top: 275.874,
   left: 160.233,
   width: 9.557,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box4", 
   isText: false,
   type: "checkbox",
   top: 277.529,
   left: 282.765,
   width: 10.080,
   height: 10.080
}
,
{
   page: 0,
   name: "Legal Description", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 567.493,
   left: 42.5456,
   width: 533.5654,
   height: 158.1465
}
,
{
   page: 1,
   name: "IS part of an affordable housing program If this box is NOT checked Seller represents that Property is NOT part of an", 
   isText: false,
   type: "checkbox",
   top: 67.8,
   left: 81.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Lease means any agreement between the Seller and a tenant to create a tenancy or leasehold interest in the Property The", 
   isText: false,
   type: "checkbox",
   top: 154.2,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Date Listing Begins", 
   fontSize: 9,
   type: "date",
   top: 199.68,
   left: 388.92,
   width: 180.00,
   height: 12.36
}
,
{
   page: 1,
   name: "and any written extensions", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 226.32,
   left: 67.92,
   width: 390.00,
   height: 12.36
}
,
{
   page: 1,
   name: "eg three days after MEC the first day is excluded and the last day is included If any deadline falls on a Saturday Sunday", 
   isText: false,
   type: "checkbox",
   top: 368.52,
   left: 374.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "not a Saturday Sunday or Holiday Should neither box be checked the deadline will not be extended", 
   isText: false,
   type: "checkbox",
   top: 368.52,
   left: 322.2,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "buyer as a customer", 
   isText: false,
   type: "checkbox",
   top: 593.64,
   left: 117.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "existence of offers on the Property and whether the offers were obtained by Broker a broker within Brokerage Firm or by", 
   isText: false,
   type: "checkbox",
   top: 323.88,
   left: 113.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Does Not consent to Brokers disclosure to prospective buyers and cooperating brokers the", 
   isText: false,
   type: "checkbox",
   top: 323.88,
   left: 168.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Sale Compensation Percent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 504.96,
   left: 188.76,
   width: 64.92,
   height: 12.36
}
,
{
   page: 2,
   name: "Sale Compensation Dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 504.96,
   left: 404.28,
   width: 99.96,
   height: 12.36
}
,
{
   page: 2,
   name: "Buyer Broker Compensation Percent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 548.4,
   left: 248.52,
   width: 54.96,
   height: 12.36
}
,
{
   page: 2,
   name: "Buyer Broker Compensation Dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 548.4,
   left: 452.64,
   width: 90.00,
   height: 12.36
}
,
{
   page: 2,
   name: "Lease Compensation Percent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 614.4,
   left: 504.96,
   width: 53.454,
   height: 12.36
}
,
{
   page: 2,
   name: "Lease Compensation Dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 627.6,
   left: 218.56,
   width: 65.04,
   height: 10.68
}
,
{
   page: 2,
   name: "undefined_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 639.36,
   left: 67.68,
   width: 499.92,
   height: 12.36
}
,
{
   page: 2,
   name: "Brokerage Firm agrees to contribute from the Lease Compensation to tenants brokerage firm an amount of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 652.8,
   left: 505.8,
   width: 50.04,
   height: 12.36
}
,
{
   page: 2,
   name: "of the gross rent or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 666,
   left: 152.76,
   width: 64.92,
   height: 12.36
}
,
{
   page: 2,
   name: "Text9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 706.9122,
   left: 58.2548,
   width: 514.5832,
   height: 35.0910
}
,
{
   page: 3,
   name: "Any Sale or Lease if  362 is checked of the Property within", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 76.92,
   left: 368.16,
   width: 44.04,
   height: 14.16
}
,
{
   page: 3,
   name: "Will", 
   isText: false,
   type: "checkbox",
   top: 107.88,
   left: 423.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not owe the", 
   isText: false,
   type: "checkbox",
   top: 107.88,
   left: 474.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will_2", 
   isText: false,
   type: "checkbox",
   top: 342.6,
   left: 162.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will_3", 
   isText: false,
   type: "checkbox",
   top: 358.2,
   left: 162.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be submitted to one or more MLS and", 
   isText: false,
   type: "checkbox",
   top: 342.6,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be submitted to one or more property information exchanges", 
   isText: false,
   type: "checkbox",
   top: 358.2,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will_4", 
   isText: false,
   type: "checkbox",
   top: 521.88,
   left: 198.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will_5", 
   isText: false,
   type: "checkbox",
   top: 538.92,
   left: 198.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be displayed on the Internet", 
   isText: false,
   type: "checkbox",
   top: 521.88,
   left: 252.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be displayed on the Internet_2", 
   isText: false,
   type: "checkbox",
   top: 538.92,
   left: 252.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Electronic Lock Box", 
   isText: false,
   type: "checkbox",
   top: 579,
   left: 104.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Manual Lock Box", 
   isText: false,
   type: "checkbox",
   top: 595.8,
   left: 104.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "undefined_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 608.52,
   left: 122.4,
   width: 444.96,
   height: 14.16
}
,
{
   page: 3,
   name: "undefined_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 625.44,
   left: 155.28,
   width: 411.96,
   height: 14.16
}
,
{
   page: 3,
   name: "Other than Broker Seller further authorizes the following persons to access the Property using the method described in", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 640.32,
   left: 68.4981,
   width: 498.7419,
   height: 14.16
}
,
{
   page: 3,
   name: "Licensed Appraisers", 
   isText: false,
   type: "checkbox",
   top: 691.32,
   left: 316.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Unlicensed Inspectors", 
   isText: false,
   type: "checkbox",
   top: 708.12,
   left: 316.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Actively Licensed Real Estate Brokers", 
   isText: false,
   type: "checkbox",
   top: 691.32,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Unlicensed Broker Assistants", 
   isText: false,
   type: "checkbox",
   top: 708.12,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Other", 
   isText: false,
   type: "checkbox",
   top: 725.16,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "undefined_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 720.84,
   left: 152.16,
   width: 414.96,
   height: 14.16
}
,
{
   page: 3,
   name: "Text6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 437.893,
   left: 51.7093,
   width: 523.7467,
   height: 67.818
}
,
{
   page: 3,
   name: "Check Box7", 
   isText: false,
   type: "checkbox",
   top: 613.312,
   left: 103.419,
   width: 10.080,
   height: 10.080
}
,
{
   page: 3,
   name: "Text10", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 738.3305,
   left: 110.619,
   width: 455.674,
   height: 21.3455
}
,
{
   page: 4,
   name: "Is", 
   isText: false,
   type: "checkbox",
   top: 426.6,
   left: 352.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Is Not currently a party to any listing", 
   isText: false,
   type: "checkbox",
   top: 426.6,
   left: 393,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Has", 
   isText: false,
   type: "checkbox",
   top: 440.04,
   left: 426.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Has Not received a", 
   isText: false,
   type: "checkbox",
   top: 440.04,
   left: 472.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "111 Price US", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 648.48,
   left: 137.64,
   width: 129.96,
   height: 12.84
}
,
{
   page: 4,
   name: "Cash", 
   isText: false,
   type: "checkbox",
   top: 667.08,
   left: 117.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Conventional", 
   isText: false,
   type: "checkbox",
   top: 667.08,
   left: 170.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "FHA", 
   isText: false,
   type: "checkbox",
   top: 667.08,
   left: 254.52,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "VA", 
   isText: false,
   type: "checkbox",
   top: 667.08,
   left: 299.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "undefined_6", 
   isText: false,
   type: "checkbox",
   top: 667.08,
   left: 340.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Other_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663,
   left: 392.88,
   width: 174.36,
   height: 14.04
}
,
{
   page: 4,
   name: "113 Loan Discount Points", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 679.08,
   left: 180.48,
   width: 386.76,
   height: 14.04
}
,
{
   page: 4,
   name: "114 Buyers Closing Costs FHAVA Seller must pay closing costs and fees not to exceed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.92,
   left: 446.88,
   width: 89.64,
   height: 14.04
}
,
{
   page: 4,
   name: "Buyer is not allowed by law to pay for tax service and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 710.04,
   left: 278.16,
   width: 288.96,
   height: 14.04
}
,
{
   page: 4,
   name: "115 Earnest Money Minimum amount of earnest money deposit is", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 725.76,
   left: 340.8,
   width: 84.12,
   height: 14.04
}
,
{
   page: 4,
   name: "submitted in the form of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 740.64,
   left: 158.28,
   width: 409.20,
   height: 14.04
}
,
{
   page: 4,
   name: "Text11", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 66.764,
   left: 51.0548,
   width: 521.1292,
   height: 128.037
}
,
{
   page: 4,
   name: "Text12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 219.274,
   left: 54.9821,
   width: 517.8559,
   height: 54.073
}
,
{
   page: 5,
   name: "Cashiers Check at Sellers expense", 
   isText: false,
   type: "checkbox",
   top: 58.92,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Funds Electronically Transferred Wire Transfer to an account specified by Seller at Sellers expense or", 
   isText: false,
   type: "checkbox",
   top: 74.76,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Closing Companys Trust Account Check", 
   isText: false,
   type: "checkbox",
   top: 90.6,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "IS a foreign person for purposes of US income taxation and authorizes", 
   isText: false,
   type: "checkbox",
   top: 132.12,
   left: 259.32,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "the following are owned by the Seller and included leased items should be listed under 1316 Leased Items", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 316.08,
   left: 341.16,
   width: 110.04,
   height: 10.68
}
,
{
   page: 5,
   name: "None", 
   isText: false,
   type: "checkbox",
   top: 340.92,
   left: 63.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Solar Panels", 
   isText: false,
   type: "checkbox",
   top: 340.92,
   left: 111.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Water Softeners", 
   isText: false,
   type: "checkbox",
   top: 340.92,
   left: 185.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Security Systems", 
   isText: false,
   type: "checkbox",
   top: 340.92,
   left: 279.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Satellite Systems including satellite", 
   isText: false,
   type: "checkbox",
   top: 340.92,
   left: 376.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Inclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 463.42,
   left: 55.6366,
   width: 523.0934,
   height: 101.855
}
,
{
   page: 5,
   name: "Encumbered Inclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 600.875,
   left: 56.2911,
   width: 522.4389,
   height: 49.491
}
,
{
   page: 5,
   name: "Leased Items", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 701.6758,
   left: 68.7275,
   width: 509.3475,
   height: 58.6547
}
,
{
   page: 6,
   name: "undefined_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 182.28,
   left: 326.16,
   width: 240.96,
   height: 12.60
}
,
{
   page: 6,
   name: "undefined_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 208.8,
   left: 305.28,
   width: 261.84,
   height: 12.48
}
,
{
   page: 6,
   name: "undefined_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 250.32,
   left: 58.68,
   width: 509.52,
   height: 12.60
}
,
{
   page: 6,
   name: "undefined_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 277.68,
   left: 58.68,
   width: 509.88,
   height: 12.60
}
,
{
   page: 6,
   name: "Deeded Water Rights The following legally described water rights are included", 
   isText: false,
   type: "checkbox",
   top: 311.16,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "deed at Closing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 414.84,
   left: 348.72,
   width: 155.64,
   height: 12.60
}
,
{
   page: 6,
   name: "Well Permit Number", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 430.68,
   left: 372,
   width: 195.12,
   height: 12.60
}
,
{
   page: 6,
   name: "Well Rights The Well Permit number of the included Well is", 
   isText: false,
   type: "checkbox",
   top: 433.32,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Water Stock The water stock included are as follows", 
   isText: false,
   type: "checkbox",
   top: 449.16,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Other Rights Relating to Water The following rights relating to water not included in  1351 1352 and", 
   isText: false,
   type: "checkbox",
   top: 523.08,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Exclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 90.328,
   left: 53.673,
   width: 519.165,
   height: 90.073
}
,
{
   page: 6,
   name: "Deeded Water Rights", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 322.038,
   left: 62.8366,
   width: 508.0384,
   height: 92.036
}
,
{
   page: 6,
   name: "Water Stock", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 460.802,
   left: 62.8366,
   width: 508.0384,
   height: 61.273
}
,
{
   page: 6,
   name: "Other Rights Relating to Water", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 549.166,
   left: 60.873,
   width: 510.656,
   height: 59.964
}
,
{
   page: 6,
   name: "Growing Crops", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 620.512,
   left: 54.3275,
   width: 516.5475,
   height: 70.436
}
,
{
   page: 7,
   name: "special warranty deed", 
   isText: false,
   type: "checkbox",
   top: 48.96,
   left: 59.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "general warranty deed", 
   isText: false,
   type: "checkbox",
   top: 48.96,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "bargain and sale deed", 
   isText: false,
   type: "checkbox",
   top: 48.96,
   left: 413.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "quit claim deed", 
   isText: false,
   type: "checkbox",
   top: 63.84,
   left: 59.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "personal representatives deed", 
   isText: false,
   type: "checkbox",
   top: 63.84,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "deed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 75.96,
   left: 77.76,
   width: 465.00,
   height: 12.60
}
,
{
   page: 7,
   name: "Is_2", 
   isText: false,
   type: "checkbox",
   top: 334.2,
   left: 346.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Is Not located within a common interest", 
   isText: false,
   type: "checkbox",
   top: 334.2,
   left: 383.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Association Assessment", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 370.32,
   left: 169.92,
   width: 114.96,
   height: 12.60
}
,
{
   page: 7,
   name: "Association Assessment Frequency", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 370.32,
   left: 302.64,
   width: 249.96,
   height: 12.60
}
,
{
   page: 7,
   name: "Special Assessments", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 399.84,
   left: 58.8,
   width: 510.0,
   height: 12.60
}
,
{
   page: 7,
   name: "Possession", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 498,
   left: 49.68,
   width: 519.96,
   height: 12.6
}
,
{
   page: 7,
   name: "Agrees", 
   isText: false,
   type: "checkbox",
   top: 643.32,
   left: 291.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Does Not Agree to provide on or before the sale", 
   isText: false,
   type: "checkbox",
   top: 643.32,
   left: 350.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Check Box8", 
   isText: false,
   type: "checkbox",
   top: 78.709,
   left: 59.5639,
   width: 10.0800,
   height: 9.426
}
,
{
   page: 7,
   name: "Tenancies", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 224.546,
   left: 52.3639,
   width: 520.4741,
   height: 44.255
}
,
{
   page: 7,
   name: "Text52", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 173.455,
   left: 53.0184,
   width: 513.2746,
   height: 12.837
}
,
{
   page: 8,
   name: "paid to Seller in its entirety", 
   isText: false,
   type: "checkbox",
   top: 383.88,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "divided between Brokerage Firm and Seller onehalf to Brokerage Firm but not to exceed the Brokerage Firm", 
   isText: false,
   type: "checkbox",
   top: 399.24,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "Other_3", 
   isText: false,
   type: "checkbox",
   top: 427.56,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "undefined_14", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 423.12,
   left: 128.16,
   width: 438.96,
   height: 14.40
}
,
{
   page: 9,
   name: "Additional Provisions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 322.692,
   left: 37.9638,
   width: 543.3832,
   height: 438.293
}
,
{
   page: 10,
   name: "electronic address of the recipient by facsimile email or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 270.96,
   left: 285.84,
   width: 276.72,
   height: 10.80
}
,
{
   page: 10,
   name: "Street Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 580.68,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "City State Zip", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 594.12,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Phone No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 607.56,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Fax No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 621,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Email Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 634.68,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Street Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 580.68,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "City State Zip_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 594.12,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Phone No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 607.56,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Fax No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 621,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Email Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 634.68,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 10,
   name: "Text23", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 43.2,
   left: 41.2365,
   width: 528.9835,
   height: 22.0
}
,
{
   page: 10,
   name: "Text24", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 78.546,
   left: 41.2365,
   width: 528.9835,
   height: 90.727
}
,
{
   page: 10,
   name: "Seller 1 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 514.475,
   left: 117.819,
   width: 186.000,
   height: 23.964
}
,
{
   page: 10,
   name: "Seller 2 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 513.82,
   left: 381.983,
   width: 186.001,
   height: 23.964
}
,
{
   page: 10,
   name: "Seller 3 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 674.839,
   left: 117.164,
   width: 186.000,
   height: 23.964
}
,
{
   page: 10,
   name: "Seller 4 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 674.839,
   left: 381.329,
   width: 186.000,
   height: 23.964
}
,
{
   page: 11,
   name: "Broker Seller Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 57.48,
   left: 119.28,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Date Broker Seller Signed", 
   fontSize: 9,
   type: "date",
   top: 95.88,
   left: 71.76,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Brokerage Firm Street Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 124.68,
   left: 184.68,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Brokerage Firm City State Zip", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 138.12,
   left: 184.68,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Broker Seller Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 151.56,
   left: 184.68,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Broker Seller Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 165,
   left: 184.68,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Broker Seller Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 178.68,
   left: 184.68,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "CoBroker Seller Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 213.72,
   left: 134.28,
   width: 349.92,
   height: 12.72
}
,
{
   page: 11,
   name: "Date CoBroker Seller Sign", 
   fontSize: 9,
   type: "date",
   top: 252.12,
   left: 71.76,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "signature Broker Seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 73.964,
   left: 124.055,
   width: 343.092,
   height: 15.454
}
,
{
   page: 11,
   name: "signature CoBroker Seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 230.401,
   left: 138.764,
   width: 343.092,
   height: 14.800
}
,
{
   page: 11,
   name: "Brokerage Firm Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 41.346,
   left: 119.28,
   width: 349.92,
   height: 12.720
}
] }
