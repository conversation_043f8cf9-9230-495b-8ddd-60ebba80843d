export function exclusiveRighttoSellListingContract2026CB() {
return [   // Release 2025-12-07 13:42:08
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 297.36,
   left: 451.56,
   width: 120.00,
   height: 13.56
}
,
{
   page: 0,
   name: "<PERSON><PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 489.84,
   left: 113.88,
   width: 450.00,
   height: 13.56
}
,
{
   page: 0,
   name: "Broker <PERSON>ller Full Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 525,
   left: 120,
   width: 447.24,
   height: 13.56
}
,
{
   page: 0,
   name: "<PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 542.52,
   left: 424.32,
   width: 142.80,
   height: 13.56
}
,
{
   page: 0,
   name: "Legal Description", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 568.148,
   left: 48.4366,
   width: 530.2924,
   height: 182.3643
}
,
{
   page: 0,
   name: "Check Box47", 
   isText: false,
   type: "checkbox",
   top: 276.219,
   left: 159.055,
   width: 10.080,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box48", 
   isText: false,
   type: "checkbox",
   top: 277.529,
   left: 282.11,
   width: 10.08,
   height: 10.080
}
,
{
   page: 1,
   name: "Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 40.32,
   left: 102.84,
   width: 459.96,
   height: 12.36
}
,
{
   page: 1,
   name: "IS part of an affordable housing program If this box is NOT checked Seller represents that Property is NOT part of an", 
   isText: false,
   type: "checkbox",
   top: 92.76,
   left: 81.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Lease means any agreement between the Seller and a tenant to create a tenancy or leasehold interest in the Property The", 
   isText: false,
   type: "checkbox",
   top: 179.4,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Date Listing Begins", 
   fontSize: 9,
   type: "date",
   top: 224.88,
   left: 388.92,
   width: 180.00,
   height: 12.36
}
,
{
   page: 1,
   name: "Listing Ends", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 250.625,
   left: 67.92,
   width: 390.00,
   height: 12.360
}
,
{
   page: 1,
   name: "not a Saturday Sunday or Holiday Should neither box be checked the deadline will not be extended", 
   isText: false,
   type: "checkbox",
   top: 393.48,
   left: 322.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "eg three days after MEC the first day is excluded and the last day is included If any deadline falls on a Saturday Sunday", 
   isText: false,
   type: "checkbox",
   top: 393.48,
   left: 374.52,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Seller Agency", 
   isText: false,
   type: "checkbox",
   top: 619.203,
   left: 118.473,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "doesConsent", 
   isText: false,
   type: "checkbox",
   top: 348.84,
   left: 114.6,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Does Not consent", 
   isText: false,
   type: "checkbox",
   top: 348.84,
   left: 170.52,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Sale Compensation Percent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 529.92,
   left: 191.76,
   width: 49.92,
   height: 12.36
}
,
{
   page: 2,
   name: "Sale Compensation Dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 529.92,
   left: 445.44,
   width: 99.96,
   height: 12.36
}
,
{
   page: 2,
   name: "of the gross purchase price or 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 679.68,
   left: 226.32,
   width: 45.00,
   height: 12.36
}
,
{
   page: 2,
   name: "in US dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 679.68,
   left: 424.08,
   width: 84.96,
   height: 12.36
}
,
{
   page: 3,
   name: "Lease Compensation Percent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 42,
   left: 504.96,
   width: 55.08,
   height: 10.68
}
,
{
   page: 3,
   name: "Lease Compensation Dollars", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 53.52,
   left: 224.76,
   width: 65.04,
   height: 10.68
}
,
{
   page: 3,
   name: "of the gross purchase price or 2_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 122.64,
   left: 298.08,
   width: 45.00,
   height: 10.68
}
,
{
   page: 3,
   name: "in", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 122.64,
   left: 498.84,
   width: 65.04,
   height: 10.68
}
,
{
   page: 3,
   name: "calendar days after the Listing Period", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 291.36,
   left: 369.12,
   width: 44.04,
   height: 10.68
}
,
{
   page: 3,
   name: "Will", 
   isText: false,
   type: "checkbox",
   top: 554.76,
   left: 162.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will_2", 
   isText: false,
   type: "checkbox",
   top: 570.36,
   left: 162.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be submitted to one or more MLS and", 
   isText: false,
   type: "checkbox",
   top: 554.76,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Will Not be submitted to one or more property information exchanges", 
   isText: false,
   type: "checkbox",
   top: 570.36,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Text31", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 649.966,
   left: 57.6003,
   width: 519.1647,
   height: 100.5463
}
,
{
   page: 3,
   name: "Text51", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 206.184,
   left: 61.8551,
   width: 513.2759,
   height: 33.782
}
,
{
   page: 4,
   name: "Will_3", 
   isText: false,
   type: "checkbox",
   top: 58.2,
   left: 198.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Will_4", 
   isText: false,
   type: "checkbox",
   top: 75.24,
   left: 198.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Will Not be displayed on the Internet", 
   isText: false,
   type: "checkbox",
   top: 58.2,
   left: 252.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Will Not be displayed on the Internet_2", 
   isText: false,
   type: "checkbox",
   top: 75.24,
   left: 252.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Electronic Lock Box", 
   isText: false,
   type: "checkbox",
   top: 115.32,
   left: 104.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Manual Lock Box", 
   isText: false,
   type: "checkbox",
   top: 132.12,
   left: 104.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "undefined_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 144.84,
   left: 122.4,
   width: 444.96,
   height: 14.16
}
,
{
   page: 4,
   name: "undefined_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 161.76,
   left: 155.28,
   width: 411.96,
   height: 14.16
}
,
{
   page: 4,
   name: "Other than Broker Seller further authorizes the following persons to access the Property using the method described in", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 176.64,
   left: 67.8435,
   width: 499.3965,
   height: 14.815
}
,
{
   page: 4,
   name: "Licensed Appraisers", 
   isText: false,
   type: "checkbox",
   top: 227.64,
   left: 316.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Unlicensed Inspectors", 
   isText: false,
   type: "checkbox",
   top: 244.44,
   left: 316.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Actively Licensed Real Estate Brokers", 
   isText: false,
   type: "checkbox",
   top: 227.64,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Unlicensed Broker Assistants", 
   isText: false,
   type: "checkbox",
   top: 244.44,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Other", 
   isText: false,
   type: "checkbox",
   top: 261.24,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Broker Marketing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.92,
   left: 152.16,
   width: 415.32,
   height: 14.16
}
,
{
   page: 4,
   name: "Is", 
   isText: false,
   type: "checkbox",
   top: 706.44,
   left: 351.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Is Not currently a party to any listing", 
   isText: false,
   type: "checkbox",
   top: 706.44,
   left: 393.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Has", 
   isText: false,
   type: "checkbox",
   top: 719.64,
   left: 425.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Has Not received a", 
   isText: false,
   type: "checkbox",
   top: 719.64,
   left: 472.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 4,
   name: "Check Box32", 
   isText: false,
   type: "checkbox",
   top: 149.892,
   left: 104.728,
   width: 10.080,
   height: 10.080
}
,
{
   page: 4,
   name: "Text33", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 346.565,
   left: 58.9093,
   width: 511.9657,
   height: 128.692
}
,
{
   page: 4,
   name: "Text34", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 497.457,
   left: 55.6366,
   width: 515.8924,
   height: 58.000
}
,
{
   page: 5,
   name: "Sale Price", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 222.96,
   left: 137.64,
   width: 129.96,
   height: 12.84
}
,
{
   page: 5,
   name: "Cash", 
   isText: false,
   type: "checkbox",
   top: 241.56,
   left: 117.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Conventional", 
   isText: false,
   type: "checkbox",
   top: 241.56,
   left: 170.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "FHA", 
   isText: false,
   type: "checkbox",
   top: 241.56,
   left: 254.52,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "VA", 
   isText: false,
   type: "checkbox",
   top: 241.56,
   left: 299.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "undefined_4", 
   isText: false,
   type: "checkbox",
   top: 241.56,
   left: 340.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Other_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 238.68,
   left: 392.88,
   width: 174.36,
   height: 12.84
}
,
{
   page: 5,
   name: "113 Loan Discount Points", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 254.76,
   left: 180.48,
   width: 386.76,
   height: 12.84
}
,
{
   page: 5,
   name: "114 Buyers Closing Costs FHAVA Seller must pay closing costs and fees not to exceed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 270.6,
   left: 446.88,
   width: 89.64,
   height: 12.84
}
,
{
   page: 5,
   name: "Buyer is not allowed by law to pay for tax service and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 285.72,
   left: 278.16,
   width: 288.96,
   height: 12.84
}
,
{
   page: 5,
   name: "115 Earnest Money Minimum amount of earnest money deposit is", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 301.44,
   left: 340.8,
   width: 84.12,
   height: 12.84
}
,
{
   page: 5,
   name: "Earnest Money Form", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 316.32,
   left: 158.28,
   width: 409.20,
   height: 12.84
}
,
{
   page: 5,
   name: "Cashiers Check at Sellers expense", 
   isText: false,
   type: "checkbox",
   top: 351.24,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Funds Electronically Transferred Wire Transfer to an account specified by Seller at Sellers expense or", 
   isText: false,
   type: "checkbox",
   top: 367.32,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Closing Companys Trust Account Check", 
   isText: false,
   type: "checkbox",
   top: 383.16,
   left: 59.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "IS a foreign person for purposes of US income taxation and authorizes", 
   isText: false,
   type: "checkbox",
   top: 424.68,
   left: 259.56,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "systems including accessories and garage door openers including", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 613.68,
   left: 341.16,
   width: 110.04,
   height: 12.36
}
,
{
   page: 5,
   name: "None", 
   isText: false,
   type: "checkbox",
   top: 643.8,
   left: 63.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Solar Panels", 
   isText: false,
   type: "checkbox",
   top: 643.8,
   left: 111.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Water Softeners", 
   isText: false,
   type: "checkbox",
   top: 643.8,
   left: 185.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Security Systems", 
   isText: false,
   type: "checkbox",
   top: 643.8,
   left: 279.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Satellite Systems including satellite", 
   isText: false,
   type: "checkbox",
   top: 643.8,
   left: 376.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "undefined_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 492.72,
   left: 326.16,
   width: 240.96,
   height: 12.48
}
,
{
   page: 6,
   name: "undefined_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 519.36,
   left: 305.28,
   width: 261.84,
   height: 12.48
}
,
{
   page: 6,
   name: "undefined_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 561,
   left: 58.68,
   width: 509.52,
   height: 12.48
}
,
{
   page: 6,
   name: "undefined_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 588.36,
   left: 58.68,
   width: 509.88,
   height: 12.48
}
,
{
   page: 6,
   name: "Water RightsWell Rights", 
   isText: false,
   type: "checkbox",
   top: 621.72,
   left: 104.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "deed at Closing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 725.52,
   left: 348.72,
   width: 155.64,
   height: 12.48
}
,
{
   page: 6,
   name: "Inclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 63.927,
   left: 52.3095,
   width: 523.0925,
   height: 101.855
}
,
{
   page: 6,
   name: "Encumbered Inclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 201.382,
   left: 52.964,
   width: 522.438,
   height: 49.491
}
,
{
   page: 6,
   name: "Leased Items", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 301.183,
   left: 65.4004,
   width: 509.3466,
   height: 52.764
}
,
{
   page: 6,
   name: "Exclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 400.056,
   left: 51.6552,
   width: 519.1648,
   height: 90.073
}
,
{
   page: 6,
   name: "Deeded Water Rights", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 632.403,
   left: 63.4003,
   width: 508.0387,
   height: 92.0361
}
,
{
   page: 7,
   name: "undefined_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 29.96,
   left: 372.0,
   width: 195.12,
   height: 12.84
}
,
{
   page: 7,
   name: "Well Rights The Well Permit number of the included Well is", 
   isText: false,
   type: "checkbox",
   top: 32.84,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Water Stock The water stock included are as follows", 
   isText: false,
   type: "checkbox",
   top: 48.68,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Other Rights Relating to Water The following rights relating to water not included in  1351 1352 and", 
   isText: false,
   type: "checkbox",
   top: 107.96,
   left: 99.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "special warranty deed", 
   isText: false,
   type: "checkbox",
   top: 317.96,
   left: 59.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "general warranty deed", 
   isText: false,
   type: "checkbox",
   top: 317.96,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "bargain and sale deed", 
   isText: false,
   type: "checkbox",
   top: 317.96,
   left: 413.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "quit claim deed", 
   isText: false,
   type: "checkbox",
   top: 332.84,
   left: 59.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "personal representatives deed", 
   isText: false,
   type: "checkbox",
   top: 332.84,
   left: 212.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "deed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 344.72,
   left: 77.76,
   width: 465.00,
   height: 12.84
}
,
{
   page: 7,
   name: "Is_2", 
   isText: false,
   type: "checkbox",
   top: 598.92,
   left: 345.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Is Not located within a common interest", 
   isText: false,
   type: "checkbox",
   top: 598.92,
   left: 384.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "per", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 634.56,
   left: 169.92,
   width: 114.96,
   height: 12.84
}
,
{
   page: 7,
   name: "and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 634.56,
   left: 302.64,
   width: 249.96,
   height: 12.84
}
,
{
   page: 7,
   name: "undefined_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 664.32,
   left: 58.8,
   width: 510.0,
   height: 12.84
}
,
{
   page: 7,
   name: "Tenancies", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 492.22,
   left: 53.673,
   width: 518.511,
   height: 39.019
}
,
{
   page: 7,
   name: "Water Stock", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 59.819,
   left: 61.1461,
   width: 508.0389,
   height: 47.527
}
,
{
   page: 7,
   name: "Other Rights Relating to Water", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 132.91,
   left: 59.1825,
   width: 510.6565,
   height: 47.528
}
,
{
   page: 7,
   name: "Growing Crops", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 192.256,
   left: 52.637,
   width: 516.548,
   height: 46.218
}
,
{
   page: 7,
   name: "Check Box49", 
   isText: false,
   type: "checkbox",
   top: 347.383,
   left: 59.5639,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 7,
   name: "Text53", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 443.402,
   left: 53.0184,
   width: 510.6566,
   height: 12.182
}
,
{
   page: 8,
   name: "undefined_11", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 60,
   left: 49.68,
   width: 519.96,
   height: 12.36
}
,
{
   page: 8,
   name: "Agrees", 
   isText: false,
   type: "checkbox",
   top: 205.08,
   left: 291.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "Does Not Agree to provide on or before the sale", 
   isText: false,
   type: "checkbox",
   top: 205.08,
   left: 352.2,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "paid to Seller in its entirety", 
   isText: false,
   type: "checkbox",
   top: 653.64,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "divided between Brokerage Firm and Seller onehalf to Brokerage Firm but not to exceed the Brokerage Firm", 
   isText: false,
   type: "checkbox",
   top: 669,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "Other_3", 
   isText: false,
   type: "checkbox",
   top: 697.32,
   left: 82.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 8,
   name: "undefined_12", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.92,
   left: 128.16,
   width: 438.96,
   height: 12.36
}
,
{
   page: 10,
   name: "undefined_13", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 692.88,
   left: 285.84,
   width: 276.72,
   height: 10.80
}
,
{
   page: 10,
   name: "Text44", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 32.727,
   left: 37.9638,
   width: 536.8382,
   height: 456.620
}
,
{
   page: 10,
   name: "Text45", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 503.348,
   left: 43.2002,
   width: 531.6018,
   height: 90.073
}
,
{
   page: 11,
   name: "Street Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 305.64,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "City State Zip", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 319.08,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Phone No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 332.76,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Fax No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 346.2,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Email Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 359.64,
   left: 117.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Street Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 305.64,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "City State Zip_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 319.08,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Phone No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 332.76,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Fax No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 346.2,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Email Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 359.64,
   left: 387.12,
   width: 174.96,
   height: 12.72
}
,
{
   page: 11,
   name: "Seller 1 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 240.038,
   left: 113.992,
   width: 186.000,
   height: 23.964
}
,
{
   page: 11,
   name: "Seller 2 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 239.383,
   left: 378.156,
   width: 186.001,
   height: 23.964
}
,
{
   page: 11,
   name: "Seller 3 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 401.038,
   left: 116.574,
   width: 186.000,
   height: 23.964
}
,
{
   page: 11,
   name: "Seller 4 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 401.038,
   left: 380.739,
   width: 186.000,
   height: 23.964
}
,
{
   page: 11,
   name: "Brokerage Firm Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 479.584,
   left: 118.662,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Broker Seller Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 495.718,
   left: 118.662,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Date Broker Seller Signed", 
   fontSize: 9,
   type: "date",
   top: 534.118,
   left: 71.1418,
   width: 174.9602,
   height: 12.720
}
,
{
   page: 11,
   name: "Brokerage Firm Street Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 562.918,
   left: 184.062,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Brokerage Firm City State Zip", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 576.358,
   left: 184.062,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Broker Seller Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 589.798,
   left: 184.062,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Broker Seller Fax", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 603.238,
   left: 184.062,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Broker Seller Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 616.918,
   left: 184.062,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "CoBroker Seller Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 651.958,
   left: 133.662,
   width: 349.920,
   height: 12.720
}
,
{
   page: 11,
   name: "Date CoBroker Seller Sign", 
   fontSize: 9,
   type: "date",
   top: 689.739,
   left: 71.3086,
   width: 174.9604,
   height: 12.7197
}
,
{
   page: 11,
   name: "signature Broker Seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 512.202,
   left: 123.437,
   width: 343.092,
   height: 15.454
}
,
{
   page: 11,
   name: "signature CoBroker Seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.02,
   left: 138.313,
   width: 343.092,
   height: 14.80
}
] }
