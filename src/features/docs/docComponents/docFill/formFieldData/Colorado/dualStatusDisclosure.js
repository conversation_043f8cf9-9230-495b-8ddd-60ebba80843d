export function dualStatusDisclosure() {
return [   //2024 Release 2025-11-11 16:59:14
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
 
{
   page: 0,
   name: "Check Box3", 
   isText: false,
   type: "checkbox",
   top: 300.84,
   left: 126,
   width: 6.000,
   height: 6.00
}
,
{
   page: 0,
   name: "Is  Is Not acting in a dual capacity as a real estate broker and a mortgage broker in the", 
   isText: false,
   type: "checkbox",
   top: 300.84,
   left: 142,
   width: 6,
   height: 6.00
}
,
{
   page: 0,
   name: "Check Box4", 
   isText: false,
   type: "checkbox",
   top: 315,
   left: 193,
   width: 6.000,
   height: 6.12
},
{
   page: 0,
   name: "REAL ESTATE BROKER AND MORTGAGE BROKER", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 203.418,
   left: 129.84,
   width: 155.28,
   height: 13.942
}

,
{
   page: 0,
   name: "Is Not accepting a placement fee commission or other valuable", 
   isText: false,
   type: "checkbox",
   top: 315,
   left: 213,
   width: 6.00,
   height: 6.12
}
,
{
   page: 0,
   name: "consideration for directly or indirectly placing a loan with a mortgage lender or its representative", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 350.287,
   left: 174.72,
   width: 298.92,
   height: 12.633
}
,
{
   page: 0,
   name: "Does", 
   isText: false,
   type: "checkbox",
   top: 354,
   left: 476.76,
   width: 6.00,
   height: 6
}
,
{
   page: 0,
   name: "Does_2", 
   isText: false,
   type: "checkbox",
   top: 354,
   left: 510.12,
   width: 6.00,
   height: 6
}
,
{
   page: 0,
   name: "Does_3", 
   isText: false,
   type: "checkbox",
   top: 380.4,
   left: 347.88,
   width: 6.00,
   height: 6.0
}
,
{
   page: 0,
   name: "Does Not consent to the Broker", 
   isText: false,
   type: "checkbox",
   top: 380.28,
   left: 380.28,
   width: 6.00,
   height: 6.00
}
,
{
   page: 0,
   name: "date brokerAck", 
   type: "date",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617,
   left: 89.64,
   width: 92.04,
   height: 11.978
}
,
{
   page: 0,
   name: "the Broker provided", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 628.36,
   left: 208.56,
   width: 149.52,
   height: 12.24
}
,
{
   page: 0,
   name: "and retained a copy for the Brokers", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617,
   left: 285.72,
   width: 143.64,
   height: 13.287
}
,
{
   page: 0,
   name: "signature broker", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 680,
   left: 72.0,
   width: 199.92,
   height: 13.942
}
] }
