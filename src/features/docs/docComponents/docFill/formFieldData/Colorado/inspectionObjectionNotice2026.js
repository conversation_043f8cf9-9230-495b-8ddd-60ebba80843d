export function inspectionObjectionNotice2026() {
return [   // Release 2025-12-10 17:17:31
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 228.96,
   left: 460.08,
   width: 125.04,
   height: 11.542
}
,
{
   page: 0,
   name: "Date Contract Date", 
   fontSize: 9,
   type: "date",
   top: 251.76,
   left: 297.36,
   width: 130.08,
   height: 11.542
}
,
{
   page: 0,
   name: "<PERSON>ller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 266.88,
   left: 58.8,
   width: 489.96,
   height: 11.542
}
,
{
   page: 0,
   name: "Buyer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 281.76,
   left: 75.72,
   width: 475.08,
   height: 11.542
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 311.76,
   left: 58.8,
   width: 480.0,
   height: 11.542
}
,
{
   page: 0,
   name: "Text65", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 395.347,
   left: 39.9274,
   width: 555.2936,
   height: 366.5517
}
,
{
   page: 1,
   name: "If more space is required attached are", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 311.12,
   left: 203.64,
   width: 53.04,
   height: 11.324
}
,
{
   page: 1,
   name: "Text66", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 29.324,
   left: 38.2253,
   width: 546.3917,
   height: 281.199
}
,
{
   page: 1,
   name: "Check Box67", 
   isText: false,
   type: "checkbox",
   top: 413.675,
   left: 165.601,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box68", 
   isText: false,
   type: "checkbox",
   top: 413.02,
   left: 202.91,
   width: 10.08,
   height: 10.08
}
] }
