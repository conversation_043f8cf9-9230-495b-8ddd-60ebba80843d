export function agreementtoReviveContractManufacturedHome() {
return [   //2024 Release 2025-12-06 11:20:25
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 268.484,
   left: 407.52,
   width: 174.12,
   height: 12.014
}
,
{
   page: 0,
   name: "date contractDate", 
   fontSize: 9,
   type: "date",
   top: 291.12,
   left: 234.12,
   width: 127.56,
   height: 12.015
}
,
{
   page: 0,
   name: "Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 306.08,
   left: 53.76,
   width: 499.92,
   height: 12.015
}
,
{
   page: 0,
   name: "Buyer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 321.84,
   left: 70.68,
   width: 480.00,
   height: 12.015
}
,
{
   page: 0,
   name: "Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 365.28,
   left: 54.12,
   width: 529.92,
   height: 12.015
}
,
{
   page: 0,
   name: "date terminated", 
   fontSize: 9,
   type: "date",
   top: 398.96,
   left: 335.76,
   width: 146.16,
   height: 12.015
}
,
{
   page: 0,
   name: "Time of Day Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 588.12,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 0,
   name: "undefined_3", 
   isText: true,
   type: "checkbox",
   top: 596.52,
   left: 522.36,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "undefined_4", 
   isText: true,
   type: "checkbox",
   top: 596.52,
   left: 564.84,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "undefined_5", 
   isText: true,
   type: "checkbox",
   top: 624.6,
   left: 522.36,
   width: 8.16,
   height: 8.4
}
,
{
   page: 0,
   name: "undefined_6", 
   isText: true,
   type: "checkbox",
   top: 624.6,
   left: 564.84,
   width: 8.16,
   height: 8.4
}
,
{
   page: 0,
   name: "DateAlternative Earnest Money Deadline", 
   fontSize: 9,
   type: "date",
   top: 616.2,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 0,
   name: "New Date or DeadlineAlternative Earnest Money Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 616.2,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 0,
   name: "DateLease Delivery Deadline", 
   fontSize: 9,
   type: "date",
   top: 666.36,
   left: 262.32,
   width: 69.72,
   height: 26.76
}
,
{
   page: 0,
   name: "New Date or DeadlineLease Delivery Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 666.36,
   left: 334.32,
   width: 168.84,
   height: 26.76
}
,
{
   page: 0,
   name: "undefined_7", 
   isText: true,
   type: "checkbox",
   top: 675,
   left: 522.36,
   width: 8.16,
   height: 8.4
}
,
{
   page: 0,
   name: "undefined_8", 
   isText: true,
   type: "checkbox",
   top: 675,
   left: 564.84,
   width: 8.16,
   height: 8.4
}
,
{
   page: 0,
   name: "DateLot Owner Approval Deadline", 
   fontSize: 9,
   type: "date",
   top: 694.68,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 0,
   name: "New Date or DeadlineLot Owner Approval Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.68,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 0,
   name: "undefined_9", 
   isText: true,
   type: "checkbox",
   top: 703.08,
   left: 522.36,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "undefined_10", 
   isText: true,
   type: "checkbox",
   top: 703.08,
   left: 564.84,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "undefined_11", 
   isText: true,
   type: "checkbox",
   top: 731.16,
   left: 522.36,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "undefined_12", 
   isText: true,
   type: "checkbox",
   top: 731.16,
   left: 564.84,
   width: 8.16,
   height: 8.40
}
,
{
   page: 0,
   name: "DateLease Review Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 722.76,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 0,
   name: "New Date or DeadlineLease Review Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 722.76,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Deadline", 
   fontSize: 9,
   type: "date",
   top: 94.2,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineUCC and Certificate of Title Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 94.2,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 122.28,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineUCC and Certificate of Title Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 122.28,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 150.36,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineUCC and Certificate of Title Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 150.36,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateSellers Property Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 190.92,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineSellers Property Disclosure Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 190.92,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateLeadBased Paint Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 219,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineLeadBased Paint Disclosure Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 219,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateNew Loan Application Deadline", 
   fontSize: 9,
   type: "date",
   top: 259.56,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineNew Loan Application Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 259.56,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateNew Loan Terms Deadline", 
   fontSize: 9,
   type: "date",
   top: 287.64,
   left: 262.32,
   width: 69.72,
   height: 26.76
}
,
{
   page: 1,
   name: "New Date or DeadlineNew Loan Terms Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 287.64,
   left: 334.32,
   width: 168.84,
   height: 26.76
}
,
{
   page: 1,
   name: "DateNew Loan Availability Deadline", 
   fontSize: 9,
   type: "date",
   top: 315.96,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineNew Loan Availability Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 315.96,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateBuyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 344.04,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineBuyers Credit Information Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 344.04,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateDisapproval of Buyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 372.12,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineDisapproval of Buyers Credit Information Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 372.12,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateSeller or Private Financing Deadline", 
   fontSize: 9,
   type: "date",
   top: 400.2,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineSeller or Private Financing Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 400.2,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAppraisal Deadline", 
   fontSize: 9,
   type: "date",
   top: 440.76,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineAppraisal Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 440.76,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAppraisal Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 468.84,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineAppraisal Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 468.84,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAppraisal Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 496.92,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineAppraisal Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 496.92,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineInspection Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 537.48,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateInspection Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 565.56,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineInspection Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 565.56,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateInspection Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 593.64,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineInspection Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 593.64,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateProperty Insurance Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 621.72,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineHome Insurance Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 621.72,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateDue Diligence Documents Delivery Deadline", 
   fontSize: 9,
   type: "date",
   top: 649.8,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineDue Diligence Documents Delivery Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 649.8,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateDue Diligence Documents Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 677.88,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 1,
   name: "New Date or DeadlineDue Diligence Documents Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 677.88,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DateDue Diligence Documents Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 706.2,
   left: 262.56,
   width: 69.24,
   height: 35.64
}
,
{
   page: 1,
   name: "New Date or DeadlineDue Diligence Documents Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 706.2,
   left: 334.56,
   width: 168.36,
   height: 35.64
}
,
{
   page: 1,
   name: "Check Box1", 
   isText: false,
   type: "checkbox",
   top: 101.877,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box2", 
   isText: false,
   type: "checkbox",
   top: 101.877,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box3", 
   isText: false,
   type: "checkbox",
   top: 129.931,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box4", 
   isText: false,
   type: "checkbox",
   top: 158.722,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box5", 
   isText: false,
   type: "checkbox",
   top: 129.931,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box6", 
   isText: false,
   type: "checkbox",
   top: 158.722,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box7", 
   isText: false,
   type: "checkbox",
   top: 199.325,
   left: 521.199,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box8", 
   isText: false,
   type: "checkbox",
   top: 198.587,
   left: 564.755,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box9", 
   isText: false,
   type: "checkbox",
   top: 227.379,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box10", 
   isText: false,
   type: "checkbox",
   top: 227.379,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box11", 
   isText: false,
   type: "checkbox",
   top: 267.982,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box12", 
   isText: false,
   type: "checkbox",
   top: 267.244,
   left: 564.755,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box13", 
   isText: false,
   type: "checkbox",
   top: 295.297,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box14", 
   isText: false,
   type: "checkbox",
   top: 296.035,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box15", 
   isText: false,
   type: "checkbox",
   top: 323.35,
   left: 521.199,
   width: 10.080,
   height: 10.08
}
,
{
   page: 1,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 323.35,
   left: 564.017,
   width: 10.080,
   height: 10.08
}
,
{
   page: 1,
   name: "Check Box18", 
   isText: false,
   type: "checkbox",
   top: 352.142,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 352.142,
   left: 563.279,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 379.457,
   left: 521.199,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 379.457,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 408.248,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 408.248,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 448.113,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 448.113,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 476.905,
   left: 521.199,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 476.166,
   left: 564.755,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 504.22,
   left: 521.937,
   width: 10.080,
   height: 10.08
}
,
{
   page: 1,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 504.22,
   left: 564.017,
   width: 10.080,
   height: 10.08
}
,
{
   page: 1,
   name: "Check Box30", 
   isText: false,
   type: "checkbox",
   top: 545.561,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box31", 
   isText: false,
   type: "checkbox",
   top: 545.561,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box32", 
   isText: false,
   type: "checkbox",
   top: 572.876,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box33", 
   isText: false,
   type: "checkbox",
   top: 572.876,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box34", 
   isText: false,
   type: "checkbox",
   top: 600.929,
   left: 521.199,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box35", 
   isText: false,
   type: "checkbox",
   top: 600.929,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box36", 
   isText: false,
   type: "checkbox",
   top: 628.983,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box37", 
   isText: false,
   type: "checkbox",
   top: 629.721,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box38", 
   isText: false,
   type: "checkbox",
   top: 658.512,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box39", 
   isText: false,
   type: "checkbox",
   top: 657.774,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box40", 
   isText: false,
   type: "checkbox",
   top: 685.827,
   left: 521.199,
   width: 10.080,
   height: 10.0803
}
,
{
   page: 1,
   name: "Check Box41", 
   isText: false,
   type: "checkbox",
   top: 685.827,
   left: 564.017,
   width: 10.080,
   height: 10.0803
}
,
{
   page: 1,
   name: "Check Box42", 
   isText: false,
   type: "checkbox",
   top: 718.3099,
   left: 521.937,
   width: 10.080,
   height: 10.0801
}
,
{
   page: 1,
   name: "Check Box43", 
   isText: false,
   type: "checkbox",
   top: 719.0482,
   left: 564.755,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 1,
   name: "DateInspection Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 538.039,
   left: 263.128,
   width: 69.491,
   height: 21.345
}
,
{
   page: 2,
   name: "New Date or DeadlineConditional Sale Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 81.72,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlineConditional Sale Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 81.72,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlineLeadBased Paint Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 109.8,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlineLeadBased Paint Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 109.8,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlineClosing Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 150.36,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlineClosing Date_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 150.36,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlinePossession Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 178.44,
   left: 262.32,
   width: 69.72,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlinePossession Date_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 178.44,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 2,
   name: "New Date or DeadlinePossession Time_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 206.52,
   left: 334.32,
   width: 168.84,
   height: 26.52
}
,
{
   page: 2,
   name: "DateRevive Acceptance Deadline", 
   fontSize: 9,
   type: "date",
   top: 712.04,
   left: 311.04,
   width: 120.00,
   height: 13.3411
}
,
{
   page: 2,
   name: "Time", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 712.04,
   left: 438.6,
   width: 120.0,
   height: 13.3411
}
,
{
   page: 2,
   name: "Check Box44", 
   isText: false,
   type: "checkbox",
   top: 89.327,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box45", 
   isText: false,
   type: "checkbox",
   top: 89.327,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box46", 
   isText: false,
   type: "checkbox",
   top: 118.119,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box47", 
   isText: false,
   type: "checkbox",
   top: 118.119,
   left: 564.755,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box48", 
   isText: false,
   type: "checkbox",
   top: 157.984,
   left: 521.199,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box49", 
   isText: false,
   type: "checkbox",
   top: 157.984,
   left: 564.755,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box50", 
   isText: false,
   type: "checkbox",
   top: 185.299,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box51", 
   isText: false,
   type: "checkbox",
   top: 186.775,
   left: 564.017,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box52", 
   isText: false,
   type: "checkbox",
   top: 214.829,
   left: 521.937,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box53", 
   isText: false,
   type: "checkbox",
   top: 214.09,
   left: 564.017,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Text54", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 259.861,
   left: 44.2946,
   width: 547.1744,
   height: 109.851
}
,
{
   page: 2,
   name: "Text55", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 384.624,
   left: 39.8651,
   width: 551.6039,
   height: 239.044
}
] }
