export function mLSCRENListingWaiverForm() {
return [   //2024 Release 2025-11-08 07:24:40
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "I hereby certify that Listing Agent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 221.76,
   left: 230.04,
   width: 306.60,
   height: 13.745
}
,
{
   page: 0,
   name: "of Listing Office", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 246.76,
   left: 150.0,
   width: 388.8,
   height: 13.745
}
,
{
   page: 0,
   name: "Street Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 297.64,
   left: 147.84,
   width: 339.36,
   height: 13.745
}
,
{
   page: 0,
   name: "Date Listing Contract Date", 
   fontSize: 9,
   type: "date",
   top: 460.76,
   left: 173.04,
   width: 115.56,
   height: 13.745
}
,
{
   page: 0,
   name: "Date Listing Expiration Date", 
   fontSize: 9,
   type: "date",
   top: 460.76,
   left: 419.355,
   width: 120.960,
   height: 13.745
}
,
{
   page: 0,
   name: "Signature Listing Agent", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 555.857,
   left: 189.819,
   width: 150.000,
   height: 13.745
}
,
{
   page: 0,
   name: "Date Listing Agent Sign", 
   fontSize: 9,
   type: "date",
   top: 555.857,
   left: 418.911,
   width: 150.000,
   height: 13.745
}
] }
