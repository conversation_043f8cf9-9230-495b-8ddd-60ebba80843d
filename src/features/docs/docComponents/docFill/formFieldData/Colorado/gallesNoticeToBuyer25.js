export function gallesNoticeToBuyer25() {
return [   //2024 Release 2025-11-09 12:36:34
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 3,
   name: "signature Broker", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 203.586,
   left: 66.3651,
   width: 221.1289,
   height: 13.892
}
,
{
   page: 3,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 202.296,
   left: 290.243,
   width: 76.033,
   height: 14.415
}
,
{
   page: 3,
   name: "Text17", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 269.312,
   left: 81.9214,
   width: 264.4956,
   height: 22.861
}
,
{
   page: 3,
   name: "Text18", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 316.66,
   left: 81.9214,
   width: 264.4956,
   height: 22.86
}
,
{
   page: 3,
   name: "Text19", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 368.311,
   left: 81.9214,
   width: 264.4956,
   height: 22.861
}
,
{
   page: 3,
   name: "Text20", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 7,
   top: 412.216,
   left: 81.9214,
   width: 264.4956,
   height: 22.861
}
] }
