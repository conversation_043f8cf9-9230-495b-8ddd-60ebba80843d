export function aDMINClosingWorksheetGalles() {
return [   //2024 Release 2025-05-04 12:09:33

{
   page: 0,
   name: "date Closing Date", 
   fontSize: 9,
   type: "date",
   top: 114.72,
   left: 143.76,
   width: 95.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Agent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 114.72,
   left: 291.24,
   width: 101.64,
   height: 13.92
}
,
{
   page: 0,
   name: "Title Company", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 114.72,
   left: 477.48,
   width: 84.633,
   height: 13.92
}
,
{
   page: 0,
   name: "Property Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 143.345,
   left: 168.12,
   width: 287.432,
   height: 14.575
}
,
{
   page: 0,
   name: "Our Client", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 142.516,
   left: 514.549,
   width: 55.833,
   height: 13.920
}
,
{
   page: 0,
   name: "Buyers", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 173.28,
   left: 120.12,
   width: 166.757,
   height: 12.92
}
,
{
   page: 0,
   name: "Sellers", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 171.971,
   left: 332.684,
   width: 251.630,
   height: 13.265
}
,
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 217.2,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 246.48,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 275.76,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed Before Split Off The Top 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 327.36,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 327.12,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed Before Split Off The Top 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 349.2,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 349.2,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed Before Split Off The Top 3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 371.28,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 371.28,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Brokerage Split", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 407.76,
   left: 163.8,
   width: 41.76,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 407.76,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Agent Gross Commission", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 437.04,
   left: 211.32,
   width: 41.88,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 437.04,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed After Split 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 488.4,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 488.4,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed After Split 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 510.48,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 510.48,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed After Split 3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 532.32,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_11", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 532.32,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Expenses Assessed After Split 4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 554.4,
   left: 108.96,
   width: 221.16,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_12", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 554.4,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "undefined_13", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 583.44,
   left: 435.48,
   width: 101.52,
   height: 13.92
}
,
{
   page: 0,
   name: "Title Payment Method ACHManual", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 671.16,
   left: 263.4,
   width: 53.76,
   height: 13.92
}
,
{
   page: 0,
   name: "Date Received From Title", 
   fontSize: 9,
   type: "date",
   top: 671.16,
   left: 491.76,
   width: 47.88,
   height: 13.92
}
,
{
   page: 0,
   name: "Agent Payment Method ACHCheck", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 700.68,
   left: 263.04,
   width: 53.76,
   height: 13.92
}
,
{
   page: 0,
   name: "Date Galles Agent Paid", 
   fontSize: 9,
   type: "date",
   top: 700.68,
   left: 480.24,
   width: 59.76,
   height: 13.92
}
] }
