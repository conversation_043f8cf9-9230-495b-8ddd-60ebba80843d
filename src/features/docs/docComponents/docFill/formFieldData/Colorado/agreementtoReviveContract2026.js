export function agreementtoReviveContract2026() {
return [   // Release 2025-12-10 10:22:51
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 215.567,
   left: 445.898,
   width: 138.189,
   height: 15.356
}
,
{
   page: 0,
   name: "Date Contract Date", 
   fontSize: 9,
   type: "date",
   top: 239.28,
   left: 225.12,
   width: 163.32,
   height: 14.04
}
,
{
   page: 0,
   name: "Buyer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 266.575,
   left: 70.68,
   width: 467.618,
   height: 13.385
}
,
{
   page: 0,
   name: "Address County", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 280.8,
   left: 429.24,
   width: 144.84,
   height: 14.04
}
,
{
   page: 0,
   name: "Date terminated", 
   fontSize: 9,
   type: "date",
   top: 483.48,
   left: 331.2,
   width: 150.72,
   height: 14.04
}
,
{
   page: 0,
   name: "or DeadlineTime of Day Deadline", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 708.96,
   left: 383.4,
   width: 120.12,
   height: 17.76
}
,
{
   page: 0,
   name: "DateAlternative Earnest Money Deadline", 
   fontSize: 9,
   type: "date",
   top: 728.28,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 0,
   name: "or DeadlineAlternative Earnest Money Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 728.28,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 0,
   name: "Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 253.965,
   left: 44.5093,
   width: 493.6377,
   height: 11.527
}
,
{
   page: 0,
   name: "Address Legal Description", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 310.91,
   left: 43.2002,
   width: 546.6558,
   height: 137.856
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 449.675,
   left: 91.6368,
   width: 436.6922,
   height: 14.145
}
,
{
   page: 0,
   name: "Check Box6", 
   isText: false,
   type: "checkbox",
   top: 712.8032,
   left: 519.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 0,
   name: "Check Box7", 
   isText: false,
   type: "checkbox",
   top: 713.4577,
   left: 554.402,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 0,
   name: "Check Box8", 
   isText: false,
   type: "checkbox",
   top: 736.3669,
   left: 519.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 0,
   name: "Check Box9", 
   isText: false,
   type: "checkbox",
   top: 736.3669,
   left: 554.402,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 1,
   name: "DateRecord Title Deadline and Tax Certificate", 
   fontSize: 9,
   type: "date",
   top: 77.64,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineRecord Title Deadline and Tax Certificate", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 77.64,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateRecord Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 105.72,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineRecord Title Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 105.72,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateOffRecord Title Deadline", 
   fontSize: 9,
   type: "date",
   top: 133.8,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 1,
   name: "or DeadlineOffRecord Title Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 133.8,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 1,
   name: "DateOffRecord Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 162.12,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineOffRecord Title Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 162.12,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateTitle Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 190.2,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineTitle Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 190.2,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateThird Party Right to PurchaseApprove Deadline", 
   fontSize: 9,
   type: "date",
   top: 218.28,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineThird Party Right to PurchaseApprove Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 218.28,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAssociation Documents Deadline", 
   fontSize: 9,
   type: "date",
   top: 258.84,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineAssociation Documents Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 258.84,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAssociation Documents Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 286.92,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineAssociation Documents Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 286.92,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateSellers Property Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 327.48,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineSellers Property Disclosure Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 327.48,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateLeadBased Paint Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 355.56,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineLeadBased Paint Disclosure Deadline CBS1 CBS2 CBSF1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.56,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateNew Loan Application Deadline", 
   fontSize: 9,
   type: "date",
   top: 396.12,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineNew Loan Application Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 396.12,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateNew Loan Terms Deadline", 
   fontSize: 9,
   type: "date",
   top: 424.2,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineNew Loan Terms Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 424.2,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateNew Loan Availability Deadline", 
   fontSize: 9,
   type: "date",
   top: 452.28,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineNew Loan Availability Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 452.28,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateBuyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 480.36,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineBuyers Credit Information Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 480.36,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateDisapproval of Buyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 508.44,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineDisapproval of Buyers Credit Information Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 508.44,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateExisting Loan Deadline", 
   fontSize: 9,
   type: "date",
   top: 536.52,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineExisting Loan Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 536.52,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateExisting Loan Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 564.6,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineExisting Loan Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 564.6,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateLoan Transfer Approval Deadline", 
   fontSize: 9,
   type: "date",
   top: 592.68,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineLoan Transfer Approval Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 592.68,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateSeller or Private Financing Deadline", 
   fontSize: 9,
   type: "date",
   top: 620.76,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 1,
   name: "or DeadlineSeller or Private Financing Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 620.76,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 1,
   name: "DateAppraisal Deadline", 
   fontSize: 9,
   type: "date",
   top: 661.32,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineAppraisal Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 661.32,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "DateAppraisal Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 689.4,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 1,
   name: "or DeadlineAppraisal Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 689.4,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 1,
   name: "DateAppraisal Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 717.72,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 1,
   name: "or DeadlineAppraisal Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 717.72,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 1,
   name: "Check Box10", 
   isText: false,
   type: "checkbox",
   top: 85.746,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box11", 
   isText: false,
   type: "checkbox",
   top: 85.091,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box12", 
   isText: false,
   type: "checkbox",
   top: 114.546,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box13", 
   isText: false,
   type: "checkbox",
   top: 113.891,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box14", 
   isText: false,
   type: "checkbox",
   top: 142.037,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box15", 
   isText: false,
   type: "checkbox",
   top: 141.382,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box16", 
   isText: false,
   type: "checkbox",
   top: 169.528,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 170.183,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box18", 
   isText: false,
   type: "checkbox",
   top: 198.328,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 197.674,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 225.819,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 225.819,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 266.401,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 267.056,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 294.547,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 295.201,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 335.783,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 335.129,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 363.274,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 363.274,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box30", 
   isText: false,
   type: "checkbox",
   top: 403.856,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box31", 
   isText: false,
   type: "checkbox",
   top: 403.856,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box32", 
   isText: false,
   type: "checkbox",
   top: 432.656,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box33", 
   isText: false,
   type: "checkbox",
   top: 432.002,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box34", 
   isText: false,
   type: "checkbox",
   top: 459.493,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box35", 
   isText: false,
   type: "checkbox",
   top: 458.838,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box36", 
   isText: false,
   type: "checkbox",
   top: 488.948,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box37", 
   isText: false,
   type: "checkbox",
   top: 488.293,
   left: 555.712,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box38", 
   isText: false,
   type: "checkbox",
   top: 516.439,
   left: 519.711,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box39", 
   isText: false,
   type: "checkbox",
   top: 515.784,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box40", 
   isText: false,
   type: "checkbox",
   top: 544.584,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box41", 
   isText: false,
   type: "checkbox",
   top: 544.584,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box42", 
   isText: false,
   type: "checkbox",
   top: 572.075,
   left: 519.711,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box43", 
   isText: false,
   type: "checkbox",
   top: 572.075,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box44", 
   isText: false,
   type: "checkbox",
   top: 600.875,
   left: 518.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box45", 
   isText: false,
   type: "checkbox",
   top: 600.875,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box46", 
   isText: false,
   type: "checkbox",
   top: 629.021,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box47", 
   isText: false,
   type: "checkbox",
   top: 629.021,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box48", 
   isText: false,
   type: "checkbox",
   top: 668.948,
   left: 518.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box49", 
   isText: false,
   type: "checkbox",
   top: 669.603,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box50", 
   isText: false,
   type: "checkbox",
   top: 697.7485,
   left: 518.402,
   width: 10.080,
   height: 10.0801
}
,
{
   page: 1,
   name: "Check Box51", 
   isText: false,
   type: "checkbox",
   top: 697.7485,
   left: 555.712,
   width: 10.080,
   height: 10.0801
}
,
{
   page: 1,
   name: "Check Box52", 
   isText: false,
   type: "checkbox",
   top: 725.2396,
   left: 519.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 1,
   name: "Check Box53", 
   isText: false,
   type: "checkbox",
   top: 725.2396,
   left: 555.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 2,
   name: "DateNew ILC or New Survey Deadline", 
   fontSize: 9,
   type: "date",
   top: 77.64,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineNew ILC or New Survey Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 77.64,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateNew ILC or New Survey Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 105.72,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineNew ILC or New Survey Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 105.72,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateNew ILC or New Survey Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 133.8,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 2,
   name: "or DeadlineNew ILC or New Survey Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 133.8,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 2,
   name: "DateWater Rights Examination Deadline", 
   fontSize: 9,
   type: "date",
   top: 174.36,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineWater Rights Examination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 174.36,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateMineral Rights Examination Deadline", 
   fontSize: 9,
   type: "date",
   top: 202.44,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 2,
   name: "or DeadlineMineral Rights Examination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 202.44,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 2,
   name: "DateInspection Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 230.76,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineInspection Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 230.76,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateInspection Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 258.84,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineInspection Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 258.84,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateInspection Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 286.92,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineInspection Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 286.92,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateProperty Insurance Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 315,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineProperty Insurance Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 315,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateDue Diligence Documents Delivery Deadline", 
   fontSize: 9,
   type: "date",
   top: 343.08,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineDue Diligence Documents Delivery Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 343.08,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateDue Diligence Documents Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 371.16,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineDue Diligence Documents Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 371.16,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateDue Diligence Documents Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 399.24,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineDue Diligence Documents Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 399.24,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateEnvironmental Inspection Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 427.32,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineEnvironmental Inspection Termination Deadline CBS2 CBS3 CBS4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 427.32,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateADA Evaluation Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 455.4,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineADA Evaluation Termination Deadline CBS2 CBS3 CBS4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 455.4,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateConditional Sale Deadline", 
   fontSize: 9,
   type: "date",
   top: 483.48,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineConditional Sale Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 483.48,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateLeadBased Paint Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 511.56,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineLeadBased Paint Termination Deadline CBS1 CBS2 CBSF1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 511.56,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateEstoppel Statements Deadline", 
   fontSize: 9,
   type: "date",
   top: 539.64,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineEstoppel Statements Deadline CBS2 CBS3 CBS4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 539.64,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DateEstoppel Statements Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 567.72,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineEstoppel Statements Termination Deadline CBS2 CBS3 CBS4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 567.72,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "42Row1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 692.6476,
   left: 50.4,
   width: 21.24,
   height: 25.1456
}
,
{
   page: 2,
   name: " 11Row1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 719.0296,
   left: 73.6145,
   width: 24.9055,
   height: 18.6001
}
,
{
   page: 2,
   name: "DateClosing Date", 
   fontSize: 9,
   type: "date",
   top: 608.28,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineClosing Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 608.28,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "DatePossession Date", 
   fontSize: 9,
   type: "date",
   top: 636.36,
   left: 316.32,
   width: 65.16,
   height: 26.76
}
,
{
   page: 2,
   name: "or DeadlinePossession Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 636.36,
   left: 383.76,
   width: 119.40,
   height: 26.76
}
,
{
   page: 2,
   name: "45", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 691.92,
   left: 72.0546,
   width: 27.0654,
   height: 27.36
}
,
{
   page: 2,
   name: "or DeadlinePossession Time", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 664.68,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "Possession TimeRow1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 692.76,
   left: 100.32,
   width: 213.72,
   height: 26.52
}
,
{
   page: 2,
   name: "Date New DateRow25", 
   fontSize: 9,
   type: "date",
   top: 692.76,
   left: 316.32,
   width: 65.16,
   height: 26.52
}
,
{
   page: 2,
   name: "or DeadlineRow25", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 692.76,
   left: 383.76,
   width: 119.40,
   height: 26.52
}
,
{
   page: 2,
   name: "undefined_93", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 720.0,
   left: 49.8,
   width: 21.829,
   height: 16.6909
}
,
{
   page: 2,
   name: "Possession TimeRow2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 720.48,
   left: 99.96,
   width: 214.44,
   height: 17.52
}
,
{
   page: 2,
   name: "Date New DateRow26", 
   fontSize: 9,
   type: "date",
   top: 720.48,
   left: 315.96,
   width: 65.88,
   height: 17.52
}
,
{
   page: 2,
   name: "or DeadlineRow26", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 720.48,
   left: 383.4,
   width: 120.12,
   height: 17.52
}
,
{
   page: 2,
   name: "Check Box54", 
   isText: false,
   type: "checkbox",
   top: 85.746,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box55", 
   isText: false,
   type: "checkbox",
   top: 85.746,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box56", 
   isText: false,
   type: "checkbox",
   top: 113.891,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box57", 
   isText: false,
   type: "checkbox",
   top: 113.237,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box58", 
   isText: false,
   type: "checkbox",
   top: 142.037,
   left: 518.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box59", 
   isText: false,
   type: "checkbox",
   top: 142.037,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box60", 
   isText: false,
   type: "checkbox",
   top: 182.619,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box61", 
   isText: false,
   type: "checkbox",
   top: 181.964,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box62", 
   isText: false,
   type: "checkbox",
   top: 210.765,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box63", 
   isText: false,
   type: "checkbox",
   top: 210.11,
   left: 555.057,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box64", 
   isText: false,
   type: "checkbox",
   top: 238.256,
   left: 518.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box65", 
   isText: false,
   type: "checkbox",
   top: 238.256,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box66", 
   isText: false,
   type: "checkbox",
   top: 265.747,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box67", 
   isText: false,
   type: "checkbox",
   top: 266.401,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box68", 
   isText: false,
   type: "checkbox",
   top: 294.547,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box69", 
   isText: false,
   type: "checkbox",
   top: 294.201,
   left: 554.712,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box70", 
   isText: false,
   type: "checkbox",
   top: 322.692,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box71", 
   isText: false,
   type: "checkbox",
   top: 322.692,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box72", 
   isText: false,
   type: "checkbox",
   top: 350.838,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box73", 
   isText: false,
   type: "checkbox",
   top: 350.183,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box74", 
   isText: false,
   type: "checkbox",
   top: 378.983,
   left: 519.057,
   width: 10.080,
   height: 10.081
}
,
{
   page: 2,
   name: "Check Box75", 
   isText: false,
   type: "checkbox",
   top: 378.983,
   left: 555.057,
   width: 10.080,
   height: 10.081
}
,
{
   page: 2,
   name: "Check Box76", 
   isText: false,
   type: "checkbox",
   top: 406.475,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box77", 
   isText: false,
   type: "checkbox",
   top: 406.475,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box78", 
   isText: false,
   type: "checkbox",
   top: 434.62,
   left: 519.711,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box79", 
   isText: false,
   type: "checkbox",
   top: 434.62,
   left: 555.057,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box80", 
   isText: false,
   type: "checkbox",
   top: 463.42,
   left: 519.057,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box81", 
   isText: false,
   type: "checkbox",
   top: 462.766,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box82", 
   isText: false,
   type: "checkbox",
   top: 490.911,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box83", 
   isText: false,
   type: "checkbox",
   top: 490.911,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box84", 
   isText: false,
   type: "checkbox",
   top: 519.057,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box85", 
   isText: false,
   type: "checkbox",
   top: 519.366,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box86", 
   isText: false,
   type: "checkbox",
   top: 547.202,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box87", 
   isText: false,
   type: "checkbox",
   top: 547.202,
   left: 555.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box88", 
   isText: false,
   type: "checkbox",
   top: 575.348,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box89", 
   isText: false,
   type: "checkbox",
   top: 575.348,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box90", 
   isText: false,
   type: "checkbox",
   top: 615.93,
   left: 519.057,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box91", 
   isText: false,
   type: "checkbox",
   top: 616.585,
   left: 555.712,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box92", 
   isText: false,
   type: "checkbox",
   top: 644.73,
   left: 519.057,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box93", 
   isText: false,
   type: "checkbox",
   top: 644.73,
   left: 554.402,
   width: 10.080,
   height: 10.08
}
,
{
   page: 2,
   name: "Check Box94", 
   isText: false,
   type: "checkbox",
   top: 672.221,
   left: 519.057,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box95", 
   isText: false,
   type: "checkbox",
   top: 672.221,
   left: 554.402,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box96", 
   isText: false,
   type: "checkbox",
   top: 700.3668,
   left: 519.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 2,
   name: "Check Box97", 
   isText: false,
   type: "checkbox",
   top: 700.3668,
   left: 555.057,
   width: 10.080,
   height: 10.0800
}
,
{
   page: 2,
   name: "Check Box98", 
   isText: false,
   type: "checkbox",
   top: 723.2759,
   left: 519.711,
   width: 10.080,
   height: 10.0801
}
,
{
   page: 2,
   name: "Check Box99", 
   isText: false,
   type: "checkbox",
   top: 723.2759,
   left: 555.057,
   width: 10.080,
   height: 10.0801
}
,
{
   page: 3,
   name: "Date acceptRevive", 
   fontSize: 9,
   type: "date",
   top: 445.2,
   left: 295.56,
   width: 105.00,
   height: 10.68
}
,
{
   page: 3,
   name: "time acceptRevive", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 445.2,
   left: 408,
   width: 105,
   height: 10.68
}
,
{
   page: 3,
   name: "Text4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 50.4,
   left: 42.5456,
   width: 540.7654,
   height: 80.255
}
,
{
   page: 3,
   name: "Text5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 141.692,
   left: 43.2002,
   width: 541.4198,
   height: 234.073
}
] }
