export function manufacturedHomeAddendum2026() {
return [   // Release 2025-12-10 10:35:43
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 288.68,
   left: 456.24,
   width: 133.32,
   height: 12.731
}
,
{
   page: 0,
   name: "Date Contract Date", 
   fontSize: 9,
   type: "date",
   top: 346.76,
   left: 445.2,
   width: 140.76,
   height: 12.731
}
,
{
   page: 0,
   name: "Date Contract Date Commercial", 
   fontSize: 9,
   type: "date",
   top: 373.4,
   left: 475.2,
   width: 110.76,
   height: 12.731
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 411.08,
   left: 94.56,
   width: 446.28,
   height: 12.731
}
,
{
   page: 0,
   name: "Manufacturer", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 547.68,
   left: 176.64,
   width: 402.84,
   height: 28.80
}
,
{
   page: 0,
   name: "Model", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 579.12,
   left: 176.64,
   width: 402.84,
   height: 28.80
}
,
{
   page: 0,
   name: "Serial No", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 609.24,
   left: 176.4,
   width: 403.32,
   height: 20.04
}
,
{
   page: 0,
   name: "Size", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 630.6,
   left: 176.4,
   width: 403.32,
   height: 19.8
}
,
{
   page: 0,
   name: "Year of Manufacture", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 651.72,
   left: 176.4,
   width: 403.32,
   height: 19.80
}
,
{
   page: 0,
   name: "Certificate No", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 672.84,
   left: 176.4,
   width: 403.32,
   height: 20.04
}
,
{
   page: 0,
   name: "Manufacturer Tag No", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.2,
   left: 176.4,
   width: 403.32,
   height: 19.8
}
,
{
   page: 0,
   name: "VIN No", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 715.32,
   left: 176.4,
   width: 403.32,
   height: 19.80
}
,
{
   page: 0,
   name: "Check Box16", 
   isText: false,
   type: "checkbox",
   top: 350.183,
   left: 58.9093,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 0,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 377.02,
   left: 58.9093,
   width: 10.0800,
   height: 10.08
}
,
{
   page: 1,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 265.92,
   left: 409.44,
   width: 180.00,
   height: 12.36
}
,
{
   page: 1,
   name: "Conveyance of all Inclusions will be by bill of sale or other applicable legal instrument", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 279.12,
   left: 80.88,
   width: 505.08,
   height: 12.36
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Deadline", 
   fontSize: 9,
   type: "date",
   top: 542.76,
   left: 248.64,
   width: 78.84,
   height: 26.76
}
,
{
   page: 1,
   name: "UCC and Certificate of Title Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 542.76,
   left: 329.76,
   width: 249.72,
   height: 26.76
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 571.08,
   left: 248.64,
   width: 78.84,
   height: 26.52
}
,
{
   page: 1,
   name: "UCC and Certificate of Title Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 571.08,
   left: 329.76,
   width: 249.72,
   height: 26.52
}
,
{
   page: 1,
   name: "46", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 626.36,
   left: 65.04,
   width: 28.68,
   height: 19.32
}
,
{
   page: 1,
   name: " 7_3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 626.36,
   left: 95.52,
   width: 29.64,
   height: 19.32
}
,
{
   page: 1,
   name: "DateUCC and Certificate of Title Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 599.16,
   left: 248.64,
   width: 78.84,
   height: 26.52
}
,
{
   page: 1,
   name: "UCC and Certificate of Title Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 599.16,
   left: 329.76,
   width: 249.72,
   height: 26.52
}
,
{
   page: 1,
   name: "UCC and Certificate of Title Resolution DeadlineRow1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 627.24,
   left: 127.2,
   width: 119.16,
   height: 26.52
}
,
{
   page: 1,
   name: "Date or DeadlineRow5", 
   fontSize: 9,
   type: "date",
   top: 627.24,
   left: 248.64,
   width: 78.84,
   height: 26.52
}
,
{
   page: 1,
   name: "DeadlineRow5_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 627.24,
   left: 329.76,
   width: 249.72,
   height: 26.52
}
,
{
   page: 1,
   name: "loanType", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.96,
   left: 430.56,
   width: 156.36,
   height: 12.36
}
,
{
   page: 1,
   name: "Text1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 331.856,
   left: 63.4912,
   width: 519.8198,
   height: 151.601
}
,
{
   page: 1,
   name: "Check Box2", 
   isText: false,
   type: "checkbox",
   top: 148.582,
   left: 83.1276,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box3", 
   isText: false,
   type: "checkbox",
   top: 149.237,
   left: 140.073,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box4", 
   isText: false,
   type: "checkbox",
   top: 149.892,
   left: 229.746,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box5", 
   isText: false,
   type: "checkbox",
   top: 149.237,
   left: 338.401,
   width: 10.080,
   height: 10.080
}
,
{
   page: 1,
   name: "Check Box6", 
   isText: false,
   type: "checkbox",
   top: 149.237,
   left: 448.366,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box7", 
   isText: false,
   type: "checkbox",
   top: 332.511,
   left: 85.7458,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box8", 
   isText: false,
   type: "checkbox",
   top: 332.511,
   left: 134.182,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box9", 
   isText: false,
   type: "checkbox",
   top: 346.256,
   left: 306.983,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box10", 
   isText: false,
   type: "checkbox",
   top: 346.256,
   left: 358.038,
   width: 10.080,
   height: 10.080
}
,
{
   page: 2,
   name: "Check Box11", 
   isText: false,
   type: "checkbox",
   top: 371.783,
   left: 54.9821,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 3,
   name: "Check Box12", 
   isText: false,
   type: "checkbox",
   top: 399.929,
   left: 58.2548,
   width: 10.0800,
   height: 10.080
}
,
{
   page: 3,
   name: "Check Box13", 
   isText: false,
   type: "checkbox",
   top: 399.275,
   left: 111.928,
   width: 10.080,
   height: 10.080
}
,
{
   page: 3,
   name: "Check Box14", 
   isText: false,
   type: "checkbox",
   top: 399.929,
   left: 172.146,
   width: 10.080,
   height: 10.080
}
,
{
   page: 3,
   name: "Check Box15", 
   isText: false,
   type: "checkbox",
   top: 399.929,
   left: 231.71,
   width: 10.08,
   height: 10.080
}
] }
