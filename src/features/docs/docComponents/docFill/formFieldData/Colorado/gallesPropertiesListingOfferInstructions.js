export function gallesPropertiesListingOfferInstructions() {
return [   //2024 Release 2025-11-10 07:41:13
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 183.178,
   left: 87.84,
   width: 396.36,
   height: 12.087
}
,
{
   page: 0,
   name: "MLS", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 183.178,
   left: 508.32,
   width: 61.08,
   height: 12.087
}
,
{
   page: 0,
   name: "<PERSON>", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 202.52,
   left: 78.0436,
   width: 492.8404,
   height: 69.425
}
,
{
   page: 0,
   name: "Owner Names", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 275.134,
   left: 122.531,
   width: 448.353,
   height: 30.415
}
,
{
   page: 0,
   name: "Company Trust or Entity If Applicable", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 306.527,
   left: 214.604,
   width: 356.280,
   height: 12.873
}
,
{
   page: 0,
   name: "Inclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 328.469,
   left: 97.2436,
   width: 473.6404,
   height: 72.175
}
,
{
   page: 0,
   name: "Exclusions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 403.607,
   left: 98.0727,
   width: 472.2873,
   height: 71.259
}
,
{
   page: 0,
   name: "Sellers Preferred Title Company", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 555.56,
   left: 186.0,
   width: 172.8,
   height: 11.564
}
,
{
   page: 0,
   name: "Earnest Money", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 555.56,
   left: 423.84,
   width: 143.16,
   height: 11.564
}
,
{
   page: 0,
   name: "List Agent 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 573.08,
   left: 104.16,
   width: 131.40,
   height: 11.564
}
,
{
   page: 0,
   name: "Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 573.08,
   left: 266.291,
   width: 73.669,
   height: 11.04
}
,
{
   page: 0,
   name: "Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 573.08,
   left: 369.12,
   width: 197.88,
   height: 11.564
}
,
{
   page: 0,
   name: "List Agent 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 590.48,
   left: 103.92,
   width: 131.64,
   height: 11.564
}
,
{
   page: 0,
   name: "Phone_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 590.48,
   left: 268.32,
   width: 70.811,
   height: 12.088
}
,
{
   page: 0,
   name: "Email_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 590.48,
   left: 368.88,
   width: 198.84,
   height: 11.564
}
,
{
   page: 0,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 139.393,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 168.219,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 278.565,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 401.62,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 488.057,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 479.563,
   left: 554.821,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 498.184,
   left: 192.51,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 498.184,
   left: 228.51,
   width: 10.171,
   height: 9.159
}
,
{
   page: 0,
   name: "Text30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 638.185,
   left: 53.0184,
   width: 515.2376,
   height: 129.3455
}
] }
