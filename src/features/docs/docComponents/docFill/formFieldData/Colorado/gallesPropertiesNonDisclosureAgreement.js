export function gallesPropertiesNonDisclosureAgreement() {
return [   //2024 Release 2025-11-07 20:21:54
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Property Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.92,
   left: 145.56,
   width: 142.44,
   height: 12.273
}
,
{
   page: 0,
   name: "Property Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.92,
   left: 384.28,
   width: 153.48,
   height: 12.273
}
,
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 213.636,
   left: 312.96,
   width: 140.531,
   height: 10.964
}
,
{
   page: 0,
   name: "will be used solely for purposes pursuant to acquisition of said", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 240.32,
   left: 333.64,
   width: 114.96,
   height: 12.273
}
,
{
   page: 0,
   name: "property", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 253.68,
   left: 88.56,
   width: 158.88,
   height: 12.273
}
,
{
   page: 0,
   name: "Seller Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 498.28,
   left: 73.0,
   width: 147.96,
   height: 12.273
}
,
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 498.28,
   left: 432.0,
   width: 60.24,
   height: 12.273
}
,
{
   page: 0,
   name: "Prospective Buyer Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 534.88,
   left: 73.0,
   width: 147.96,
   height: 12.273
}
,
{
   page: 0,
   name: "Date_2", 
   fontSize: 9,
   type: "date",
   top: 534.88,
   left: 432.0,
   width: 64.8,
   height: 12.273
}
,
{
   page: 0,
   name: "Listing Agent Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 571.6,
   left: 73.0,
   width: 147.96,
   height: 12.273
}
,
{
   page: 0,
   name: "Date_3", 
   fontSize: 9,
   type: "date",
   top: 571.6,
   left: 432.0,
   width: 64.8,
   height: 12.273
}
,
{
   page: 0,
   name: "Prospective Buyer Rep Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 609.32,
   left: 73.0,
   width: 147.96,
   height: 11.618
}
,
{
   page: 0,
   name: "Date_4", 
   fontSize: 9,
   type: "date",
   top: 608.32,
   left: 432.0,
   width: 64.8,
   height: 12.273
}
,
{
   page: 0,
   name: "Text12", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 213.219,
   left: 69.3821,
   width: 220.6909,
   height: 12.273
}
,
{
   page: 0,
   name: "signature listingAgent", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 572.912,
   left: 252.656,
   width: 150.000,
   height: 12.272
}
] }
