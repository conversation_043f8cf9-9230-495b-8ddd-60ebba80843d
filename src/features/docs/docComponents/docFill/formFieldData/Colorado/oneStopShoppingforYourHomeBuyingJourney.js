export function oneStopShoppingforYourHomeBuyingJourney() {
return [   //2024 Release 2025-08-29 06:42:42
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Text7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 379.747,
   left: 59.2548,
   width: 50.5082,
   height: 15.455
}
,
{
   page: 0,
   name: "Text8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 414.638,
   left: 58.2548,
   width: 50.5082,
   height: 15.455
}
,
{
   page: 0,
   name: "Text1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 479.457,
   left: 72.6549,
   width: 235.7461,
   height: 13.490
}
,
{
   page: 0,
   name: "Text2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 508.793,
   left: 72.6549,
   width: 235.7461,
   height: 13.491
}
,
{
   page: 0,
   name: "Text3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 538.13,
   left: 72.6549,
   width: 235.7461,
   height: 13.49
}
,
{
   page: 0,
   name: "Text4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 478.802,
   left: 350.147,
   width: 235.746,
   height: 13.491
}
,
{
   page: 0,
   name: "Text5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 508.139,
   left: 350.147,
   width: 235.746,
   height: 13.490
}
,
{
   page: 0,
   name: "Text6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 537.475,
   left: 350.147,
   width: 235.746,
   height: 13.491
}
] }
