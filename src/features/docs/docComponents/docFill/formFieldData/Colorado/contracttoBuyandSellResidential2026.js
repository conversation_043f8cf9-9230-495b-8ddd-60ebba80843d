export function contracttoBuyandSellResidential2026() {
return [   //2024 Release 2025-12-04 18:31:25
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 265.92,
   left: 407.16,
   width: 155.04,
   height: 16.44
}
,
{
   page: 0,
   name: "Buyer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 369.971,
   left: 126.0,
   width: 455.433,
   height: 13.669
}
,
{
   page: 0,
   name: "Joint Tenants", 
   isText: false,
   type: "checkbox",
   top: 404.52,
   left: 96.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 0,
   name: "Tenants In Common", 
   isText: false,
   type: "checkbox",
   top: 421.8,
   left: 95.88,
   width: 10.08,
   height: 10.08
}
,
{
   page: 0,
   name: "Other", 
   isText: false,
   type: "checkbox",
   top: 439.08,
   left: 95.88,
   width: 10.08,
   height: 10.08
}
,
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 435.098,
   left: 139.92,
   width: 443.367,
   height: 13.822
}
,
{
   page: 0,
   name: "Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.418,
   left: 121.8,
   width: 463.091,
   height: 13.822
}
,
{
   page: 0,
   name: "Property The Property is the following legally described real estate in the County of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 504.676,
   left: 437.28,
   width: 125.04,
   height: 11.204
}
,
{
   page: 0,
   name: "Text1180", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 531.493,
   left: 53.673,
   width: 535.529,
   height: 158.801
}
,
{
   page: 0,
   name: "known as", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 695.1928,
   left: 110.64,
   width: 454.68,
   height: 13.1672
}
,
{
   page: 1,
   name: "accessories and garage door openers including", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 117.36,
   left: 280.92,
   width: 55.08,
   height: 12.36
}
,
{
   page: 1,
   name: "Solar Panels", 
   isText: false,
   type: "checkbox",
   top: 146.28,
   left: 91.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Water Softeners", 
   isText: false,
   type: "checkbox",
   top: 163.56,
   left: 91.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Security Systems", 
   isText: false,
   type: "checkbox",
   top: 180.84,
   left: 91.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Satellite Systems including satellite dishes", 
   isText: false,
   type: "checkbox",
   top: 198.12,
   left: 91.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Text1181", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 331.202,
   left: 71.3458,
   width: 511.3112,
   height: 147.673
}
,
{
   page: 1,
   name: "If the box is checked Buyer and Seller have concurrently entered into a separate agreement for additional personal", 
   isText: false,
   type: "checkbox",
   top: 478.92,
   left: 77.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Text1182", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 581.893,
   left: 71.3458,
   width: 513.2742,
   height: 93.346
}
,
{
   page: 1,
   name: "Will", 
   isText: false,
   type: "checkbox",
   top: 676.2,
   left: 109.56,
   width: 10.08,
   height: 10.08
}
,
{
   page: 1,
   name: "Will Not assume the debt and obligations on the Encumbered Inclusions subject to Buyers", 
   isText: false,
   type: "checkbox",
   top: 676.2,
   left: 154.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "257 Parking and Storage Facilities The use or ownership of the following parking facilities", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 44.88,
   left: 469.08,
   width: 84.96,
   height: 12.84
}
,
{
   page: 2,
   name: "98", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 58.56,
   left: 77.04,
   width: 249.96,
   height: 12.84
}
,
{
   page: 2,
   name: "99", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 72.48,
   left: 77.04,
   width: 470.04,
   height: 12.84
}
,
{
   page: 2,
   name: "Text1183", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 138.419,
   left: 72.0003,
   width: 510.6557,
   height: 99.237
}
,
{
   page: 2,
   name: "Buyer_2", 
   isText: false,
   type: "checkbox",
   top: 238.2,
   left: 108.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Will_2", 
   isText: false,
   type: "checkbox",
   top: 238.2,
   left: 153.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "259", 
   isText: false,
   type: "checkbox",
   top: 284.28,
   left: 107.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "electricity Solar Power Plan that will remain in effect after Closing Buyer", 
   isText: false,
   type: "checkbox",
   top: 314.04,
   left: 386.52,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Will_3", 
   isText: false,
   type: "checkbox",
   top: 314.04,
   left: 431.16,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Text1184", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 375.674,
   left: 66.7639,
   width: 521.1291,
   height: 82.873
}
,
{
   page: 2,
   name: "271", 
   isText: false,
   type: "checkbox",
   top: 477,
   left: 103.8,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Text1185", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 489.602,
   left: 69.3821,
   width: 519.1649,
   height: 49.491
}
,
{
   page: 2,
   name: "Any deeded water rights will be conveyed by a good and sufficient", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 539.28,
   left: 346.44,
   width: 154.92,
   height: 12.84
}
,
{
   page: 2,
   name: "272", 
   isText: false,
   type: "checkbox",
   top: 561.48,
   left: 103.8,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "Text1186", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 585.821,
   left: 71.3457,
   width: 516.5473,
   height: 66.509
}
,
{
   page: 2,
   name: "273", 
   isText: false,
   type: "checkbox",
   top: 653.16,
   left: 105.24,
   width: 10.08,
   height: 10.08
}
,
{
   page: 2,
   name: "The Well Permit  is", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 742.56,
   left: 165.6,
   width: 400.08,
   height: 12.84
}
,
{
   page: 3,
   name: "274", 
   isText: false,
   type: "checkbox",
   top: 47.88,
   left: 107.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 3,
   name: "Time of Day Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 248.52,
   left: 402,
   width: 178.2,
   height: 20.76
}
,
{
   page: 3,
   name: "DateAlternative Earnest Money Deadline", 
   fontSize: 9,
   type: "date",
   top: 273.84,
   left: 321.36,
   width: 78.60,
   height: 17.76
}
,
{
   page: 3,
   name: "Alternative Earnest Money Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 270.84,
   left: 402.24,
   width: 177.72,
   height: 26.52
}
,
{
   page: 3,
   name: "DateRecord Title Deadline and Tax Certificate", 
   fontSize: 9,
   type: "date",
   top: 317.64,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Record Title Deadline and Tax Certificate_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 314.64,
   left: 402.12,
   width: 177.96,
   height: 24.72
}
,
{
   page: 3,
   name: "DateRecord Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 343.8,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Record Title Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 340.8,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateOffRecord Title Deadline", 
   fontSize: 9,
   type: "date",
   top: 370.2,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "OffRecord Title Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 367.2,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateOffRecord Title Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 396.6,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "OffRecord Title Objection Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 393.6,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateTitle Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 423.0,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Title Resolution Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 420,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateThird Party Right to PurchaseApprove Deadline", 
   fontSize: 9,
   type: "date",
   top: 445.846,
   left: 321.0,
   width: 79.32,
   height: 16.560
}
,
{
   page: 3,
   name: "Third Party Right to PurchaseApprove Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 446.846,
   left: 401.88,
   width: 178.44,
   height: 16.560
}
,
{
   page: 3,
   name: "DateAssociation Documents Deadline", 
   fontSize: 9,
   type: "date",
   top: 477.2,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Association Documents Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 475.2,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateAssociation Documents Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 503.6,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Association Documents Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 501.6,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateSellers Property Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 541.52,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "Sellers Property Disclosure Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 539.52,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateLeadBased Paint Disclosure Deadline", 
   fontSize: 9,
   type: "date",
   top: 567.92,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "LeadBased Paint Disclosure Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 565.92,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateNew Loan Application Deadline", 
   fontSize: 9,
   type: "date",
   top: 606.56,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "New Loan Application Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 604.56,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateNew Loan Terms Deadline", 
   fontSize: 9,
   type: "date",
   top: 632.96,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "New Loan Terms Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 630.96,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateNew Loan Availability Deadline", 
   fontSize: 9,
   type: "date",
   top: 659.36,
   left: 321.24,
   width: 78.84,
   height: 17.76
}
,
{
   page: 3,
   name: "New Loan Availability Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 657.36,
   left: 402.12,
   width: 177.96,
   height: 24.96
}
,
{
   page: 3,
   name: "DateBuyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 683.52,
   left: 321.0,
   width: 79.32,
   height: 17.76
}
,
{
   page: 3,
   name: "Buyers Credit Information Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 683.52,
   left: 401.88,
   width: 178.44,
   height: 17.52
}
,
{
   page: 3,
   name: "DateDisapproval of Buyers Credit Information Deadline", 
   fontSize: 9,
   type: "date",
   top: 702.24,
   left: 321.0,
   width: 79.32,
   height: 17.76
}
,
{
   page: 3,
   name: "Disapproval of Buyers Credit Information Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 702.24,
   left: 401.88,
   width: 178.44,
   height: 17.76
}
,
{
   page: 3,
   name: "DateExisting Loan Deadline", 
   fontSize: 9,
   type: "date",
   top: 721.2,
   left: 321.0,
   width: 79.32,
   height: 17.76
}
,
{
   page: 3,
   name: "Existing Loan Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 721.2,
   left: 401.88,
   width: 178.44,
   height: 17.76
}
,
{
   page: 3,
   name: "DateExisting Loan Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 740.16,
   left: 321.0,
   width: 79.32,
   height: 17.76
}
,
{
   page: 3,
   name: "Existing Loan Termination Deadline_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 740.16,
   left: 401.88,
   width: 178.44,
   height: 17.76
}
,
{
   page: 3,
   name: "Text1187", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 58.909,
   left: 72.6549,
   width: 513.9291,
   height: 46.873
}
,
{
   page: 4,
   name: "DateLoan Transfer Approval Deadline", 
   fontSize: 9,
   type: "date",
   top: 48.48,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Loan Transfer Approval Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 48.48,
   left: 402.0,
   width: 176.848,
   height: 17.52
}
,
{
   page: 4,
   name: "DateSeller or Private Financing Deadline", 
   fontSize: 9,
   type: "date",
   top: 66.2,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Seller or Private Financing Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 67.2,
   left: 402.0,
   width: 176.848,
   height: 17.76
}
,
{
   page: 4,
   name: "DateAppraisal Deadline", 
   fontSize: 9,
   type: "date",
   top: 102.24,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Appraisal Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 99.24,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateAppraisal Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 129.32,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Appraisal Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 127.32,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateAppraisal Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 157.4,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Appraisal Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 155.4,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateNew ILC or New Survey Deadline", 
   fontSize: 9,
   type: "date",
   top: 196.421,
   left: 321.36,
   width: 78.60,
   height: 17.520
}
,
{
   page: 4,
   name: "New ILC or New Survey Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 194.16,
   left: 402.0,
   width: 176.848,
   height: 19.724
}
,
{
   page: 4,
   name: "DateNew ILC or New Survey Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 219.12,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "New ILC or New Survey Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 215.12,
   left: 402.0,
   width: 176.848,
   height: 23.161
}
,
{
   page: 4,
   name: "DateNew ILC or New Survey Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 242.84,
   left: 321.36,
   width: 78.60,
   height: 16.52
}
,
{
   page: 4,
   name: "New ILC or New Survey Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 240.984,
   left: 402.0,
   width: 176.848,
   height: 20.925
}
,
{
   page: 4,
   name: "DateWater Rights Examination Deadline", 
   fontSize: 9,
   type: "date",
   top: 280.88,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Water Rights Examination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 275.88,
   left: 402.0,
   width: 176.848,
   height: 28.80
}
,
{
   page: 4,
   name: "DateMineral Rights Examination Deadline", 
   fontSize: 9,
   type: "date",
   top: 313.24,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Mineral Rights Examination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 309.24,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateInspection Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 341.32,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Inspection Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 337.32,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateInspection Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 369.4,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Inspection Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 365.4,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateInspection Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 397.48,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Inspection Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 393.48,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateProperty Insurance Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 425.56,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Property Insurance Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 421.56,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateDue Diligence Documents Delivery Deadline", 
   fontSize: 9,
   type: "date",
   top: 454.64,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Due Diligence Documents Delivery Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 449.64,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateDue Diligence Documents Objection Deadline", 
   fontSize: 9,
   type: "date",
   top: 481.72,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Due Diligence Documents Objection Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 477.72,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateDue Diligence Documents Resolution Deadline", 
   fontSize: 9,
   type: "date",
   top: 508.44,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Due Diligence Documents Resolution Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 506.44,
   left: 402.0,
   width: 176.848,
   height: 20.378
}
,
{
   page: 4,
   name: "DateConditional Sale Deadline", 
   fontSize: 9,
   type: "date",
   top: 529.4,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Conditional Sale Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 528.4,
   left: 402.0,
   width: 176.848,
   height: 20.235
}
,
{
   page: 4,
   name: "DateLeadBased Paint Termination Deadline", 
   fontSize: 9,
   type: "date",
   top: 551.36,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "LeadBased Paint Termination Deadline", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 551.36,
   left: 402.0,
   width: 176.848,
   height: 17.76
}
,
{
   page: 4,
   name: "DateClosing Date", 
   fontSize: 9,
   type: "date",
   top: 586.44,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Closing Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 581.44,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DatePossession Date", 
   fontSize: 9,
   type: "date",
   top: 614.52,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Possession Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 609.52,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "Possession Time", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 638.6,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "DateAcceptance Deadline Date", 
   fontSize: 9,
   type: "date",
   top: 670.003,
   left: 321.36,
   width: 78.60,
   height: 17.760
}
,
{
   page: 4,
   name: "Acceptance Deadline Date", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 666.68,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: "Acceptance Deadline Time", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 694.76,
   left: 402.0,
   width: 176.848,
   height: 26.52
}
,
{
   page: 4,
   name: " 2744", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 722.48,
   left: 69.0,
   width: 29.88,
   height: 17.76
}
,
{
   page: 4,
   name: "Acceptance Deadline Time44", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 722.48,
   left: 100.44,
   width: 219.00,
   height: 17.76
}
,
{
   page: 4,
   name: "Date Text23", 
   fontSize: 9,
   type: "date",
   top: 722.48,
   left: 321.36,
   width: 78.60,
   height: 17.76
}
,
{
   page: 4,
   name: "Text24", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 722.48,
   left: 402.0,
   width: 176.848,
   height: 17.76
}
,
{
   page: 4,
   name: " 2745", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 741.44,
   left: 69.0,
   width: 29.88,
   height: 17.52
}
,
{
   page: 4,
   name: "Acceptance Deadline Time45", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 741.44,
   left: 100.44,
   width: 219.00,
   height: 17.52
}
,
{
   page: 4,
   name: "Date Text25", 
   fontSize: 9,
   type: "date",
   top: 741.44,
   left: 321.36,
   width: 78.60,
   height: 17.52
}
,
{
   page: 4,
   name: "Text26", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 741.44,
   left: 402.0,
   width: 176.848,
   height: 17.52
}
,
{
   page: 5,
   name: "333 Deadlines If any deadline falls on a Saturday Sunday or federal or Colorado state holiday Holiday such Deadline", 
   isText: false,
   type: "checkbox",
   top: 307.08,
   left: 73.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "checked the deadline will not be extended", 
   isText: false,
   type: "checkbox",
   top: 307.08,
   left: 117.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 5,
   name: "Purchase Price", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 404.056,
   left: 288.836,
   width: 96.186,
   height: 15.467
}
,
{
   page: 5,
   name: "Earnest Money", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 424.96,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "New Loan", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 447.04,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "Assumption Balance", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.12,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "Private Financing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 491.2,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "Seller Financing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 513.28,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: " 477", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 535.36,
   left: 118.56,
   width: 52.20,
   height: 15.467
}
,
{
   page: 5,
   name: "Seller Financing7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 535.36,
   left: 172.56,
   width: 101.64,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_36", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 535.36,
   left: 289.523,
   width: 96.185,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_37", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 535.36,
   left: 400.072,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: " 478", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 557.44,
   left: 119.246,
   width: 52.200,
   height: 15.467
}
,
{
   page: 5,
   name: "Seller Financing8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 557.44,
   left: 172.56,
   width: 101.64,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_40", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 557.44,
   left: 288.836,
   width: 96.186,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_38", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 557.44,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "Cash at Closing", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 579.76,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_42", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 602.68,
   left: 288.836,
   width: 96.186,
   height: 15.467
}
,
{
   page: 5,
   name: "fill_43", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 602.68,
   left: 399.386,
   width: 85.374,
   height: 15.467
}
,
{
   page: 5,
   name: "Seller Concession The Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 631.44,
   left: 331.08,
   width: 109.92,
   height: 13.08
}
,
{
   page: 5,
   name: "undefined_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 716.64,
   left: 402.96,
   width: 165.00,
   height: 13.08
}
,
{
   page: 5,
   name: "Earnest Money Holder", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 730.56,
   left: 186.84,
   width: 279.96,
   height: 13.08
}
,
{
   page: 6,
   name: "funds that are immediately verifiable and available in an amount not less than the amount stated as Cash at Closing in  41", 
   isText: false,
   type: "checkbox",
   top: 480.6,
   left: 424.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Does", 
   isText: false,
   type: "checkbox",
   top: 480.6,
   left: 471.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "If either or both of the FHA or VA boxes are checked and Buyer closes the transaction using one of those loan types Seller", 
   isText: false,
   type: "checkbox",
   top: 629.64,
   left: 73.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Conventional", 
   isText: false,
   type: "checkbox",
   top: 629.64,
   left: 156.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "FHA", 
   isText: false,
   type: "checkbox",
   top: 629.64,
   left: 204.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "VA", 
   isText: false,
   type: "checkbox",
   top: 629.64,
   left: 245.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "Bond", 
   isText: false,
   type: "checkbox",
   top: 629.64,
   left: 294.6,
   width: 10.08,
   height: 10.08
}
,
{
   page: 6,
   name: "undefined_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 627.12,
   left: 341.04,
   width: 220.08,
   height: 12.36
}
,
{
   page: 6,
   name: "undefined_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 659.52,
   left: 463.8,
   width: 94.92,
   height: 12.36
}
,
{
   page: 7,
   name: "per", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 56.4,
   left: 301.8,
   width: 69.96,
   height: 12.36
}
,
{
   page: 7,
   name: "including principal", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 56.4,
   left: 389.64,
   width: 99.96,
   height: 12.36
}
,
{
   page: 7,
   name: "per annum and also including escrow for the following as indicated", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 69.6,
   left: 204.6,
   width: 60.0,
   height: 12.36
}
,
{
   page: 7,
   name: "Real Estate Taxes", 
   isText: false,
   type: "checkbox",
   top: 91.32,
   left: 63.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Property Insurance Premium", 
   isText: false,
   type: "checkbox",
   top: 91.32,
   left: 165.96,
   width: 10.766,
   height: 10.08
}
,
{
   page: 7,
   name: "Mortgage Insurance Premium and", 
   isText: false,
   type: "checkbox",
   top: 91.32,
   left: 318.6,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Check Box45", 
   isText: false,
   type: "checkbox",
   top: 111.148,
   left: 63.807,
   width: 10.080,
   height: 10.080
}
,
{
   page: 7,
   name: "undefined_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 108.24,
   left: 81.72,
   width: 465.00,
   height: 12.36
}
,
{
   page: 7,
   name: "At the time of assumption the new", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 127.44,
   left: 310.44,
   width: 99.96,
   height: 12.36
}
,
{
   page: 7,
   name: "per annum and the new payment will not exceed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 140.64,
   left: 176.76,
   width: 75.00,
   height: 12.36
}
,
{
   page: 7,
   name: "per_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.84,
   left: 78.48,
   width: 79.92,
   height: 12.36
}
,
{
   page: 7,
   name: "principal and interest plus escrow if any If the actual principal", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.84,
   left: 176.16,
   width: 110.16,
   height: 12.36
}
,
{
   page: 7,
   name: "or if any other terms or provisions of the", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 186.24,
   left: 267.96,
   width: 120.00,
   height: 12.36
}
,
{
   page: 7,
   name: "Will_4", 
   isText: false,
   type: "checkbox",
   top: 221.16,
   left: 130.2,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Will Not be released from liability on said loan If applicable compliance with the", 
   isText: false,
   type: "checkbox",
   top: 221.16,
   left: 174.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "on or before Loan Transfer Approval Deadline", 
   isText: false,
   type: "checkbox",
   top: 248.76,
   left: 73.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "at Closing of an appropriate letter of commitment from lender", 
   isText: false,
   type: "checkbox",
   top: 262.68,
   left: 73.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "in an amount not", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 279.84,
   left: 293.4,
   width: 205.92,
   height: 12.36
}
,
{
   page: 7,
   name: "undefined_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 293.76,
   left: 119.76,
   width: 84.96,
   height: 12.36
}
,
{
   page: 7,
   name: "Buyer_3", 
   isText: false,
   type: "checkbox",
   top: 439.8,
   left: 392.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "Seller will deliver the", 
   isText: false,
   type: "checkbox",
   top: 439.8,
   left: 444.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 7,
   name: "days before Seller or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 450.48,
   left: 352.08,
   width: 99.96,
   height: 12.36
}
,
{
   page: 8,
   name: "55 Buyer Representation of Principal Residence Buyer represents that Buyer will occupy the Property as Buyers", 
   isText: false,
   type: "checkbox",
   top: 525.48,
   left: 339.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 9,
   name: "The purchaser", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 200.64,
   left: 421.8,
   width: 84.96,
   height: 12.36
}
,
{
   page: 9,
   name: "64 Cost of Appraisal Cost of the Appraisal to be obtained after the date of this Contract must be timely paid by", 
   isText: false,
   type: "checkbox",
   top: 473.16,
   left: 63.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 9,
   name: "company lenders agent or all three", 
   isText: false,
   type: "checkbox",
   top: 473.16,
   left: 116.28,
   width: 10.08,
   height: 10.08
}
,
{
   page: 10,
   name: "811", 
   isText: false,
   type: "checkbox",
   top: 693.72,
   left: 103.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 10,
   name: "Price or if this box is checked", 
   isText: false,
   type: "checkbox",
   top: 733.32,
   left: 201.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "to furnish the owners title insurance policy at Buyers expense On or before Record Title Deadline Buyer must furnish to", 
   isText: false,
   type: "checkbox",
   top: 64.92,
   left: 103.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "Extended Coverage OEC If the Title Commitment is to contain OEC it will commit to delete or insure over the standard", 
   isText: false,
   type: "checkbox",
   top: 143.16,
   left: 366.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "Will_5", 
   isText: false,
   type: "checkbox",
   top: 143.16,
   left: 411.24,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "Regardless of whether the Contract requires OEC the Title Insurance Commitment may not provide OEC or delete or insure", 
   isText: false,
   type: "checkbox",
   top: 228.36,
   left: 72.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "Buyer_4", 
   isText: false,
   type: "checkbox",
   top: 228.36,
   left: 124.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "Seller_2", 
   isText: false,
   type: "checkbox",
   top: 228.36,
   left: 176.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "OneHalf by Buyer and OneHalf by Seller", 
   isText: false,
   type: "checkbox",
   top: 228.36,
   left: 385.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 11,
   name: "undefined_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 225.866,
   left: 431.64,
   width: 135.00,
   height: 12.334
}
,
{
   page: 12,
   name: "undefined_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 336.449,
   left: 62.76,
   width: 500.04,
   height: 14.791
}
,
{
   page: 12,
   name: "metropolitan districts that affect the Property Tax Certificate must be delivered to Buyer on or before Record Title", 
   isText: false,
   type: "checkbox",
   top: 361.32,
   left: 276.6,
   width: 10.08,
   height: 10.08
}
,
{
   page: 12,
   name: "Seller_3", 
   isText: false,
   type: "checkbox",
   top: 361.32,
   left: 327.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "New ILC or New Survey If the box is checked 1", 
   isText: false,
   type: "checkbox",
   top: 581.88,
   left: 306.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "2_2", 
   isText: false,
   type: "checkbox",
   top: 595.56,
   left: 81,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "New Survey in the form of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 592.56,
   left: 211.08,
   width: 194.88,
   height: 12.84
}
,
{
   page: 13,
   name: "Ordering of New ILC or New Survey", 
   isText: false,
   type: "checkbox",
   top: 629.16,
   left: 273.24,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "Seller_4", 
   isText: false,
   type: "checkbox",
   top: 629.16,
   left: 324.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "Closing by", 
   isText: false,
   type: "checkbox",
   top: 688.68,
   left: 131.88,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "Seller_5", 
   isText: false,
   type: "checkbox",
   top: 688.68,
   left: 183,
   width: 10.08,
   height: 10.08
}
,
{
   page: 13,
   name: "Buyer or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 685.68,
   left: 243.48,
   width: 320.04,
   height: 12.84
}
,
{
   page: 13,
   name: "opinion of title if an Abstract of Title and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 719.28,
   left: 243.84,
   width: 315.00,
   height: 12.84
}
,
{
   page: 15,
   name: "Text27", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 403.856,
   left: 80.202,
   width: 501.282,
   height: 65.224
}
,
{
   page: 15,
   name: "Text28", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 638.839,
   left: 85.7458,
   width: 497.1652,
   height: 110.5067
}
,
{
   page: 16,
   name: "undefined_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 344.95,
   left: 62.76,
   width: 504.96,
   height: 14.93
}
,
{
   page: 16,
   name: "Addendum disclosing the source of potable water for the Property", 
   isText: false,
   type: "checkbox",
   top: 442.2,
   left: 95.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 16,
   name: "Does_2", 
   isText: false,
   type: "checkbox",
   top: 442.2,
   left: 142.68,
   width: 10.08,
   height: 10.08
}
,
{
   page: 16,
   name: "Note to Buyer SOME WATER PROVIDERS RELY TO VARYING DEGREES ON NONRENEWABLE GROUND", 
   isText: false,
   type: "checkbox",
   top: 476.28,
   left: 63.72,
   width: 10.08,
   height: 10.08
}
,
{
   page: 16,
   name: "There is No Well Buyer", 
   isText: false,
   type: "checkbox",
   top: 476.28,
   left: 191.88,
   width: 10.08,
   height: 10.08
}
,
{
   page: 16,
   name: "Does_3", 
   isText: false,
   type: "checkbox",
   top: 476.28,
   left: 238.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 17,
   name: "Closing Instructions Colorado Real Estate Commissions Closing Instructions", 
   isText: false,
   type: "checkbox",
   top: 648.36,
   left: 420.84,
   width: 10.08,
   height: 10.08
}
,
{
   page: 17,
   name: "Are", 
   isText: false,
   type: "checkbox",
   top: 648.36,
   left: 463.56,
   width: 10.08,
   height: 10.08
}
,
{
   page: 17,
   name: "undefined_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 718.08,
   left: 62.76,
   width: 420.00,
   height: 12.36
}
,
{
   page: 18,
   name: "special warranty deed", 
   isText: false,
   type: "checkbox",
   top: 151.08,
   left: 64.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "general warranty deed", 
   isText: false,
   type: "checkbox",
   top: 151.08,
   left: 178.2,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "bargain and sale deed", 
   isText: false,
   type: "checkbox",
   top: 151.08,
   left: 293.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "quit claim deed", 
   isText: false,
   type: "checkbox",
   top: 151.08,
   left: 406.2,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "personal representatives deed", 
   isText: false,
   type: "checkbox",
   top: 165.24,
   left: 64.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller provided another deed is not selected must execute and deliver a good and sufficient special warranty deed to Buyer at", 
   isText: false,
   type: "checkbox",
   top: 165.24,
   left: 214.44,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "deed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 161.76,
   left: 232.92,
   width: 219.96,
   height: 13.32
}
,
{
   page: 18,
   name: "Buyer_5", 
   isText: false,
   type: "checkbox",
   top: 398.04,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_6", 
   isText: false,
   type: "checkbox",
   top: 398.04,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_2", 
   isText: false,
   type: "checkbox",
   top: 398.04,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Other_2", 
   isText: false,
   type: "checkbox",
   top: 417.24,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "undefined_11", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 413.76,
   left: 231.48,
   width: 320.04,
   height: 13.32
}
,
{
   page: 18,
   name: "Buyer_6", 
   isText: false,
   type: "checkbox",
   top: 520.68,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_7", 
   isText: false,
   type: "checkbox",
   top: 520.68,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_3", 
   isText: false,
   type: "checkbox",
   top: 520.68,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA", 
   isText: false,
   type: "checkbox",
   top: 520.68,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Buyer_7", 
   isText: false,
   type: "checkbox",
   top: 572.28,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_8", 
   isText: false,
   type: "checkbox",
   top: 572.28,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_4", 
   isText: false,
   type: "checkbox",
   top: 572.28,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA_2", 
   isText: false,
   type: "checkbox",
   top: 572.28,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Buyer_8", 
   isText: false,
   type: "checkbox",
   top: 610.68,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_9", 
   isText: false,
   type: "checkbox",
   top: 610.68,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_5", 
   isText: false,
   type: "checkbox",
   top: 610.68,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA_3", 
   isText: false,
   type: "checkbox",
   top: 610.68,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Buyer_9", 
   isText: false,
   type: "checkbox",
   top: 647.4,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_10", 
   isText: false,
   type: "checkbox",
   top: 647.4,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_6", 
   isText: false,
   type: "checkbox",
   top: 647.4,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA_4", 
   isText: false,
   type: "checkbox",
   top: 647.4,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Buyer_10", 
   isText: false,
   type: "checkbox",
   top: 684.12,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_11", 
   isText: false,
   type: "checkbox",
   top: 684.12,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_7", 
   isText: false,
   type: "checkbox",
   top: 684.12,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA_5", 
   isText: false,
   type: "checkbox",
   top: 684.12,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Buyer_11", 
   isText: false,
   type: "checkbox",
   top: 735.96,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "Seller_12", 
   isText: false,
   type: "checkbox",
   top: 735.96,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "OneHalf by Buyer and OneHalf by Seller_8", 
   isText: false,
   type: "checkbox",
   top: 735.96,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 18,
   name: "NA_6", 
   isText: false,
   type: "checkbox",
   top: 735.96,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "for", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 60.517,
   left: 70.32,
   width: 84.96,
   height: 12.563
}
,
{
   page: 19,
   name: "Water DistrictMunicipality", 
   isText: false,
   type: "checkbox",
   top: 80.76,
   left: 99.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Augmentation Membership", 
   isText: false,
   type: "checkbox",
   top: 98.28,
   left: 99.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Water Stock", 
   isText: false,
   type: "checkbox",
   top: 80.76,
   left: 253.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "undefined_12", 
   isText: false,
   type: "checkbox",
   top: 98.28,
   left: 253.08,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Check Box46", 
   isText: false,
   type: "checkbox",
   top: 98.798,
   left: 419.891,
   width: 10.080,
   height: 10.080
}
,
{
   page: 19,
   name: "undefined_13", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 94.184,
   left: 438.0,
   width: 114.96,
   height: 13.936
}
,
{
   page: 19,
   name: "Buyer_12", 
   isText: false,
   type: "checkbox",
   top: 133.32,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Seller_13", 
   isText: false,
   type: "checkbox",
   top: 133.32,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "OneHalf by Buyer and OneHalf by Seller_9", 
   isText: false,
   type: "checkbox",
   top: 133.32,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "NA_7", 
   isText: false,
   type: "checkbox",
   top: 133.32,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Buyer_13", 
   isText: false,
   type: "checkbox",
   top: 181.56,
   left: 189.96,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Seller_14", 
   isText: false,
   type: "checkbox",
   top: 181.56,
   left: 242.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "OneHalf by Buyer and OneHalf by Seller_10", 
   isText: false,
   type: "checkbox",
   top: 181.56,
   left: 293.64,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "NA_8", 
   isText: false,
   type: "checkbox",
   top: 181.56,
   left: 502.92,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "IS a foreign person for purposes of US income taxation If the box in this Section is not checked Seller", 
   isText: false,
   type: "checkbox",
   top: 257.88,
   left: 103.8,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Taxes for the Calendar Year Immediately Preceding Closing", 
   isText: false,
   type: "checkbox",
   top: 474.12,
   left: 86.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Most Recent Mill Levy and Most Recent Assessed or Actual Valuation per the county assessor adjusted by", 
   isText: false,
   type: "checkbox",
   top: 494.04,
   left: 86.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Other_3", 
   isText: false,
   type: "checkbox",
   top: 527.64,
   left: 86.76,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "undefined_14", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 523.544,
   left: 133.2,
   width: 405.0,
   height: 13.936
}
,
{
   page: 19,
   name: "Rents Actually Received", 
   isText: false,
   type: "checkbox",
   top: 547.32,
   left: 205.56,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Accrued", 
   isText: false,
   type: "checkbox",
   top: 547.32,
   left: 335.88,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Text29", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 603.145,
   left: 72.6549,
   width: 479.3271,
   height: 13.767
}
,
{
   page: 19,
   name: "Buyer_14", 
   isText: false,
   type: "checkbox",
   top: 711.96,
   left: 79.56,
   width: 10.08,
   height: 10.08
}
,
{
   page: 19,
   name: "Seller Except however any special assessment by the Association for improvements that have been", 
   isText: false,
   type: "checkbox",
   top: 711.96,
   left: 132.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 20,
   name: "against the Property except the current regular assessments and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 43.2,
   left: 328.32,
   width: 215.16,
   height: 12.36
}
,
{
   page: 20,
   name: "additionally liable to Buyer notwithstanding  202 If Seller is in Default for payment of", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.64,
   left: 428.28,
   width: 56.64,
   height: 12.36
}
,
{
   page: 21,
   name: "Buyer will be paid to Seller and retained by Seller It is agreed that the Earnest Money is not a penalty and the Parties", 
   isText: false,
   type: "checkbox",
   top: 222.36,
   left: 108.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 22,
   name: "undefined_15", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 582.48,
   left: 62.76,
   width: 390.00,
   height: 12.36
}
,
{
   page: 23,
   name: "party beneficiary under this provision only The amount paid by Seller under this provision is in addition to any other amounts", 
   isText: false,
   type: "checkbox",
   top: 162.36,
   left: 92.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 23,
   name: "of the Purchase Price or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 157.84,
   left: 109.966,
   width: 40.080,
   height: 14.243
}
,
{
   page: 23,
   name: "by Seller Buyers brokerage firm is an intended third", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 157.84,
   left: 266.446,
   width: 79.920,
   height: 14.243
}
,
{
   page: 23,
   name: "Buyer and Buyers brokerage firm This amount may be modified between Buyer and Buyers brokerage firm outside of this", 
   isText: false,
   type: "checkbox",
   top: 207.96,
   left: 92.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 23,
   name: "of the Purchase Price or_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 203.44,
   left: 109.966,
   width: 40.080,
   height: 14.243
}
,
{
   page: 23,
   name: "by Buyer pursuant to a separate agreement between", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 203.44,
   left: 266.446,
   width: 75.000,
   height: 14.243
}
,
{
   page: 23,
   name: "firm and Sellers brokerage firm", 
   isText: false,
   type: "checkbox",
   top: 253.56,
   left: 92.04,
   width: 10.08,
   height: 10.08
}
,
{
   page: 23,
   name: "of the Purchase Price or_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249.04,
   left: 109.966,
   width: 40.080,
   height: 14.243
}
,
{
   page: 23,
   name: "by a separate agreement between Buyers brokerage", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249.04,
   left: 266.446,
   width: 75.000,
   height: 14.243
}
,
{
   page: 23,
   name: "Text30", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 345.22,
   left: 50.1794,
   width: 541.7616,
   height: 406.2141
}
,
{
   page: 24,
   name: "of this Contract", 
   isText: false,
   type: "checkbox",
   top: 234.84,
   left: 108.12,
   width: 10.08,
   height: 10.08
}
,
{
   page: 24,
   name: "Address 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 474.12,
   left: 126.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Address 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 491.64,
   left: 126.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Address 1_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 474.12,
   left: 396.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Address 2_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 491.64,
   left: 396.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Phone No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 509.16,
   left: 126.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Phone No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 509.16,
   left: 396.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Fax No", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 526.68,
   left: 126.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Fax No_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 526.68,
   left: 396.48,
   width: 174.96,
   height: 16.80
}
,
{
   page: 24,
   name: "Email Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 544.2,
   left: 126.48,
   width: 174.96,
   height: 16.8
}
,
{
   page: 24,
   name: "Email Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 544.2,
   left: 396.48,
   width: 174.96,
   height: 16.8
}
,
{
   page: 24,
   name: "Text31", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 37.964,
   left: 53.0184,
   width: 541.7616,
   height: 105.018
}
,
{
   page: 24,
   name: "Text32", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 175.419,
   left: 54.8243,
   width: 540.3897,
   height: 58.363
}
,
{
   page: 24,
   name: "Text33", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 280.801,
   left: 60.4708,
   width: 532.1562,
   height: 70.027
}
,
{
   page: 24,
   name: "Text34", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 401.008,
   left: 124.153,
   width: 193.910,
   height: 24.058
}
,
{
   page: 24,
   name: "Text35", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 401.008,
   left: 394.199,
   width: 193.910,
   height: 24.058
}
,
{
   page: 24,
   name: "Text36", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 592.217,
   left: 124.839,
   width: 193.910,
   height: 24.059
}
,
{
   page: 24,
   name: "Text37", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 589.465,
   left: 394.199,
   width: 193.910,
   height: 24.058
}
,
{
   page: 25,
   name: "Text39", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 64.119,
   left: 393.742,
   width: 200.772,
   height: 25.430
}
,
{
   page: 25,
   name: "Text38", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 64.119,
   left: 118.655,
   width: 200.771,
   height: 25.430
}
,
{
   page: 25,
   name: "Address 1_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 142.56,
   left: 126.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Address 2_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 160.08,
   left: 126.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Phone No_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 177.6,
   left: 126.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Fax No_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 195.12,
   left: 126.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Email Address_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 212.64,
   left: 126.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Address 1_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 142.56,
   left: 396.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Address 2_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 160.08,
   left: 396.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Phone No_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 177.6,
   left: 396.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Fax No_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 195.12,
   left: 396.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Email Address_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 212.64,
   left: 396.48,
   width: 174.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Does_4", 
   isText: false,
   type: "checkbox",
   top: 435.48,
   left: 108.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 25,
   name: "Does Not acknowledge receipt of Earnest Money deposit Broker agrees that if Brokerage Firm is", 
   isText: false,
   type: "checkbox",
   top: 435.48,
   left: 155.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 25,
   name: "Buyers Agent", 
   isText: false,
   type: "checkbox",
   top: 519.48,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 25,
   name: "TransactionBroker in this transaction", 
   isText: false,
   type: "checkbox",
   top: 534.6,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 25,
   name: "Customer Broker has no brokerage relationship with Buyer See  B for Brokers brokerage relationship with Seller", 
   isText: false,
   type: "checkbox",
   top: 549.48,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 25,
   name: "Brokerage Firms Name", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 640.52,
   left: 198.48,
   width: 354.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Brokerage Firms License 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 656.04,
   left: 198.48,
   width: 354.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Brokerage Firms License 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 670.92,
   left: 198.48,
   width: 354.96,
   height: 14.056
}
,
{
   page: 25,
   name: "Brokerage Firms License 3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 686.04,
   left: 198.48,
   width: 354.96,
   height: 14.0556
}
,
{
   page: 25,
   name: "Text40", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 251.242,
   left: 120.341,
   width: 200.771,
   height: 25.431
}
,
{
   page: 25,
   name: "Text41", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 251.242,
   left: 394.429,
   width: 200.771,
   height: 25.431
}
,
{
   page: 25,
   name: "signature broker buyer", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 715.5993,
   left: 198.282,
   width: 184.991,
   height: 15.8251
}
,
{
   page: 25,
   name: "Date_2", 
   fontSize: 9,
   type: "date",
   top: 715.9044,
   left: 393.48,
   width: 90.00,
   height: 14.0556
}
,
{
   page: 26,
   name: "1_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 41.4,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "2_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 56.28,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "3_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 71.4,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "4_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 86.28,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "5_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 101.16,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "undefined_16", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 131.16,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "signature cobroker buyer", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 160.12,
   left: 197.106,
   width: 187.735,
   height: 13.081
}
,
{
   page: 26,
   name: "Date_3", 
   fontSize: 9,
   type: "date",
   top: 158.04,
   left: 393.48,
   width: 90.00,
   height: 14.16
}
,
{
   page: 26,
   name: "Does_5", 
   isText: false,
   type: "checkbox",
   top: 231,
   left: 108.36,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Does Not acknowledge receipt of Earnest Money deposit Broker agrees that if Brokerage Firm is_2", 
   isText: false,
   type: "checkbox",
   top: 231,
   left: 155.4,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Sellers Agent", 
   isText: false,
   type: "checkbox",
   top: 315,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "TransactionBroker in this transaction_2", 
   isText: false,
   type: "checkbox",
   top: 330.12,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Customer Broker has no brokerage relationship with Seller See  A for Brokers brokerage relationship with Buyer", 
   isText: false,
   type: "checkbox",
   top: 345,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Seller_15", 
   isText: false,
   type: "checkbox",
   top: 389.88,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Buyer_15", 
   isText: false,
   type: "checkbox",
   top: 404.76,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "Other_4", 
   isText: false,
   type: "checkbox",
   top: 420.6,
   left: 75.48,
   width: 10.08,
   height: 10.08
}
,
{
   page: 26,
   name: "This Brokers Acknowledgments and Compensation Disclosure is for disclosure purposes only and does NOT create any", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.52,
   left: 122.28,
   width: 429.00,
   height: 14.16
}
,
{
   page: 26,
   name: "claim for compensation Any agreement to pay compensation must be entered into separately and apart from this provision", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 480.64,
   left: 198.48,
   width: 354.96,
   height: 12.788
}
,
{
   page: 26,
   name: "Brokerage Firms License 1_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 495.52,
   left: 198.48,
   width: 354.96,
   height: 12.788
}
,
{
   page: 26,
   name: "Brokerage Firms License 2_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 510.4,
   left: 198.48,
   width: 354.96,
   height: 12.788
}
,
{
   page: 26,
   name: "Brokerage Firms License 3_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 525.28,
   left: 198.48,
   width: 354.96,
   height: 12.788
}
,
{
   page: 26,
   name: "signature broker seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 554.718,
   left: 198.983,
   width: 183.618,
   height: 15.139
}
,
{
   page: 26,
   name: "Date_4", 
   fontSize: 9,
   type: "date",
   top: 554.28,
   left: 393.48,
   width: 90.00,
   height: 14.16
}
,
{
   page: 26,
   name: "1_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 597.28,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "2_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 612.16,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "3_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 627.04,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "4_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 641.92,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "5_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 657.04,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "undefined_17", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 685.8,
   left: 198.48,
   width: 354.96,
   height: 14.16
}
,
{
   page: 26,
   name: "signature cobroker seller", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 713.1187,
   left: 200.946,
   width: 181.561,
   height: 15.8251
}
,
{
   page: 26,
   name: "Date_5", 
   fontSize: 9,
   type: "date",
   top: 712.92,
   left: 393.48,
   width: 90.00,
   height: 14.16
}
] }
