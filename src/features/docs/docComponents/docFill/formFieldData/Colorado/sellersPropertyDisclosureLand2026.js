export function sellersPropertyDisclosureLand2026() {
return [   // Release 2025-12-10 19:36:30
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 28.673,
 left: 53,
 width: 240,
 height: 36,
 }, 
{
   page: 0,
   name: "Date SPD completed by <PERSON><PERSON>", 
   fontSize: 9,
   type: "date",
   top: 533.64,
   left: 168.0,
   width: 381.0,
   height: 13.353
}
,
{
   page: 0,
   name: "Address Full", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 551.64,
   left: 80.16,
   width: 468.84,
   height: 13.353
}
,
{
   page: 0,
   name: "<PERSON><PERSON>", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 569.64,
   left: 69.12,
   width: 479.88,
   height: 13.353
}
,
{
   page: 0,
   name: "Listing Year Built", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 587.64,
   left: 167.28,
   width: 381.72,
   height: 13.353
}
,
{
   page: 1,
   name: "CommentsFlooding", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 116.28,
   left: 190.56,
   width: 402.12,
   height: 81.48
}
,
{
   page: 1,
   name: "CommentsDrainage issues", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 199.56,
   left: 190.56,
   width: 402.12,
   height: 71.16
}
,
{
   page: 1,
   name: "CommentsGrading issues", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 272.52,
   left: 190.56,
   width: 402.12,
   height: 81.48
}
,
{
   page: 1,
   name: "Grading issues4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.8,
   left: 42,
   width: 118.68,
   height: 40.2
}
,
{
   page: 1,
   name: "Comments4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 355.8,
   left: 190.56,
   width: 402.12,
   height: 40.2
}
,
{
   page: 1,
   name: "CommentsDrainage or retention ponds dams storm water detention basins or other similar facilities", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 466.2,
   left: 190.56,
   width: 402.12,
   height: 81.48
}
,
{
   page: 1,
   name: "Drainage or retention ponds dams storm water detention basins or other similar facilities6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 549.48,
   left: 42,
   width: 118.68,
   height: 50.52
}
,
{
   page: 1,
   name: "Comments6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 549.48,
   left: 190.56,
   width: 402.12,
   height: 50.52
}
,
{
   page: 1,
   name: "Type of water supply Public Community Well Shared Well Other None If the Property is served by a Well a copy of the Well Permit Is Is Not provided Well Permit  Drilling Records Are Are not provided Shared Well Agreement Yes No", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 679.004,
   left: 37.44,
   width: 555.24,
   height: 30.3528
}
,
{
   page: 1,
   name: "Is Not provided Well Permit", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 645.051,
   left: 433.56,
   width: 157.44,
   height: 14.709
}
,
{
   page: 1,
   name: "Check Box1080", 
   isText: false,
   type: "checkbox",
   top: 122.524,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1081", 
   isText: false,
   type: "checkbox",
   top: 205.808,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1082", 
   isText: false,
   type: "checkbox",
   top: 277.081,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1083", 
   isText: false,
   type: "checkbox",
   top: 359.564,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1084", 
   isText: false,
   type: "checkbox",
   top: 470.076,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1085", 
   isText: false,
   type: "checkbox",
   top: 552.56,
   left: 168.17,
   width: 13.68,
   height: 13.68
}
,
{
   page: 1,
   name: "Check Box1086", 
   isText: false,
   type: "checkbox",
   top: 634.243,
   left: 124.126,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1087", 
   isText: false,
   type: "checkbox",
   top: 634.243,
   left: 168.971,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1088", 
   isText: false,
   type: "checkbox",
   top: 635.043,
   left: 231.435,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1089", 
   isText: false,
   type: "checkbox",
   top: 634.243,
   left: 272.276,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1090", 
   isText: false,
   type: "checkbox",
   top: 634.243,
   left: 340.345,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1091", 
   isText: false,
   type: "checkbox",
   top: 634.243,
   left: 381.987,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1092", 
   isText: false,
   type: "checkbox",
   top: 649.458,
   left: 264.268,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1093", 
   isText: false,
   type: "checkbox",
   top: 649.458,
   left: 294.699,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1094", 
   isText: false,
   type: "checkbox",
   top: 665.474,
   left: 106.508,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1095", 
   isText: false,
   type: "checkbox",
   top: 665.474,
   left: 144.146,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1096", 
   isText: false,
   type: "checkbox",
   top: 664.673,
   left: 330.735,
   width: 13.680,
   height: 13.680
}
,
{
   page: 1,
   name: "Check Box1097", 
   isText: false,
   type: "checkbox",
   top: 664.673,
   left: 366.772,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "CommentsZoning violation variance conditional use violation of an enforceable PUD or non conforming use", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 109.56,
   left: 194.88,
   width: 397.80,
   height: 63.00
}
,
{
   page: 2,
   name: "CommentsNotice or threat of condemnation proceedings", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 174.36,
   left: 194.88,
   width: 397.80,
   height: 63.24
}
,
{
   page: 2,
   name: "CommentsNotice of any adverse conditions from any governmental or quasi governmental agency that have not been resolved", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 239.4,
   left: 194.88,
   width: 397.80,
   height: 55.32
}
,
{
   page: 2,
   name: "CommentsNotice of zoning action related to the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 296.52,
   left: 194.88,
   width: 397.80,
   height: 63.00
}
,
{
   page: 2,
   name: "CommentsBuilding code city or county violations", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 361.32,
   left: 194.88,
   width: 397.80,
   height: 53.88
}
,
{
   page: 2,
   name: "CommentsViolation of restrictive covenants or owners association rules or regulations", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 417,
   left: 194.88,
   width: 397.80,
   height: 53.88
}
,
{
   page: 2,
   name: "CommentsOther legal action", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 472.68,
   left: 194.88,
   width: 397.80,
   height: 53.88
}
,
{
   page: 2,
   name: "CommentsAny part of the Property leased to others written or oral", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 528.36,
   left: 194.88,
   width: 397.80,
   height: 44.76
}
,
{
   page: 2,
   name: "CommentsArcheological or historical designation on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 574.92,
   left: 194.88,
   width: 397.80,
   height: 44.76
}
,
{
   page: 2,
   name: "CommentsThreatened or Endangered species on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 621.48,
   left: 194.88,
   width: 397.80,
   height: 44.76
}
,
{
   page: 2,
   name: "CommentsGrandfathered conditions or uses", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.04,
   left: 194.88,
   width: 397.80,
   height: 44.52
}
,
{
   page: 2,
   name: "Grandfathered conditions or uses12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 714.36,
   left: 37.44,
   width: 127.80,
   height: 35.64
}
,
{
   page: 2,
   name: "Comments12", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 714.36,
   left: 194.88,
   width: 397.80,
   height: 35.64
}
,
{
   page: 2,
   name: "Check Box1098", 
   isText: false,
   type: "checkbox",
   top: 113.715,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1099", 
   isText: false,
   type: "checkbox",
   top: 178.581,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1100", 
   isText: false,
   type: "checkbox",
   top: 242.646,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1101", 
   isText: false,
   type: "checkbox",
   top: 301.105,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1102", 
   isText: false,
   type: "checkbox",
   top: 365.971,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1103", 
   isText: false,
   type: "checkbox",
   top: 420.426,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1104", 
   isText: false,
   type: "checkbox",
   top: 475.682,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1105", 
   isText: false,
   type: "checkbox",
   top: 530.938,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1106", 
   isText: false,
   type: "checkbox",
   top: 577.385,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1107", 
   isText: false,
   type: "checkbox",
   top: 623.832,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1108", 
   isText: false,
   type: "checkbox",
   top: 671.881,
   left: 173.776,
   width: 13.680,
   height: 13.680
}
,
{
   page: 2,
   name: "Check Box1109", 
   isText: false,
   type: "checkbox",
   top: 716.7261,
   left: 173.776,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 3,
   name: "CommentsAny access problems issues or concerns", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 99.24,
   left: 190.56,
   width: 402.12,
   height: 72.12
}
,
{
   page: 3,
   name: "CommentsRoads trails paths or driveways through the Property used by others", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 173.16,
   left: 190.56,
   width: 402.12,
   height: 81.48
}
,
{
   page: 3,
   name: "CommentsPublic highway or county road bordering the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.44,
   left: 190.56,
   width: 402.12,
   height: 72.36
}
,
{
   page: 3,
   name: "CommentsAny proposed or existing transportation project that affects or is expected to affect the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 330.6,
   left: 190.56,
   width: 402.12,
   height: 72.36
}
,
{
   page: 3,
   name: "CommentsEncroachments boundary disputes or unrecorded easements", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 404.76,
   left: 190.56,
   width: 402.12,
   height: 63.00
}
,
{
   page: 3,
   name: "CommentsShared or common areas with adjoining properties", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 469.56,
   left: 190.56,
   width: 402.12,
   height: 63.24
}
,
{
   page: 3,
   name: "CommentsRequirements for curb gravelpaving landscaping", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 534.6,
   left: 190.56,
   width: 402.12,
   height: 63.0
}
,
{
   page: 3,
   name: "CommentsAny limitations on parking or access due to size number of vehicles or type of vehicles in the past year", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 599.4,
   left: 190.56,
   width: 402.12,
   height: 63.24
}
,
{
   page: 3,
   name: "Any limitations on parking or access due to size number of vehicles or type of vehicles in the past year9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 664.44,
   left: 37.44,
   width: 123.24,
   height: 44.52
}
,
{
   page: 3,
   name: "Comments9", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 664.44,
   left: 190.56,
   width: 402.12,
   height: 44.52
}
,
{
   page: 3,
   name: "Any limitations on parking or access due to size number of vehicles or type of vehicles in the past year10", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 710.76,
   left: 37.44,
   width: 123.24,
   height: 35.64
}
,
{
   page: 3,
   name: "Comments10", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 710.76,
   left: 190.56,
   width: 402.12,
   height: 35.64
}
,
{
   page: 3,
   name: "Check Box1110", 
   isText: false,
   type: "checkbox",
   top: 103.305,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1111", 
   isText: false,
   type: "checkbox",
   top: 176.979,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1112", 
   isText: false,
   type: "checkbox",
   top: 261.064,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1113", 
   isText: false,
   type: "checkbox",
   top: 335.54,
   left: 168.17,
   width: 13.68,
   height: 13.68
}
,
{
   page: 3,
   name: "Check Box1114", 
   isText: false,
   type: "checkbox",
   top: 407.613,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1115", 
   isText: false,
   type: "checkbox",
   top: 474.08,
   left: 168.17,
   width: 13.68,
   height: 13.68
}
,
{
   page: 3,
   name: "Check Box1116", 
   isText: false,
   type: "checkbox",
   top: 538.145,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1117", 
   isText: false,
   type: "checkbox",
   top: 603.011,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1118", 
   isText: false,
   type: "checkbox",
   top: 667.076,
   left: 168.17,
   width: 13.68,
   height: 13.680
}
,
{
   page: 3,
   name: "Check Box1119", 
   isText: false,
   type: "checkbox",
   top: 713.5229,
   left: 168.17,
   width: 13.68,
   height: 13.6800
}
,
{
   page: 4,
   name: "CommentsHazardous materials on the Property such as radioactive toxic or biohazardous materials asbestos pesticides herbicides wastewater sludge radon methane mill tailings solvents or petroleum products", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 113.4,
   left: 199.44,
   width: 388.68,
   height: 77.16
}
,
{
   page: 4,
   name: "CommentsUnderground storage tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 192.36,
   left: 199.44,
   width: 388.68,
   height: 44.76
}
,
{
   page: 4,
   name: "CommentsAboveground storage tanks", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 238.92,
   left: 199.44,
   width: 388.68,
   height: 44.52
}
,
{
   page: 4,
   name: "CommentsUnderground transmission lines", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 285.24,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsProperty used as situated on or adjoining a dump landfill or municipal solid waste landfill", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 340.92,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsMonitoring wells or test equipment", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 396.6,
   left: 199.44,
   width: 388.68,
   height: 54.12
}
,
{
   page: 4,
   name: "CommentsSliding settling upheaval movement or instability of earth or expansive soils on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 452.52,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsMine shafts tunnels or abandoned wells on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 508.2,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsWithin a governmentally designated geological hazard or sensitive area", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 563.88,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsWithin a governmentally designated floodplain or wetland area", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 619.56,
   left: 199.44,
   width: 388.68,
   height: 53.88
}
,
{
   page: 4,
   name: "CommentsDead diseased or infested trees or shrubs", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 675.24,
   left: 199.44,
   width: 388.68,
   height: 72.36
}
,
{
   page: 4,
   name: "Check Box1120", 
   isText: false,
   type: "checkbox",
   top: 118.52,
   left: 177.78,
   width: 13.68,
   height: 13.68
}
,
{
   page: 4,
   name: "Check Box1121", 
   isText: false,
   type: "checkbox",
   top: 195.398,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1122", 
   isText: false,
   type: "checkbox",
   top: 242.646,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1123", 
   isText: false,
   type: "checkbox",
   top: 290.694,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1124", 
   isText: false,
   type: "checkbox",
   top: 344.349,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1125", 
   isText: false,
   type: "checkbox",
   top: 400.406,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1126", 
   isText: false,
   type: "checkbox",
   top: 457.263,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1127", 
   isText: false,
   type: "checkbox",
   top: 510.117,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1128", 
   isText: false,
   type: "checkbox",
   top: 566.174,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1129", 
   isText: false,
   type: "checkbox",
   top: 623.031,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 4,
   name: "Check Box1130", 
   isText: false,
   type: "checkbox",
   top: 677.486,
   left: 177.78,
   width: 13.68,
   height: 13.680
}
,
{
   page: 5,
   name: "Environmental assessments studies or reports done involving the physical condition of the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 45.36,
   left: 200.228,
   width: 386.807,
   height: 45.12
}
,
{
   page: 5,
   name: "Used for any mining graveling or other natural resource extraction operations such as oil and gas wells", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 91.92,
   left: 200.228,
   width: 386.807,
   height: 45.12
}
,
{
   page: 5,
   name: "Other environmental problems issues or concerns", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 138.48,
   left: 200.228,
   width: 386.807,
   height: 45.12
}
,
{
   page: 5,
   name: "Odors", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 185.04,
   left: 200.228,
   width: 386.807,
   height: 44.88
}
,
{
   page: 5,
   name: "Odors16", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 231.72,
   left: 37.44,
   width: 132.36,
   height: 35.64
}
,
{
   page: 5,
   name: "F", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 233.329,
   left: 200.259,
   width: 386.900,
   height: 31.627
}
,
{
   page: 5,
   name: "CommentsProperty is part of an owners association", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 350.04,
   left: 204,
   width: 384.12,
   height: 63.00
}
,
{
   page: 5,
   name: "CommentsSpecial assessments or increases in regular assessments approved by owners association but not yet implemented", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 414.84,
   left: 204,
   width: 384.12,
   height: 53.88
}
,
{
   page: 5,
   name: "CommentsProblems or defects in the Common Elements or Limited Common Elements of the Association Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 470.52,
   left: 204,
   width: 384.12,
   height: 53.88
}
,
{
   page: 5,
   name: "CommentsHas the Association made demand or commenced a lawsuit against a builder or contractor alleging defective construction of improvements of the Association Property common area or property owned or controlled by the Association but outside the Sellers Property or unit", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 597.96,
   left: 204,
   width: 384.12,
   height: 107.64
}
,
{
   page: 5,
   name: "Has the Association made demand or commenced a lawsuit against a builder or contractor alleging defective construction of improvements of the Association Property common area or property owned or controlled by the Association but outside the Sellers Property or unit5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 707.4,
   left: 37.44,
   width: 136.68,
   height: 35.64
}
,
{
   page: 5,
   name: "Comments5", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 707.4,
   left: 204,
   width: 384.12,
   height: 35.64
}
,
{
   page: 5,
   name: "Check Box1131", 
   isText: false,
   type: "checkbox",
   top: 49.65,
   left: 176.179,
   width: 13.680,
   height: 13.68
}
,
{
   page: 5,
   name: "Check Box1132", 
   isText: false,
   type: "checkbox",
   top: 95.297,
   left: 176.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1133", 
   isText: false,
   type: "checkbox",
   top: 141.744,
   left: 176.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1134", 
   isText: false,
   type: "checkbox",
   top: 188.191,
   left: 176.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1135", 
   isText: false,
   type: "checkbox",
   top: 234.638,
   left: 176.179,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1136", 
   isText: false,
   type: "checkbox",
   top: 354.759,
   left: 181.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1137", 
   isText: false,
   type: "checkbox",
   top: 418.824,
   left: 181.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1138", 
   isText: false,
   type: "checkbox",
   top: 476.483,
   left: 181.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1139", 
   isText: false,
   type: "checkbox",
   top: 605.413,
   left: 181.983,
   width: 13.680,
   height: 13.680
}
,
{
   page: 5,
   name: "Check Box1140", 
   isText: false,
   type: "checkbox",
   top: 715.1245,
   left: 181.983,
   width: 13.680,
   height: 13.6800
}
,
{
   page: 6,
   name: "Contact InformationOwners Association 1", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 114.36,
   left: 204,
   width: 384.12,
   height: 39.96
}
,
{
   page: 6,
   name: "Contact InformationOwners Association 2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 156.12,
   left: 204,
   width: 384.12,
   height: 40.20
}
,
{
   page: 6,
   name: "Contact InformationOwners Association 3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 197.88,
   left: 203.76,
   width: 384.60,
   height: 26.52
}
,
{
   page: 6,
   name: "Contact InformationOwners Association 4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 225.84,
   left: 203.64,
   width: 384.84,
   height: 23.28
}
,
{
   page: 6,
   name: "CommentsWritten reports of any building site roofing soils water sewer mold or engineering investigations or studies of the Property Provide copies of all such reports in possession of Seller", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 306.84,
   left: 204,
   width: 388.68,
   height: 55.32
}
,
{
   page: 6,
   name: "CommentsAny property insurance claim submitted whether paid or not", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 363.96,
   left: 204,
   width: 388.68,
   height: 50.52
}
,
{
   page: 6,
   name: "CommentsGovernment special improvements approved but not yet installed that may become a lien against the Property Provide copies of all such reports in possession of Seller", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.28,
   left: 204,
   width: 388.68,
   height: 66.12
}
,
{
   page: 6,
   name: "CommentsPending 1 litigation or 2 other dispute resolution proceeding regarding the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 484.2,
   left: 204,
   width: 388.68,
   height: 40.2
}
,
{
   page: 6,
   name: "CommentsSigns Government or private restriction problems", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 526.2,
   left: 204,
   width: 388.68,
   height: 40.2
}
,
{
   page: 6,
   name: "CommentsProperty is subject to Deed Restrictions other recorded document restrictions or Affordable Housing Restrictions", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 568.2,
   left: 204,
   width: 388.68,
   height: 47.64
}
,
{
   page: 6,
   name: "Property is subject to Deed Restrictions other recorded document restrictions or Affordable Housing Restrictions7", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617.52,
   left: 37.2,
   width: 137.16,
   height: 29.28
}
,
{
   page: 6,
   name: "Comments7", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 617.52,
   left: 203.76,
   width: 389.16,
   height: 29.28
}
,
{
   page: 6,
   name: "CommentsGENERAL  Other Information", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 648.48,
   left: 203.4,
   width: 389.88,
   height: 17.04
}
,
{
   page: 6,
   name: "GENERAL  Other Information8", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 667.32,
   left: 37.44,
   width: 136.68,
   height: 39.96
}
,
{
   page: 6,
   name: "Comments8", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 667.32,
   left: 204,
   width: 388.68,
   height: 39.96
}
,
{
   page: 6,
   name: "Check Box1141", 
   isText: false,
   type: "checkbox",
   top: 119.321,
   left: 182.585,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1142", 
   isText: false,
   type: "checkbox",
   top: 160.162,
   left: 182.585,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1143", 
   isText: false,
   type: "checkbox",
   top: 201.004,
   left: 182.585,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1144", 
   isText: false,
   type: "checkbox",
   top: 229.032,
   left: 182.585,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1145", 
   isText: false,
   type: "checkbox",
   top: 309.113,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1146", 
   isText: false,
   type: "checkbox",
   top: 368.373,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1147", 
   isText: false,
   type: "checkbox",
   top: 421.227,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1148", 
   isText: false,
   type: "checkbox",
   top: 488.495,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1149", 
   isText: false,
   type: "checkbox",
   top: 530.938,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1150", 
   isText: false,
   type: "checkbox",
   top: 572.58,
   left: 181.386,
   width: 13.680,
   height: 13.68
}
,
{
   page: 6,
   name: "Check Box1151", 
   isText: false,
   type: "checkbox",
   top: 620.629,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1152", 
   isText: false,
   type: "checkbox",
   top: 650.259,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 6,
   name: "Check Box1153", 
   isText: false,
   type: "checkbox",
   top: 670.279,
   left: 181.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "CommentsCrops being grown on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 103.08,
   left: 208.56,
   width: 384.12,
   height: 71.16
}
,
{
   page: 7,
   name: "CommentsSeller owns all crops", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 176.04,
   left: 208.56,
   width: 384.12,
   height: 60.84
}
,
{
   page: 7,
   name: "CommentsLivestock on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 238.68,
   left: 208.56,
   width: 384.12,
   height: 71.16
}
,
{
   page: 7,
   name: "If yes provide name and contact", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 346.44,
   left: 79.08,
   width: 98.378,
   height: 9.524
}
,
{
   page: 7,
   name: "CommentsAny land leased from others State BLM Federal Private Other If yes provide name and contact information of lessor in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 311.64,
   left: 208.56,
   width: 384.12,
   height: 68.28
}
,
{
   page: 7,
   name: "If yes provide name and contact_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.52,
   left: 83.64,
   width: 91.44,
   height: 9.524
}
,
{
   page: 7,
   name: "CommentsAny land leased to others State BLM Federal Private Other If yes provide name and contact information of lessee in Comments", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 381.72,
   left: 208.56,
   width: 384.12,
   height: 68.28
}
,
{
   page: 7,
   name: "Any land leased to others State BLM Federal Private Other If yes provide name and contact information of lessee in Comments6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 451.68,
   left: 37.2,
   width: 141.72,
   height: 29.28
}
,
{
   page: 7,
   name: "Comments6_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 451.025,
   left: 208.32,
   width: 384.60,
   height: 29.280
}
,
{
   page: 7,
   name: "CommentsHave any noxious weeds on the Property been identified", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 540.36,
   left: 208.56,
   width: 384.12,
   height: 71.16
}
,
{
   page: 7,
   name: "CommentsHave there been any weed enforcement actions on the Property", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 613.32,
   left: 208.56,
   width: 384.12,
   height: 60.84
}
,
{
   page: 7,
   name: "CommentsHas a noxious weed management plan for the Property been entered into", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 675.96,
   left: 208.56,
   width: 384.12,
   height: 60.84
}
,
{
   page: 7,
   name: "Check Box1154", 
   isText: false,
   type: "checkbox",
   top: 110.512,
   left: 185.788,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1155", 
   isText: false,
   type: "checkbox",
   top: 178.581,
   left: 185.788,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1156", 
   isText: false,
   type: "checkbox",
   top: 241.845,
   left: 185.788,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1157", 
   isText: false,
   type: "checkbox",
   top: 315.52,
   left: 185.788,
   width: 13.680,
   height: 13.68
}
,
{
   page: 7,
   name: "Check Box1158", 
   isText: false,
   type: "checkbox",
   top: 385.19,
   left: 185.788,
   width: 13.680,
   height: 13.68
}
,
{
   page: 7,
   name: "Check Box1159", 
   isText: false,
   type: "checkbox",
   top: 455.662,
   left: 185.788,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1160", 
   isText: false,
   type: "checkbox",
   top: 544.552,
   left: 184.187,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1161", 
   isText: false,
   type: "checkbox",
   top: 617.426,
   left: 184.187,
   width: 13.680,
   height: 13.680
}
,
{
   page: 7,
   name: "Check Box1162", 
   isText: false,
   type: "checkbox",
   top: 679.889,
   left: 184.187,
   width: 13.680,
   height: 13.6798
}
,
{
   page: 7,
   name: "Check Box1170", 
   isText: false,
   type: "checkbox",
   top: 323.528,
   left: 40.0406,
   width: 12.8072,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1171", 
   isText: false,
   type: "checkbox",
   top: 325.129,
   left: 89.2885,
   width: 12.8075,
   height: 12.808
}
,
{
   page: 7,
   name: "Check Box1172", 
   isText: false,
   type: "checkbox",
   top: 335.54,
   left: 40.0406,
   width: 12.8072,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1173", 
   isText: false,
   type: "checkbox",
   top: 333.938,
   left: 89.2885,
   width: 12.8075,
   height: 12.808
}
,
{
   page: 7,
   name: "Check Box1174", 
   isText: false,
   type: "checkbox",
   top: 347.552,
   left: 40.0406,
   width: 12.8072,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1175", 
   isText: false,
   type: "checkbox",
   top: 393.999,
   left: 40.0406,
   width: 12.8072,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1176", 
   isText: false,
   type: "checkbox",
   top: 393.999,
   left: 88.4876,
   width: 12.8074,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1177", 
   isText: false,
   type: "checkbox",
   top: 404.41,
   left: 40.6951,
   width: 12.1527,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1178", 
   isText: false,
   type: "checkbox",
   top: 404.41,
   left: 88.4876,
   width: 12.8074,
   height: 12.807
}
,
{
   page: 7,
   name: "Check Box1179", 
   isText: false,
   type: "checkbox",
   top: 416.824,
   left: 40.0406,
   width: 12.8072,
   height: 12.808
}
,
{
   page: 8,
   name: "Have noxious weed management actions been implemented", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 45.6,
   left: 209.593,
   width: 383.119,
   height: 60.96
}
,
{
   page: 8,
   name: "5_7", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 172.415,
   left: 209.593,
   width: 383.119,
   height: 36.025
}
,
{
   page: 8,
   name: "Have herbicides been applied", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 108.24,
   left: 209.593,
   width: 383.119,
   height: 60.96
}
,
{
   page: 8,
   name: "Have herbicides been applied6", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 171,
   left: 37.44,
   width: 136.68,
   height: 40.2
}
,
{
   page: 8,
   name: "CommentsAny part of the Property enrolled in any governmental programs such as Conservation Reserve Program CRP Wetlands Reserve Program WRP etc", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 334.92,
   left: 186,
   width: 402.12,
   height: 66.36
}
,
{
   page: 8,
   name: "CommentsConservation easement", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 403.08,
   left: 186,
   width: 402.12,
   height: 50.52
}
,
{
   page: 8,
   name: "Conservation easement3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 455.4,
   left: 37.44,
   width: 118.68,
   height: 50.28
}
,
{
   page: 8,
   name: "Comments3", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 455.4,
   left: 186,
   width: 402.12,
   height: 50.28
}
,
{
   page: 8,
   name: "Conservation easement4", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 507.48,
   left: 37.44,
   width: 118.68,
   height: 40.20
}
,
{
   page: 8,
   name: "Comments4_2", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 507.48,
   left: 186,
   width: 402.12,
   height: 40.20
}
,
{
   page: 8,
   name: "Seller 1 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 6,
   top: 608.96,
   left: 54.2276,
   width: 372.7324,
   height: 20.278
}
,
{
   page: 8,
   name: "Seller 2 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 6,
   top: 649.12,
   left: 54.2276,
   width: 372.7324,
   height: 20.278
}
,
{
   page: 8,
   name: "Seller 3 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 6,
   top: 690.64,
   left: 54.2276,
   width: 372.7324,
   height: 20.2776
}
,
{
   page: 8,
   name: "Seller 4 Name", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 6,
   top: 731.92,
   left: 54.2276,
   width: 372.7324,
   height: 20.2776
}
,
{
   page: 8,
   name: "Check Box1163", 
   isText: false,
   type: "checkbox",
   top: 48.849,
   left: 183.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1164", 
   isText: false,
   type: "checkbox",
   top: 112.114,
   left: 183.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1165", 
   isText: false,
   type: "checkbox",
   top: 172.174,
   left: 183.386,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1166", 
   isText: false,
   type: "checkbox",
   top: 338.743,
   left: 163.366,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1167", 
   isText: false,
   type: "checkbox",
   top: 406.011,
   left: 163.366,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1168", 
   isText: false,
   type: "checkbox",
   top: 458.865,
   left: 163.366,
   width: 13.680,
   height: 13.680
}
,
{
   page: 8,
   name: "Check Box1169", 
   isText: false,
   type: "checkbox",
   top: 510.918,
   left: 163.366,
   width: 13.680,
   height: 13.680
}
] }
