export function coListingAddendumBuyerDC120() {
return [   //2024 Release 2025-06-02 16:52:04
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 68.673,
 left: 53,
 width: 180,
 height: 27,
 }, 
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 51,
   left: 257.28,
   width: 273.96,
   height: 10.2
}
,
{
   page: 0,
   name: "without right of transfer All rights reserved", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 744.36,
   left: 99.96,
   width: 246.48,
   height: 10.20
}
,
{
   page: 0,
   name: "Text65", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 135.292,
   left: 213.383,
   width: 325.419,
   height: 14.145
}
,
{
   page: 0,
   name: "Text66", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 157.546,
   left: 213.383,
   width: 325.419,
   height: 14.146
}
,
{
   page: 0,
   name: "Text67", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 180.455,
   left: 213.383,
   width: 325.419,
   height: 14.146
}
,
{
   page: 0,
   name: "Text68", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 201.401,
   left: 213.383,
   width: 325.419,
   height: 14.145
}
,
{
   page: 0,
   name: "Text69", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 223.001,
   left: 213.383,
   width: 325.419,
   height: 14.145
}
,
{
   page: 0,
   name: "Text70", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 242.328,
   left: 213.383,
   width: 325.419,
   height: 14.146
}
,
{
   page: 0,
   name: "Check Box73", 
   isText: false,
   type: "checkbox",
   top: 530.221,
   left: 463.493,
   width: 12.149,
   height: 12.148
}
,
{
   page: 0,
   name: "Check Box74", 
   isText: false,
   type: "checkbox",
   top: 531.148,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box75", 
   isText: false,
   type: "checkbox",
   top: 552.784,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box76", 
   isText: false,
   type: "checkbox",
   top: 552.13,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box77", 
   isText: false,
   type: "checkbox",
   top: 570.802,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box78", 
   isText: false,
   type: "checkbox",
   top: 571.766,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box79", 
   isText: false,
   type: "checkbox",
   top: 586.821,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box80", 
   isText: false,
   type: "checkbox",
   top: 584.857,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box81", 
   isText: false,
   type: "checkbox",
   top: 599.603,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box82", 
   isText: false,
   type: "checkbox",
   top: 598.603,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box83", 
   isText: false,
   type: "checkbox",
   top: 614.657,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box84", 
   isText: false,
   type: "checkbox",
   top: 613.657,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box85", 
   isText: false,
   type: "checkbox",
   top: 635.912,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box86", 
   isText: false,
   type: "checkbox",
   top: 635.566,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box87", 
   isText: false,
   type: "checkbox",
   top: 654.585,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box88", 
   isText: false,
   type: "checkbox",
   top: 654.239,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box89", 
   isText: false,
   type: "checkbox",
   top: 669.948,
   left: 463.493,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box90", 
   isText: false,
   type: "checkbox",
   top: 668.948,
   left: 516.711,
   width: 12.149,
   height: 12.149
}
,
{
   page: 0,
   name: "Check Box91", 
   isText: false,
   type: "checkbox",
   top: 683.039,
   left: 463.493,
   width: 12.149,
   height: 12.1492
}
,
{
   page: 0,
   name: "Check Box92", 
   isText: false,
   type: "checkbox",
   top: 682.458,
   left: 516.711,
   width: 12.149,
   height: 12.1484
}
,
{
   page: 1,
   name: "undefined_22", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 48.72,
   left: 257.28,
   width: 273.96,
   height: 12.48
}
,
{
   page: 1,
   name: "Other", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 106.24,
   left: 131.444,
   width: 309.687,
   height: 10.876
}
,
{
   page: 1,
   name: "Other_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.16,
   left: 131.444,
   width: 309.687,
   height: 10.876
}
,
{
   page: 1,
   name: "Other_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.08,
   left: 131.444,
   width: 309.687,
   height: 10.876
}
,
{
   page: 1,
   name: "signature Broker 1", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 511.68,
   left: 72,
   width: 216.12,
   height: 12.48
}
,
{
   page: 1,
   name: "signature Broker 2", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 511.68,
   left: 324,
   width: 216.12,
   height: 12.48
}
,
{
   page: 1,
   name: "without right of transfer All rights reserved_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 744.36,
   left: 99.96,
   width: 246.48,
   height: 10.20
}
,
{
   page: 1,
   name: "Text71", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 174.11,
   left: 89.6731,
   width: 450.4379,
   height: 39.091
}
,
{
   page: 1,
   name: "Text72", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 363.274,
   left: 93.6004,
   width: 428.8376,
   height: 81.746
}
,
{
   page: 1,
   name: "Check Box93", 
   isText: false,
   type: "checkbox",
   top: 74.691,
   left: 462.147,
   width: 13.886,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box94", 
   isText: false,
   type: "checkbox",
   top: 74.964,
   left: 516.439,
   width: 13.885,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box95", 
   isText: false,
   type: "checkbox",
   top: 91.019,
   left: 462.147,
   width: 13.886,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box96", 
   isText: false,
   type: "checkbox",
   top: 90.019,
   left: 516.439,
   width: 13.885,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box97", 
   isText: false,
   type: "checkbox",
   top: 104.764,
   left: 462.147,
   width: 13.886,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box98", 
   isText: false,
   type: "checkbox",
   top: 104.419,
   left: 516.439,
   width: 13.885,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box99", 
   isText: false,
   type: "checkbox",
   top: 118.51,
   left: 462.147,
   width: 13.886,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box100", 
   isText: false,
   type: "checkbox",
   top: 118.819,
   left: 516.439,
   width: 13.885,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box101", 
   isText: false,
   type: "checkbox",
   top: 132.255,
   left: 462.147,
   width: 13.886,
   height: 13.885
}
,
{
   page: 1,
   name: "Check Box102", 
   isText: false,
   type: "checkbox",
   top: 131.91,
   left: 516.439,
   width: 13.885,
   height: 13.885
}
] }
