export function residentialPurchaseAgreement() {
return [   //2024 Release 2025-08-24 18:30:08
 {
        page: 0,
        name: "<PERSON><PERSON>",
        isText: false,
        fontSize: 10,
        type: "logo",
        top: 757,
        left: 52.9457,
        width: 133.33,
      height: 20,
      }
      ,
{
   page: 0,
   name: "Date Prepared", 
   fontSize: 9,
   type: "date",
   top: 78.96,
   left: 99.24,
   width: 100.68,
   height: 10.20
}
,
{
   page: 0,
   name: "THIS IS AN OFFER FROM", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 100.92,
   left: 183.24,
   width: 350.64,
   height: 10.20
}
,
{
   page: 0,
   name: "THE PROPERTY to be acquired is", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 111.96,
   left: 214.08,
   width: 325.20,
   height: 10.20
}
,
{
   page: 0,
   name: "in", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 123,
   left: 81.72,
   width: 140.52,
   height: 10.2
}
,
{
   page: 0,
   name: "City", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 123,
   left: 251.52,
   width: 130.56,
   height: 10.2
}
,
{
   page: 0,
   name: "County California", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 123,
   left: 467.52,
   width: 60.72,
   height: 10.2
}
,
{
   page: 0,
   name: "Assessors Parcel Nos", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 134.04,
   left: 172.44,
   width: 350.64,
   height: 10.20
}
,
{
   page: 0,
   name: "License Number", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 225.96,
   left: 499.8,
   width: 76.56,
   height: 10.20
}
,
{
   page: 0,
   name: "License Number_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 249,
   left: 499.8,
   width: 76.56,
   height: 10.2
}
,
{
   page: 0,
   name: "License Number_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 271.92,
   left: 499.8,
   width: 76.56,
   height: 10.20
}
,
{
   page: 0,
   name: "License Number_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 294.96,
   left: 499.8,
   width: 76.56,
   height: 10.20
}
,
{
   page: 0,
   name: "More than one Brokerage represents", 
   isText: false,
   type: "checkbox",
   top: 316.2,
   left: 71.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 0,
   name: "Seller", 
   isText: false,
   type: "checkbox",
   top: 316.2,
   left: 231.96,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Buyer See Additional Broker Acknowledgement CAR Form ABA", 
   isText: false,
   type: "checkbox",
   top: 316.2,
   left: 270.48,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Possible", 
   isText: false,
   type: "checkbox",
   top: 326.76,
   left: 524.28,
   width: 8.40,
   height: 10.32
}
,
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 399,
   left: 278.64,
   width: 156.36,
   height: 10.2
}
,
{
   page: 0,
   name: "All Cash", 
   isText: false,
   type: "checkbox",
   top: 400.44,
   left: 455.28,
   width: 7.44,
   height: 9.24
}
,
{
   page: 0,
   name: "date", 
   fontSize: 9,
   type: "date",
   top: 426,
   left: 306.72,
   width: 54.00,
   height: 8.76
}
,
{
   page: 0,
   name: "at 5PM or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 460.92,
   left: 308.76,
   width: 22.92,
   height: 8.28
}
,
{
   page: 0,
   name: "undefined_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 475.32,
   left: 278.64,
   width: 54.12,
   height: 10.20
}
,
{
   page: 0,
   name: "undefined_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 475.32,
   left: 337.2,
   width: 26.64,
   height: 10.20
}
,
{
   page: 0,
   name: "within 3 or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 475.32,
   left: 496.32,
   width: 22.56,
   height: 10.20
}
,
{
   page: 0,
   name: "after Acceptance by wire transfer", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 494.52,
   left: 478.68,
   width: 94.08,
   height: 8.76
}
,
{
   page: 0,
   name: "OR", 
   isText: false,
   type: "checkbox",
   top: 494.4,
   left: 469.44,
   width: 7.56,
   height: 9.24
}
,
{
   page: 0,
   name: "undefined_5", 
   isText: false,
   type: "checkbox",
   top: 507.84,
   left: 127.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 0,
   name: "undefined_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 520.8,
   left: 337.2,
   width: 26.64,
   height: 10.2
}
,
{
   page: 0,
   name: "not to exceed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 540,
   left: 334.08,
   width: 26.88,
   height: 8.76
}
,
{
   page: 0,
   name: "Buyer to pay up to", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 549.36,
   left: 351,
   width: 22.8,
   height: 8.28
}
,
{
   page: 0,
   name: "17 or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 573.6,
   left: 295.08,
   width: 22.44,
   height: 10.2
}
,
{
   page: 0,
   name: "undefined_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 573.6,
   left: 463.92,
   width: 111.84,
   height: 10.2
}
,
{
   page: 0,
   name: "undefined_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 598.44,
   left: 278.64,
   width: 54.12,
   height: 10.20
}
,
{
   page: 0,
   name: "undefined_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 598.44,
   left: 337.2,
   width: 26.64,
   height: 10.20
}
,
{
   page: 0,
   name: "Initial adjustable rate", 
   isText: false,
   type: "checkbox",
   top: 610.92,
   left: 319.08,
   width: 7.56,
   height: 9.24
}
,
{
   page: 0,
   name: "not to exceed_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 619.92,
   left: 334.08,
   width: 26.88,
   height: 8.64
}
,
{
   page: 0,
   name: "Buyer to pay up to_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 629.596,
   left: 351.0,
   width: 22.8,
   height: 8.400
}
,
{
   page: 0,
   name: "undefined_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 625.44,
   left: 463.92,
   width: 111.84,
   height: 10.20
}
,
{
   page: 0,
   name: "undefined_11", 
   isText: false,
   type: "checkbox",
   top: 650.4,
   left: 353.76,
   width: 7.56,
   height: 9.24
}
,
{
   page: 0,
   name: "undefined_12", 
   isText: false,
   type: "checkbox",
   top: 650.4,
   left: 405.84,
   width: 7.44,
   height: 9.24
}
,
{
   page: 0,
   name: "undefined_13", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 663.36,
   left: 278.64,
   width: 147.48,
   height: 10.20
}
,
{
   page: 0,
   name: "undefined_14", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 677.76,
   left: 278.64,
   width: 147.48,
   height: 10.20
}
,
{
   page: 0,
   name: "Text3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 223.855,
   left: 178.037,
   width: 250.146,
   height: 11.528
}
,
{
   page: 0,
   name: "Text4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 248.728,
   left: 132.219,
   width: 295.964,
   height: 10.219
}
,
{
   page: 0,
   name: "Text5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 271.638,
   left: 177.383,
   width: 250.146,
   height: 10.218
}
,
{
   page: 0,
   name: "Text6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 294.892,
   left: 135.492,
   width: 291.382,
   height: 10.000
}
,
{
   page: 0,
   name: "Check Box7", 
   isText: false,
   type: "checkbox",
   top: 236.635,
   left: 189.032,
   width: 8.280,
   height: 10.441
}
,
{
   page: 0,
   name: "Check Box8", 
   isText: false,
   type: "checkbox",
   top: 236.683,
   left: 258.152,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Check Box9", 
   isText: false,
   type: "checkbox",
   top: 281.715,
   left: 188.985,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Check Box10", 
   isText: false,
   type: "checkbox",
   top: 281.715,
   left: 258.105,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Check Box11", 
   isText: false,
   type: "checkbox",
   top: 259.636,
   left: 369.726,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Check Box12", 
   isText: false,
   type: "checkbox",
   top: 305.89,
   left: 367.417,
   width: 8.280,
   height: 10.44
}
,
{
   page: 0,
   name: "Check Box13", 
   isText: false,
   type: "checkbox",
   top: 417.726,
   left: 270.545,
   width: 8.836,
   height: 9.273
}
,
{
   page: 0,
   name: "Check Box14", 
   isText: false,
   type: "checkbox",
   top: 426.017,
   left: 296.29,
   width: 8.837,
   height: 9.273
}
,
{
   page: 0,
   name: "Check Box15", 
   isText: false,
   type: "checkbox",
   top: 461.799,
   left: 334.254,
   width: 8.400,
   height: 7.527
}
,
{
   page: 0,
   name: "Check Box16", 
   isText: false,
   type: "checkbox",
   top: 461.799,
   left: 361.308,
   width: 8.400,
   height: 7.527
}
,
{
   page: 0,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 532.817,
   left: 455.381,
   width: 7.091,
   height: 8.836
}
,
{
   page: 0,
   name: "Check Box18", 
   isText: false,
   type: "checkbox",
   top: 542.526,
   left: 455.381,
   width: 7.091,
   height: 8.836
}
,
{
   page: 0,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 552.362,
   left: 455.381,
   width: 7.091,
   height: 8.837
}
,
{
   page: 0,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 562.071,
   left: 455.381,
   width: 7.091,
   height: 8.837
}
,
{
   page: 0,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 607.435,
   left: 455.381,
   width: 7.091,
   height: 8.836
}
,
{
   page: 0,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 616.89,
   left: 455.381,
   width: 7.091,
   height: 8.836
}
,
{
   page: 0,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 531.49,
   left: 319.236,
   width: 7.527,
   height: 9.273
}
,
{
   page: 0,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 259.636,
   left: 133.654,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Check Box78", 
   isText: false,
   type: "checkbox",
   top: 305.454,
   left: 132.909,
   width: 8.280,
   height: 10.440
}
,
{
   page: 0,
   name: "Text79", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 521.89,
   left: 277.836,
   width: 56.182,
   height: 10.218
}
,
{
   page: 0,
   name: "Text80", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 416.29,
   left: 282.763,
   width: 23.018,
   height: 10.218
}
,
{
   page: 0,
   name: "Text90", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 451.199,
   left: 281.018,
   width: 134.727,
   height: 9.345
}
,
{
   page: 1,
   name: "Date", 
   fontSize: 9,
   type: "date",
   top: 15.84,
   left: 466.8,
   width: 109.56,
   height: 10.32
}
,
{
   page: 1,
   name: "Seller Credit to Buyer", 
   isText: false,
   type: "checkbox",
   top: 46.44,
   left: 131.28,
   width: 7.44,
   height: 9.12
}
,
{
   page: 1,
   name: "undefined_17", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 44.76,
   left: 278.4,
   width: 62.88,
   height: 10.32
}
,
{
   page: 1,
   name: "ADDITIONAL SELLER CREDIT TERMS does not include buyer broker compensation 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 59.16,
   left: 459.96,
   width: 111.96,
   height: 8.16
}
,
{
   page: 1,
   name: "ADDITIONAL SELLER CREDIT TERMS does not include buyer broker compensation 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 70.08,
   left: 131.16,
   width: 440.76,
   height: 10.32
}
,
{
   page: 1,
   name: "Seller Payment to Compensate Buyers Broker", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 100.92,
   left: 126.84,
   width: 139.08,
   height: 12.72
}
,
{
   page: 1,
   name: "undefined_18", 
   isText: false,
   type: "checkbox",
   top: 84.6,
   left: 131.28,
   width: 7.44,
   height: 9.12
}
,
{
   page: 1,
   name: "Seller agrees to pay Buyers Broker out of transaction proceeds", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 82.92,
   left: 511.92,
   width: 26.88,
   height: 10.32
}
,
{
   page: 1,
   name: "final purchase price AND if applicable", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 93.48,
   left: 416.4,
   width: 54.0,
   height: 8.76
}
,
{
   page: 1,
   name: "undefined_19", 
   isText: false,
   type: "checkbox",
   top: 102.36,
   left: 272.4,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "undefined_20", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 102.36,
   left: 288.6,
   width: 53.76,
   height: 8.76
}
,
{
   page: 1,
   name: "3 or", 
   isText: false,
   type: "checkbox",
   top: 119.76,
   left: 355.2,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "Days", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 118.2,
   left: 383.04,
   width: 22.56,
   height: 10.32
}
,
{
   page: 1,
   name: "3 or_2", 
   isText: false,
   type: "checkbox",
   top: 141.36,
   left: 355.2,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "Days_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 139.8,
   left: 383.04,
   width: 22.56,
   height: 10.32
}
,
{
   page: 1,
   name: "3 or_3", 
   isText: false,
   type: "checkbox",
   top: 162.96,
   left: 355.2,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "Days_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 161.4,
   left: 383.04,
   width: 22.56,
   height: 10.32
}
,
{
   page: 1,
   name: "Preapproval", 
   isText: false,
   type: "checkbox",
   top: 162.96,
   left: 521.88,
   width: 7.44,
   height: 9.24
}
,
{
   page: 1,
   name: "5 or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 196.68,
   left: 290.4,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 211.08,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "undefined_21", 
   isText: false,
   type: "checkbox",
   top: 241.32,
   left: 455.52,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "17 or 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 239.88,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 252.84,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "No appraisal contingency", 
   isText: false,
   type: "checkbox",
   top: 254.28,
   left: 455.52,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "undefined_22", 
   isText: false,
   type: "checkbox",
   top: 281.04,
   left: 131.28,
   width: 7.44,
   height: 9.24
}
,
{
   page: 1,
   name: "minimum of purchase price or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 281.04,
   left: 147.12,
   width: 89.64,
   height: 8.76
}
,
{
   page: 1,
   name: "17 or_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 296.52,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 309.48,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 345.48,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 358.32,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 380.4,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 402.12,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "17 or_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 423.72,
   left: 294.84,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "CRB attached", 
   isText: false,
   type: "checkbox",
   top: 423,
   left: 482.04,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "CAR Form COP attached", 
   isText: false,
   type: "checkbox",
   top: 446.16,
   left: 467.64,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "6 PM or", 
   isText: false,
   type: "checkbox",
   top: 474.96,
   left: 384.24,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "AM", 
   isText: false,
   type: "checkbox",
   top: 483.84,
   left: 296.28,
   width: 7.56,
   height: 9.24
}
,
{
   page: 1,
   name: "PM on date specified as", 
   isText: false,
   type: "checkbox",
   top: 483.84,
   left: 322.08,
   width: 7.44,
   height: 9.24
}
,
{
   page: 1,
   name: "applicable in 3M2 or attached TOPA", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 483.96,
   left: 271.8,
   width: 22.8,
   height: 8.76
}
,
{
   page: 1,
   name: "days after COE 29 or fewer days", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 514.56,
   left: 281.16,
   width: 22.80,
   height: 8.76
}
,
{
   page: 1,
   name: "days after COE 30 or more days", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 523.92,
   left: 281.16,
   width: 22.80,
   height: 8.28
}
,
{
   page: 1,
   name: "Tenant Occupied Property Addendum", 
   isText: false,
   type: "checkbox",
   top: 537.24,
   left: 271.92,
   width: 7.44,
   height: 9.24
}
,
{
   page: 1,
   name: "7 or 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 572.76,
   left: 290.4,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "7 or 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 587.16,
   left: 290.4,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 608.28,
   left: 290.4,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 629.16,
   left: 290.4,
   width: 22.44,
   height: 10.32
}
,
{
   page: 1,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 160.6,
   left: 455.69,
   width: 7.44,
   height: 9.24
}
,
{
   page: 1,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 170.618,
   left: 455.69,
   width: 7.44,
   height: 9.240
}
,
{
   page: 1,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 514.235,
   left: 271.92,
   width: 7.44,
   height: 9.240
}
,
{
   page: 1,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 523.072,
   left: 271.92,
   width: 7.44,
   height: 9.240
}
,
{
   page: 1,
   name: "Text89", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.273,
   left: 111.272,
   width: 330.655,
   height: 10.654
}
,
{
   page: 2,
   name: "Property Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 14.76,
   left: 110.28,
   width: 330.60,
   height: 10.44
}
,
{
   page: 2,
   name: "Date_2", 
   fontSize: 9,
   type: "date",
   top: 14.76,
   left: 466.8,
   width: 109.56,
   height: 10.44
}
,
{
   page: 2,
   name: "Stoves ovens stoveoven", 
   isText: false,
   type: "checkbox",
   top: 58.32,
   left: 128.28,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "spas", 
   isText: false,
   type: "checkbox",
   top: 58.32,
   left: 546.48,
   width: 7.32,
   height: 9.24
}
,
{
   page: 2,
   name: "Electric car charging systems", 
   isText: false,
   type: "checkbox",
   top: 85.08,
   left: 455.64,
   width: 7.44,
   height: 9.12
}
,
{
   page: 2,
   name: "Potted treesshrubs", 
   isText: false,
   type: "checkbox",
   top: 102.84,
   left: 455.64,
   width: 7.44,
   height: 9.12
}
,
{
   page: 2,
   name: "undefined_27", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 129.6,
   left: 281.04,
   width: 138.24,
   height: 8.64
}
,
{
   page: 2,
   name: "undefined_29", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 128.28,
   left: 464.88,
   width: 111.48,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_30", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 138.96,
   left: 137.52,
   width: 120.36,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_32", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 138.96,
   left: 281.04,
   width: 138.24,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_34", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 139.56,
   left: 464.88,
   width: 111.48,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_35", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 166.32,
   left: 138.36,
   width: 120.36,
   height: 9.00
}
,
{
   page: 2,
   name: "undefined_36", 
   isText: false,
   type: "checkbox",
   top: 166.32,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "undefined_37", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 164.64,
   left: 281.04,
   width: 133.80,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_39", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 164.64,
   left: 464.88,
   width: 111.48,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer", 
   isText: false,
   type: "checkbox",
   top: 219.84,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_2", 
   isText: false,
   type: "checkbox",
   top: 219.84,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both", 
   isText: false,
   type: "checkbox",
   top: 219.84,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_40", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 218.28,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "1_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 229.32,
   left: 271.68,
   width: 173.16,
   height: 8.28
}
,
{
   page: 2,
   name: "2_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 238.2,
   left: 271.68,
   width: 173.16,
   height: 8.28
}
,
{
   page: 2,
   name: "Environmental Other", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 238.56,
   left: 451.2,
   width: 133.08,
   height: 12.60
}
,
{
   page: 2,
   name: "undefined_42", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 228.84,
   left: 486.96,
   width: 89.64,
   height: 8.76
}
,
{
   page: 2,
   name: "Provided by", 
   isText: false,
   type: "checkbox",
   top: 253.8,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "undefined_43", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 252,
   left: 327.72,
   width: 245.40,
   height: 10.44
}
,
{
   page: 2,
   name: "Optional Wildfire Disclosure Report", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 279.24,
   left: 128.16,
   width: 129.72,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_2", 
   isText: false,
   type: "checkbox",
   top: 267.24,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_3", 
   isText: false,
   type: "checkbox",
   top: 267.24,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_2", 
   isText: false,
   type: "checkbox",
   top: 267.24,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_44", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 265.68,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Provided by_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 279.24,
   left: 318.48,
   width: 254.16,
   height: 10.44
}
,
{
   page: 2,
   name: "A", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 293.28,
   left: 141,
   width: 94.08,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_3", 
   isText: false,
   type: "checkbox",
   top: 294.96,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_4", 
   isText: false,
   type: "checkbox",
   top: 294.96,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_3", 
   isText: false,
   type: "checkbox",
   top: 294.96,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "B", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 304.44,
   left: 141,
   width: 94.08,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_4", 
   isText: false,
   type: "checkbox",
   top: 306.12,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_5", 
   isText: false,
   type: "checkbox",
   top: 306.12,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_4", 
   isText: false,
   type: "checkbox",
   top: 306.12,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "1_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 293.28,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "2_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 304.44,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_45", 
   isText: false,
   type: "checkbox",
   top: 320.16,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_46", 
   isText: false,
   type: "checkbox",
   top: 320.16,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_47", 
   isText: false,
   type: "checkbox",
   top: 320.16,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_48", 
   isText: false,
   type: "checkbox",
   top: 339.84,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_49", 
   isText: false,
   type: "checkbox",
   top: 339.84,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_50", 
   isText: false,
   type: "checkbox",
   top: 339.84,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "1_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 318.6,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "2_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 338.28,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "undefined_51", 
   isText: false,
   type: "checkbox",
   top: 361.08,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_52", 
   isText: false,
   type: "checkbox",
   top: 361.08,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_53", 
   isText: false,
   type: "checkbox",
   top: 361.08,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "1_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 359.4,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "2_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 380.16,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_5", 
   isText: false,
   type: "checkbox",
   top: 381.84,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_6", 
   isText: false,
   type: "checkbox",
   top: 381.84,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_5", 
   isText: false,
   type: "checkbox",
   top: 381.84,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Each to pay their own fees", 
   isText: false,
   type: "checkbox",
   top: 381.84,
   left: 450.96,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Escrow Holder", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 392.4,
   left: 327.96,
   width: 249.84,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_6", 
   isText: false,
   type: "checkbox",
   top: 408.84,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_7", 
   isText: false,
   type: "checkbox",
   top: 408.84,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_6", 
   isText: false,
   type: "checkbox",
   top: 408.84,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_54", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 407.16,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Title Co If different from Escrow Holder", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 418.32,
   left: 421.08,
   width: 156.36,
   height: 10.44
}
,
{
   page: 2,
   name: "County transfer tax fees", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 464.4,
   left: 214.92,
   width: 50.88,
   height: 13.2
}
,
{
   page: 2,
   name: "Buyer_7", 
   isText: false,
   type: "checkbox",
   top: 465.6,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_8", 
   isText: false,
   type: "checkbox",
   top: 465.6,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_7", 
   isText: false,
   type: "checkbox",
   top: 465.6,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "City transfer tax fees", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 478.8,
   left: 203.28,
   width: 62.52,
   height: 13.2
}
,
{
   page: 2,
   name: "Buyer_8", 
   isText: false,
   type: "checkbox",
   top: 480,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_9", 
   isText: false,
   type: "checkbox",
   top: 480,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_8", 
   isText: false,
   type: "checkbox",
   top: 480,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "1_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 464.04,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "2_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 478.44,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Seller_10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 493.2,
   left: 291.84,
   width: 157.80,
   height: 11.88
}
,
{
   page: 2,
   name: "HOA certification fee", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 506.16,
   left: 201,
   width: 64.92,
   height: 11.16
}
,
{
   page: 2,
   name: "Buyer_9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 506.16,
   left: 292.08,
   width: 157.68,
   height: 11.16
}
,
{
   page: 2,
   name: "Buyer_10", 
   isText: false,
   type: "checkbox",
   top: 519.84,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Seller_11", 
   isText: false,
   type: "checkbox",
   top: 519.84,
   left: 306.48,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Both_9", 
   isText: false,
   type: "checkbox",
   top: 519.84,
   left: 340.68,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "undefined_55", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 518.16,
   left: 368.64,
   width: 76.20,
   height: 10.32
}
,
{
   page: 2,
   name: "Seller or if checked", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 582.96,
   left: 123.96,
   width: 141.72,
   height: 16.80
}
,
{
   page: 2,
   name: "Buyer_11", 
   isText: false,
   type: "checkbox",
   top: 575.64,
   left: 346.44,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_10", 
   isText: false,
   type: "checkbox",
   top: 575.64,
   left: 378.84,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "undefined_56", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 585.12,
   left: 271.68,
   width: 169.80,
   height: 10.44
}
,
{
   page: 2,
   name: "fees or costs", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 600.6,
   left: 128.16,
   width: 85.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer_12", 
   isText: false,
   type: "checkbox",
   top: 602.28,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Seller_12", 
   isText: false,
   type: "checkbox",
   top: 602.28,
   left: 306.48,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Both_11", 
   isText: false,
   type: "checkbox",
   top: 602.28,
   left: 340.68,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "fees or costs_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 613.56,
   left: 128.16,
   width: 85.20,
   height: 10.32
}
,
{
   page: 2,
   name: "Buyer_13", 
   isText: false,
   type: "checkbox",
   top: 615.24,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Seller_13", 
   isText: false,
   type: "checkbox",
   top: 615.24,
   left: 306.48,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "Both_12", 
   isText: false,
   type: "checkbox",
   top: 615.24,
   left: 340.68,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "1_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 600.6,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "2_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 613.56,
   left: 368.64,
   width: 76.20,
   height: 10.32
}
,
{
   page: 2,
   name: "Buyer_14", 
   isText: false,
   type: "checkbox",
   top: 628.32,
   left: 271.8,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Seller_14", 
   isText: false,
   type: "checkbox",
   top: 628.32,
   left: 306.48,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "Both_13", 
   isText: false,
   type: "checkbox",
   top: 628.32,
   left: 340.68,
   width: 7.56,
   height: 9.24
}
,
{
   page: 2,
   name: "If Seller or Both checked Sellers", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 626.64,
   left: 368.64,
   width: 76.20,
   height: 10.44
}
,
{
   page: 2,
   name: "Coverage includes but is not limited to 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 647.4,
   left: 128.16,
   width: 129.72,
   height: 6.36
}
,
{
   page: 2,
   name: "Coverage includes but is not limited to 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 654.36,
   left: 128.16,
   width: 129.72,
   height: 10.44
}
,
{
   page: 2,
   name: "cost not to exceed", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 637.2,
   left: 525.6,
   width: 44.76,
   height: 8.76
}
,
{
   page: 2,
   name: "Issued by", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 646.68,
   left: 310.32,
   width: 258.72,
   height: 10.44
}
,
{
   page: 2,
   name: "Buyer waives home warranty plan", 
   isText: false,
   type: "checkbox",
   top: 660.6,
   left: 271.8,
   width: 7.56,
   height: 9.12
}
,
{
   page: 2,
   name: "undefined_57", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 666.84,
   left: 128.16,
   width: 129.72,
   height: 10.44
}
,
{
   page: 2,
   name: "OTHER TERMS 1", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 681.12,
   left: 135.6,
   width: 441.24,
   height: 10.44
}
,
{
   page: 2,
   name: "OTHER TERMS 2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 692.16,
   left: 72.48,
   width: 504.36,
   height: 10.20
}
,
{
   page: 2,
   name: "OTHER TERMS 3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 702.96,
   left: 72.48,
   width: 504.36,
   height: 9.36
}
,
{
   page: 2,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 74.636,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box30", 
   isText: false,
   type: "checkbox",
   top: 83.473,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box31", 
   isText: false,
   type: "checkbox",
   top: 92.818,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box32", 
   isText: false,
   type: "checkbox",
   top: 102.363,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box33", 
   isText: false,
   type: "checkbox",
   top: 111.218,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box34", 
   isText: false,
   type: "checkbox",
   top: 120.254,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box35", 
   isText: false,
   type: "checkbox",
   top: 139.891,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box36", 
   isText: false,
   type: "checkbox",
   top: 166.509,
   left: 128.28,
   width: 7.56,
   height: 9.240
}
,
{
   page: 2,
   name: "Check Box37", 
   isText: false,
   type: "checkbox",
   top: 57.054,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box38", 
   isText: false,
   type: "checkbox",
   top: 68.073,
   left: 271.636,
   width: 8.400,
   height: 10.581
}
,
{
   page: 2,
   name: "Check Box39", 
   isText: false,
   type: "checkbox",
   top: 78.6,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box40", 
   isText: false,
   type: "checkbox",
   top: 101.672,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box41", 
   isText: false,
   type: "checkbox",
   top: 113.2,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box42", 
   isText: false,
   type: "checkbox",
   top: 128.727,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box43", 
   isText: false,
   type: "checkbox",
   top: 139.818,
   left: 271.636,
   width: 8.400,
   height: 10.582
}
,
{
   page: 2,
   name: "Check Box44", 
   isText: false,
   type: "checkbox",
   top: 57.982,
   left: 455.64,
   width: 7.44,
   height: 9.120
}
,
{
   page: 2,
   name: "Check Box45", 
   isText: false,
   type: "checkbox",
   top: 67.763,
   left: 455.64,
   width: 7.44,
   height: 9.120
}
,
{
   page: 2,
   name: "Check Box46", 
   isText: false,
   type: "checkbox",
   top: 130.491,
   left: 455.64,
   width: 7.44,
   height: 9.120
}
,
{
   page: 2,
   name: "Check Box47", 
   isText: false,
   type: "checkbox",
   top: 141.254,
   left: 455.64,
   width: 7.44,
   height: 9.120
}
,
{
   page: 2,
   name: "Check Box48", 
   isText: false,
   type: "checkbox",
   top: 165.818,
   left: 455.64,
   width: 7.44,
   height: 9.120
}
,
{
   page: 2,
   name: "Check Box49", 
   isText: false,
   type: "checkbox",
   top: 219.436,
   left: 455.199,
   width: 7.964,
   height: 9.709
}
,
{
   page: 2,
   name: "Check Box95", 
   isText: false,
   type: "checkbox",
   top: 228.848,
   left: 455.199,
   width: 7.964,
   height: 9.709
}
,
{
   page: 3,
   name: "Property Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 14.88,
   left: 110.28,
   width: 330.60,
   height: 10.32
}
,
{
   page: 3,
   name: "Date_3", 
   fontSize: 9,
   type: "date",
   top: 14.88,
   left: 466.8,
   width: 109.56,
   height: 10.32
}
,
{
   page: 3,
   name: "Other_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 98.76,
   left: 348.72,
   width: 227.64,
   height: 10.32
}
,
{
   page: 3,
   name: "CAR Form ADM", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 119.88,
   left: 135.72,
   width: 70.68,
   height: 10.32
}
,
{
   page: 3,
   name: "BUYER AND SELLER ADVISORIES Note All Advisories", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 161.76,
   left: 107.28,
   width: 190.56,
   height: 10.32
}
,
{
   page: 3,
   name: "below are provided for reference purposes only and are not", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 161.76,
   left: 348.72,
   width: 227.64,
   height: 10.32
}
,
{
   page: 3,
   name: "1_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 245.88,
   left: 348.72,
   width: 227.64,
   height: 10.32
}
,
{
   page: 3,
   name: "2_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.8,
   left: 348.72,
   width: 227.64,
   height: 9.84
}
,
{
   page: 3,
   name: "ADDITIONAL TERMS AFFECTING PURCHASE PRICE Buyer represents that funds will be good when deposited with Escrow Holder", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 256.32,
   left: 107.28,
   width: 190.56,
   height: 10.32
}
,
{
   page: 3,
   name: "Check Box50", 
   isText: false,
   type: "checkbox",
   top: 45.818,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box51", 
   isText: false,
   type: "checkbox",
   top: 56.236,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box52", 
   isText: false,
   type: "checkbox",
   top: 67.018,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box53", 
   isText: false,
   type: "checkbox",
   top: 77.618,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box54", 
   isText: false,
   type: "checkbox",
   top: 88.345,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box55", 
   isText: false,
   type: "checkbox",
   top: 98.436,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box56", 
   isText: false,
   type: "checkbox",
   top: 118.2,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box57", 
   isText: false,
   type: "checkbox",
   top: 129.236,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box58", 
   isText: false,
   type: "checkbox",
   top: 140.018,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box59", 
   isText: false,
   type: "checkbox",
   top: 150.491,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box60", 
   isText: false,
   type: "checkbox",
   top: 161.709,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box61", 
   isText: false,
   type: "checkbox",
   top: 191.072,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box62", 
   isText: false,
   type: "checkbox",
   top: 203.29,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box63", 
   isText: false,
   type: "checkbox",
   top: 224.236,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box64", 
   isText: false,
   type: "checkbox",
   top: 234.654,
   left: 71.9998,
   width: 7.9637,
   height: 11.018
}
,
{
   page: 3,
   name: "Check Box65", 
   isText: false,
   type: "checkbox",
   top: 244.69,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box66", 
   isText: false,
   type: "checkbox",
   top: 255.599,
   left: 71.9998,
   width: 7.9637,
   height: 11.019
}
,
{
   page: 3,
   name: "Check Box67", 
   isText: false,
   type: "checkbox",
   top: 99.309,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box68", 
   isText: false,
   type: "checkbox",
   top: 117.763,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box69", 
   isText: false,
   type: "checkbox",
   top: 130.418,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box70", 
   isText: false,
   type: "checkbox",
   top: 151.182,
   left: 313.817,
   width: 7.964,
   height: 10.581
}
,
{
   page: 3,
   name: "Check Box71", 
   isText: false,
   type: "checkbox",
   top: 162.2,
   left: 313.817,
   width: 7.964,
   height: 10.581
}
,
{
   page: 3,
   name: "Check Box72", 
   isText: false,
   type: "checkbox",
   top: 193.381,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box73", 
   isText: false,
   type: "checkbox",
   top: 204.218,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box74", 
   isText: false,
   type: "checkbox",
   top: 223.799,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box75", 
   isText: false,
   type: "checkbox",
   top: 234.963,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box76", 
   isText: false,
   type: "checkbox",
   top: 245.927,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 3,
   name: "Check Box77", 
   isText: false,
   type: "checkbox",
   top: 256.963,
   left: 313.817,
   width: 7.964,
   height: 10.582
}
,
{
   page: 4,
   name: "Property Address_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 13.614,
   left: 110.28,
   width: 330.60,
   height: 11.586
}
,
{
   page: 4,
   name: "Date_4", 
   fontSize: 9,
   type: "date",
   top: 14.662,
   left: 466.8,
   width: 109.56,
   height: 10.538
}
,
{
   page: 4,
   name: "Check Box96", 
   isText: false,
   type: "checkbox",
   top: 619.577,
   left: 90.2579,
   width: 8.6831,
   height: 11.595
}
,
{
   page: 5,
   name: "Property Address_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 14.138,
   left: 110.28,
   width: 330.60,
   height: 12.022
}
,
{
   page: 5,
   name: "Date_5", 
   fontSize: 9,
   type: "date",
   top: 14.662,
   left: 466.8,
   width: 109.56,
   height: 11.498
}
,
{
   page: 6,
   name: "Date_6", 
   fontSize: 9,
   type: "date",
   top: 14.138,
   left: 466.8,
   width: 109.56,
   height: 12.022
}
,
{
   page: 6,
   name: "Seller represents that all items included in the purchase price unless Otherwise Agreed i are owned by Seller and shall", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.185,
   left: 110.28,
   width: 330.60,
   height: 10.975
}
,
{
   page: 7,
   name: "Date_7", 
   fontSize: 9,
   type: "date",
   top: 15.185,
   left: 466.8,
   width: 109.56,
   height: 10.975
}
,
{
   page: 7,
   name: "In the event Seller or Sellers Agent prior to Close Of Escrow becomes aware of adverse conditions materially affecting", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.185,
   left: 110.28,
   width: 330.60,
   height: 10.975
}
,
{
   page: 8,
   name: "Date_8", 
   fontSize: 9,
   type: "date",
   top: 15.698,
   left: 466.8,
   width: 109.56,
   height: 10.462
}
,
{
   page: 8,
   name: "If the Property is a condominium or is located in a planned development or other common interest development with a", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 16.745,
   left: 110.28,
   width: 330.60,
   height: 9.415
}
,
{
   page: 9,
   name: "Date_9", 
   fontSize: 9,
   type: "date",
   top: 15.709,
   left: 466.8,
   width: 109.56,
   height: 10.451
}
,
{
   page: 9,
   name: "Buyer shall after Close Of Escrow receive a recorded grant deed or any other conveyance document required to convey title", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 14.662,
   left: 110.28,
   width: 330.60,
   height: 11.498
}
,
{
   page: 10,
   name: "Date_10", 
   fontSize: 9,
   type: "date",
   top: 14.662,
   left: 466.8,
   width: 109.56,
   height: 11.498
}
,
{
   page: 10,
   name: "NOTICE TO BUYER OR SELLER TO PERFORM The Notice to Buyer to Perform or Notice to Seller to Perform shall i be in", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.709,
   left: 110.28,
   width: 330.60,
   height: 10.451
}
,
{
   page: 11,
   name: "Date_11", 
   fontSize: 9,
   type: "date",
   top: 13.32,
   left: 466.8,
   width: 109.56,
   height: 11.88
}
,
{
   page: 11,
   name: "SCOPE OF DUTY Buyer and Seller acknowledge and agree that Agent i Does not decide what price Buyer should pay or", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 13.32,
   left: 110.28,
   width: 330.60,
   height: 11.88
}
,
{
   page: 12,
   name: "Date_12", 
   fontSize: 9,
   type: "date",
   top: 15.098,
   left: 466.8,
   width: 109.56,
   height: 11.062
}
,
{
   page: 12,
   name: "Text81", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 16.018,
   left: 110.836,
   width: 328.909,
   height: 9.782
}
,
{
   page: 13,
   name: "Property Address_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.971,
   left: 110.28,
   width: 330.60,
   height: 10.189
}
,
{
   page: 13,
   name: "Date_13", 
   fontSize: 9,
   type: "date",
   top: 14.662,
   left: 466.8,
   width: 109.56,
   height: 11.498
}
,
{
   page: 14,
   name: "Property Address_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 15.545,
   left: 110.28,
   width: 330.60,
   height: 10.615
}
,
{
   page: 14,
   name: "Date_14", 
   fontSize: 9,
   type: "date",
   top: 15.545,
   left: 466.8,
   width: 109.56,
   height: 10.615
}
,
{
   page: 14,
   name: "ENTITY BUYERS Note If this paragraph is completed", 
   isText: false,
   type: "checkbox",
   top: 408.24,
   left: 71.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 14,
   name: "enter full name of the estate including case", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 462,
   left: 285.72,
   width: 290.64,
   height: 11.16
}
,
{
   page: 14,
   name: "undefined_91", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 584.062,
   left: 453.72,
   width: 115.68,
   height: 10.178
}
,
{
   page: 14,
   name: "Date_15", 
   fontSize: 9,
   type: "date",
   top: 629.902,
   left: 498.72,
   width: 77.64,
   height: 10.178
}
,
{
   page: 14,
   name: "Printed name of BUYER", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 642.545,
   left: 173.76,
   width: 402.60,
   height: 10.615
}
,
{
   page: 14,
   name: "Printed Name of Legally Authorized Signer", 
   isText: false,
   type: "checkbox",
   top: 656.28,
   left: 71.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 14,
   name: "undefined_92", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 655.625,
   left: 257.28,
   width: 165.60,
   height: 10.615
}
,
{
   page: 14,
   name: "Title if applicable", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 656.062,
   left: 499.32,
   width: 77.16,
   height: 10.178
}
,
{
   page: 14,
   name: "Date_16", 
   fontSize: 9,
   type: "date",
   top: 669.022,
   left: 498.72,
   width: 77.64,
   height: 10.178
}
,
{
   page: 14,
   name: "Printed name of BUYER_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 681.982,
   left: 173.76,
   width: 402.60,
   height: 10.178
}
,
{
   page: 14,
   name: "Printed Name of Legally Authorized Signer_2", 
   isText: false,
   type: "checkbox",
   top: 695.28,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 14,
   name: "undefined_93", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 694.5055,
   left: 257.28,
   width: 165.60,
   height: 10.6145
}
,
{
   page: 14,
   name: "Title if applicable_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 692.76,
   left: 499.32,
   width: 77.16,
   height: 12.36
}
,
{
   page: 14,
   name: "IF MORE THAN TWO SIGNERS USE Additional Signature Addendum CAR Form ASA", 
   isText: false,
   type: "checkbox",
   top: 708.24,
   left: 53.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 14,
   name: "Text82", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 473.89,
   left: 89.4543,
   width: 483.3807,
   height: 9.782
}
,
{
   page: 14,
   name: "Text83", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 583.853,
   left: 328.145,
   width: 121.200,
   height: 10.218
}
,
{
   page: 15,
   name: "Property Address_7", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 12.84,
   left: 110.28,
   width: 330.60,
   height: 12.36
}
,
{
   page: 15,
   name: "Date_17", 
   fontSize: 9,
   type: "date",
   top: 12.84,
   left: 466.8,
   width: 109.56,
   height: 12.36
}
,
{
   page: 15,
   name: "Seller Counter Offer CAR Form SCO or SMCO", 
   isText: false,
   type: "checkbox",
   top: 93.24,
   left: 71.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 15,
   name: "BackUp Offer Addendum CAR Form BUO", 
   isText: false,
   type: "checkbox",
   top: 104.28,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 15,
   name: "ENTITY SELLERS Note If this paragraph", 
   isText: false,
   type: "checkbox",
   top: 117.36,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 15,
   name: "enter full name of the estate including case_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 171.12,
   left: 280.8,
   width: 295.56,
   height: 11.04
}
,
{
   page: 15,
   name: "undefined_96", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 182.76,
   left: 89.76,
   width: 482.40,
   height: 10.44
}
,
{
   page: 15,
   name: "undefined_97", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 290.76,
   left: 453.72,
   width: 115.68,
   height: 12.36
}
,
{
   page: 15,
   name: "Date_18", 
   fontSize: 9,
   type: "date",
   top: 333.84,
   left: 498.72,
   width: 77.64,
   height: 12.36
}
,
{
   page: 15,
   name: "Printed name of SELLER", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 346.8,
   left: 177.24,
   width: 399.12,
   height: 12.36
}
,
{
   page: 15,
   name: "Printed Name of Legally Authorized Signer_3", 
   isText: false,
   type: "checkbox",
   top: 362.28,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 15,
   name: "undefined_98", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 359.76,
   left: 257.28,
   width: 165.60,
   height: 12.36
}
,
{
   page: 15,
   name: "Title if applicable_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 359.76,
   left: 499.32,
   width: 77.16,
   height: 12.36
}
,
{
   page: 15,
   name: "Date_19", 
   fontSize: 9,
   type: "date",
   top: 372.84,
   left: 498.72,
   width: 77.64,
   height: 12.36
}
,
{
   page: 15,
   name: "Printed name of SELLER_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 385.8,
   left: 177.24,
   width: 399.12,
   height: 12.36
}
,
{
   page: 15,
   name: "Printed Name of Legally Authorized Signer_4", 
   isText: false,
   type: "checkbox",
   top: 401.28,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 15,
   name: "undefined_99", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 398.76,
   left: 257.28,
   width: 165.60,
   height: 12.36
}
,
{
   page: 15,
   name: "Title if applicable_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 398.76,
   left: 499.32,
   width: 77.16,
   height: 12.36
}
,
{
   page: 15,
   name: "IF MORE THAN TWO SIGNERS USE Additional Signature Addendum CAR Form ASA_2", 
   isText: false,
   type: "checkbox",
   top: 414.36,
   left: 53.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 15,
   name: "OFFER NOT ACCEPTED", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 432.84,
   left: 145.32,
   width: 30.24,
   height: 12.36
}
,
{
   page: 15,
   name: "undefined_100", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 10,
   top: 432.84,
   left: 178.08,
   width: 30.24,
   height: 12.36
}
,
{
   page: 15,
   name: "No Counter Offer is being made This offer was not accepted by Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 432.84,
   left: 492.24,
   width: 50.64,
   height: 12.36
}
,
{
   page: 15,
   name: "Text84", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 290.927,
   left: 327.708,
   width: 120.764,
   height: 12.400
}
,
{
   page: 16,
   name: "Property Address_8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 13.8,
   left: 110.28,
   width: 330.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_20", 
   fontSize: 9,
   type: "date",
   top: 13.8,
   left: 466.8,
   width: 109.56,
   height: 12.36
}
,
{
   page: 16,
   name: "Buyers Brokerage Firm", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 107.76,
   left: 168.48,
   width: 255.60,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 107.76,
   left: 471.48,
   width: 104.88,
   height: 12.36
}
,
{
   page: 16,
   name: "By", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.84,
   left: 84.72,
   width: 255.60,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 120.84,
   left: 387.72,
   width: 90.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_21", 
   fontSize: 9,
   type: "date",
   top: 120.84,
   left: 501.72,
   width: 74.64,
   height: 12.36
}
,
{
   page: 16,
   name: "By_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 133.8,
   left: 84.72,
   width: 255.60,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 133.8,
   left: 387.72,
   width: 90.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_22", 
   fontSize: 9,
   type: "date",
   top: 133.8,
   left: 501.72,
   width: 74.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Address", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 146.76,
   left: 107.28,
   width: 180.60,
   height: 12.36
}
,
{
   page: 16,
   name: "City_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 146.76,
   left: 307.8,
   width: 140.52,
   height: 12.36
}
,
{
   page: 16,
   name: "State", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 146.76,
   left: 473.76,
   width: 20.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Zip", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 146.76,
   left: 511.2,
   width: 65.16,
   height: 12.36
}
,
{
   page: 16,
   name: "Email", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.72,
   left: 96.72,
   width: 305.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Phone", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 159.72,
   left: 440.28,
   width: 136.08,
   height: 12.36
}
,
{
   page: 16,
   name: "Attached DEDA If Parties elect to have an alternative Delivery method such method may be indicated on CAR Form DEDA", 
   isText: false,
   type: "checkbox",
   top: 214.32,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 16,
   name: "Sellers Brokerage Firm", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 229.8,
   left: 168,
   width: 255.6,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 229.8,
   left: 470.88,
   width: 105.48,
   height: 12.36
}
,
{
   page: 16,
   name: "By_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 242.76,
   left: 84.72,
   width: 255.60,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 242.76,
   left: 387.72,
   width: 90.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_23", 
   fontSize: 9,
   type: "date",
   top: 242.76,
   left: 501.72,
   width: 74.64,
   height: 12.36
}
,
{
   page: 16,
   name: "By_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 255.84,
   left: 84.72,
   width: 255.60,
   height: 12.36
}
,
{
   page: 16,
   name: "DRE Lic_6", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 255.84,
   left: 387.72,
   width: 90.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_24", 
   fontSize: 9,
   type: "date",
   top: 255.84,
   left: 501.72,
   width: 74.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Address_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 271.8,
   left: 107.28,
   width: 180.60,
   height: 12.36
}
,
{
   page: 16,
   name: "City_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 271.8,
   left: 307.8,
   width: 140.52,
   height: 12.36
}
,
{
   page: 16,
   name: "Zip_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 271.8,
   left: 511.2,
   width: 65.16,
   height: 12.36
}
,
{
   page: 16,
   name: "Email_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 284.88,
   left: 96.72,
   width: 305.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Phone_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 284.88,
   left: 440.28,
   width: 136.08,
   height: 12.36
}
,
{
   page: 16,
   name: "More than one agent from the same firm represents Seller Additional Agent Acknowledgement CAR Form AAA attached", 
   isText: false,
   type: "checkbox",
   top: 301.32,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 16,
   name: "More than one brokerage firm represents Seller Additional Broker Acknowledgement CAR Form ABA attached", 
   isText: false,
   type: "checkbox",
   top: 312.24,
   left: 71.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 16,
   name: "Attached DEDA If Parties elect to have an alternative Delivery method such method may be indicated on CAR Form DEDA_2", 
   isText: false,
   type: "checkbox",
   top: 340.32,
   left: 71.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 16,
   name: "a deposit in the amount of", 
   isText: false,
   type: "checkbox",
   top: 439.32,
   left: 351.48,
   width: 8.28,
   height: 10.32
}
,
{
   page: 16,
   name: "Counter", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 438.109,
   left: 475.8,
   width: 60.12,
   height: 11.051
}
,
{
   page: 16,
   name: "Offer numbers", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 449.28,
   left: 96.12,
   width: 155.64,
   height: 10.80
}
,
{
   page: 16,
   name: "and", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 449.64,
   left: 271.32,
   width: 115.20,
   height: 10.44
}
,
{
   page: 16,
   name: "Escrow Holder is advised by", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 471.84,
   left: 151.2,
   width: 165.6,
   height: 12.36
}
,
{
   page: 16,
   name: "that the date of Acceptance of the Agreement is", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 471.84,
   left: 511.2,
   width: 65.16,
   height: 12.36
}
,
{
   page: 16,
   name: "Escrow Holder_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 484.8,
   left: 96.72,
   width: 315.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Escrow", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 484.8,
   left: 453.72,
   width: 122.64,
   height: 12.36
}
,
{
   page: 16,
   name: "By_5", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 497.76,
   left: 48.72,
   width: 410.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Date_25", 
   fontSize: 9,
   type: "date",
   top: 497.76,
   left: 482.76,
   width: 93.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Address_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 510.72,
   left: 71.28,
   width: 505.08,
   height: 12.36
}
,
{
   page: 16,
   name: "PhoneFaxEmail", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 523.8,
   left: 109.68,
   width: 466.68,
   height: 12.36
}
,
{
   page: 16,
   name: "Escrow Holder has the following license number", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 536.88,
   left: 237.24,
   width: 90.60,
   height: 12.36
}
,
{
   page: 16,
   name: "Department of Financial Protection and Innovation", 
   isText: false,
   type: "checkbox",
   top: 552.24,
   left: 35.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 16,
   name: "Department of Insurance", 
   isText: false,
   type: "checkbox",
   top: 552.24,
   left: 251.28,
   width: 8.40,
   height: 10.44
}
,
{
   page: 16,
   name: "Department of Real Estate", 
   isText: false,
   type: "checkbox",
   top: 552.24,
   left: 365.88,
   width: 8.40,
   height: 10.44
}
,
{
   page: 16,
   name: "PRESENTATION OF OFFER", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 575.76,
   left: 159.72,
   width: 45.24,
   height: 12.36
}
,
{
   page: 16,
   name: "undefined_105", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 575.76,
   left: 207.48,
   width: 45.36,
   height: 12.36
}
,
{
   page: 16,
   name: "Sellers Brokerage Firm presented this offer to Seller on", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 575.76,
   left: 478.44,
   width: 65.64,
   height: 12.36
}
,
{
   page: 16,
   name: "Check Box85", 
   isText: false,
   type: "checkbox",
   top: 175.127,
   left: 71.88,
   width: 8.40,
   height: 10.320
}
,
{
   page: 16,
   name: "Check Box86", 
   isText: false,
   type: "checkbox",
   top: 186.763,
   left: 71.88,
   width: 8.40,
   height: 10.320
}
,
{
   page: 16,
   name: "Text87", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 198.545,
   left: 329.454,
   width: 244.691,
   height: 12.836
}
,
{
   page: 16,
   name: "Text88", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 326.836,
   left: 476.072,
   width: 103.309,
   height: 9.345
}
] }
