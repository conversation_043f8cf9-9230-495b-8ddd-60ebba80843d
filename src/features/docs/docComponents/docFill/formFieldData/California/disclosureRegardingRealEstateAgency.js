export function disclosureRegardingRealEstateAgency() {
return [   //2024 Release 2025-08-24 08:53:13
 {
        page: 0,
        name: "<PERSON><PERSON>",
        isText: false,
        fontSize: 10,
        type: "logo",
        top: 757,
        left: 52.9457,
        width: 133.33,
      height: 20,
      }
      ,
{
   page: 0,
   name: "If checked This form is being provided in connection with a transaction for a leasehold interest exceeding one year as per Civil", 
   isText: false,
   type: "checkbox",
   top: 67.32,
   left: 35.88,
   width: 8.40,
   height: 10.32
}
,
{
   page: 0,
   name: "Buyer", 
   isText: false,
   type: "checkbox",
   top: 638.28,
   left: 35.88,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "<PERSON>ller", 
   isText: false,
   type: "checkbox",
   top: 638.28,
   left: 74.28,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Landlord", 
   isText: false,
   type: "checkbox",
   top: 638.28,
   left: 109.44,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "undefined", 
   isText: false,
   type: "checkbox",
   top: 638.28,
   left: 156.24,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Buyer_2", 
   isText: false,
   type: "checkbox",
   top: 654.24,
   left: 35.88,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Seller_2", 
   isText: false,
   type: "checkbox",
   top: 654.24,
   left: 74.28,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Landlord_2", 
   isText: false,
   type: "checkbox",
   top: 654.24,
   left: 109.44,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "undefined_2", 
   isText: false,
   type: "checkbox",
   top: 654.24,
   left: 156.24,
   width: 8.28,
   height: 10.44
}
,
{
   page: 0,
   name: "Agent", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.378,
   left: 61.8,
   width: 375.6,
   height: 12.742
}
,
{
   page: 0,
   name: "DRE Lic", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 668.378,
   left: 484.8,
   width: 91.56,
   height: 12.742
}
,
{
   page: 0,
   name: "signature By", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 691.32,
   left: 48.72,
   width: 305.64,
   height: 10.92
}
,
{
   page: 0,
   name: "DRE Lic_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 688.451,
   left: 401.76,
   width: 75.60,
   height: 13.789
}
,
{
   page: 0,
   name: "Date_3", 
   fontSize: 9,
   type: "date",
   top: 686.88,
   left: 500.76,
   width: 75.60,
   height: 15.36
}
,
{
   page: 1,
   name: "License Number", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 433.6,
   left: 510.48,
   width: 62.28,
   height: 12.513
}
,
{
   page: 1,
   name: "Is the broker of check one", 
   isText: false,
   type: "checkbox",
   top: 449.76,
   left: 157.68,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "the seller or", 
   isText: false,
   type: "checkbox",
   top: 449.76,
   left: 216.6,
   width: 6.6,
   height: 8.16
}
,
{
   page: 1,
   name: "License Number_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 454.6,
   left: 510.48,
   width: 62.28,
   height: 12.513
}
,
{
   page: 1,
   name: "Is check one", 
   isText: false,
   type: "checkbox",
   top: 470.76,
   left: 110.64,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "the Sellers Agent salesperson or broker associate", 
   isText: false,
   type: "checkbox",
   top: 470.76,
   left: 311.16,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "License Number_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 475.6,
   left: 510.48,
   width: 62.28,
   height: 12.513
}
,
{
   page: 1,
   name: "Is the broker of check one_2", 
   isText: false,
   type: "checkbox",
   top: 491.76,
   left: 157.68,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "the buyer or", 
   isText: false,
   type: "checkbox",
   top: 491.76,
   left: 217.44,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "License Number_4", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 496.6,
   left: 510.48,
   width: 62.28,
   height: 12.513
}
,
{
   page: 1,
   name: "d The disclosures and confirmation required by this section shall be in addition to the disclosure required by  207914 An agents duty to provide", 
   isText: false,
   type: "checkbox",
   top: 512.76,
   left: 110.64,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "the Buyers Agent salesperson or broker associate", 
   isText: false,
   type: "checkbox",
   top: 512.76,
   left: 311.52,
   width: 6.60,
   height: 8.16
}
,
{
   page: 1,
   name: "Text91", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 436.093,
   left: 139.81,
   width: 308.138,
   height: 9.956
}
,
{
   page: 1,
   name: "Text92", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 457.133,
   left: 105.774,
   width: 341.650,
   height: 10.480
}
,
{
   page: 1,
   name: "Text93", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 478.602,
   left: 139.287,
   width: 308.661,
   height: 10.480
}
,
{
   page: 1,
   name: "Text94", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 499.547,
   left: 108.392,
   width: 339.556,
   height: 8.909
}
] }
