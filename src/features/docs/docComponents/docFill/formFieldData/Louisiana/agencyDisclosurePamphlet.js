export function agencyDisclosurePamphlet() {
return [   //2024 Release 2024-06-06 16:47:45
{
   page: 1,
   name: "BuyersL<PERSON>ees", 
   isText: false,
   type: "checkbox",
   top: 3.6,
   left: 98.64,
   width: 13.08,
   height: 9.72
}
,
{
   page: 1,
   name: "undefined", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 182.334,
   left: 141.535,
   width: 17.880,
   height: 188.160
}
,
{
   page: 1,
   name: "Print name and date", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 184.08,
   left: 197.367,
   width: 17.880,
   height: 188.16
}
,
{
   page: 1,
   name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", 
   isText: false,
   type: "checkbox",
   top: 3.6,
   left: 256.8,
   width: 13.08,
   height: 9.72
}
,
{
   page: 1,
   name: "undefined_2", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 183.207,
   left: 300.687,
   width: 17.880,
   height: 188.160
}
,
{
   page: 1,
   name: "Print name and date_2", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 182.334,
   left: 354.655,
   width: 17.880,
   height: 188.160
}
,
{
   page: 1,
   name: "Licensee", 
   isText: false,
   type: "checkbox",
   top: 3.6,
   left: 414.96,
   width: 13.08,
   height: 9.72
}
,
{
   page: 1,
   name: "undefined_3", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 184.08,
   left: 430.8,
   width: 17.88,
   height: 188.16
}
,
{
   page: 1,
   name: "undefined_4", 
   type: "textarea",
   fontName: "Helvetica",
   fontSize: 10,
   top: 188.247,
   left: 459.251,
   width: 17.880,
   height: 198.120
}
] }
