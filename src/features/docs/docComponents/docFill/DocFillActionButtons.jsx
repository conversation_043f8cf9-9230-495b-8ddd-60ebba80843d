import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Icon } from "semantic-ui-react";
import { toast } from "react-toastify";
import { fillAndFlattenPdf } from "../../../../app/pdfLib/pdfLib";
import { updateDocInDb } from "../../../../app/firestore/firestoreService";
import download from "downloadjs";
import { changePageScaleFill } from "../../../../app/annots/annotSlice";
import { openModal } from "../../../../app/common/modals/modalSlice";
import { useMediaQuery } from "react-responsive";

export default function DocFillActionButtons() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { doc } = useSelector((state) => state.doc);
  const { pageScaleFill } = useSelector((state) => state.annot);
  const { transaction } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const isMobile = useMediaQuery({ query: "(max-width:1000px)" });

  const [processing, setProcessing] = useState(false);

  function closeDocument() {
    navigate(`/transactions/${doc.transactionId}/documents`);
  }

  async function saveAnnots() {
    toast.success("Document successfully saved");
  }

  async function downloadPdf() {
    setProcessing(true);
    const pdfBytes = await fillAndFlattenPdf(
      doc,
      transaction,
      currentUserProfile
    );
    const newDocName = doc.name.replace(/\./g, "-");
    download(pdfBytes, newDocName, "application/pdf");
    setProcessing(false);
  }

  function prepareForSigning() {
    updateDocInDb(doc.id, { subStatus: "Preparing Signatures" });
    navigate(`/transactions/${doc.transactionId}/documents/${doc.id}/prepare`);
  }

  function handleZoomIn() {
    if (pageScaleFill < 4) {
      dispatch(changePageScaleFill(pageScaleFill * 1.25));
    }
  }

  function handleZoomOut() {
    if (pageScaleFill > 0.5) {
      dispatch(changePageScaleFill(pageScaleFill / 1.25));
    }
  }

  function handleAddClause() {
    dispatch(
      openModal({
        modalType: "ClausesView",
      })
    );
  }

  function handleDeadlineTemplate() {
    dispatch(
      openModal({
        modalType: "DeadlineTemplatesSelect",
      })
    );
  }

  function handleImportMls() {
    dispatch(
      openModal({
        modalType: "DocFillImportMlsModal",
      })
    );
  }

  return (
    <>
      <div
        className={
          isMobile
            ? "mini horizontal padding background-white"
            : "large horizontal padding background-white"
        }
        style={{
          top: "auto",
          bottom: "0",
          position: "fixed",
          width: "100%",
          height: isMobile ? "50px" : "80px",
          borderTop: "1px solid #e8e8e8",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            height: "100%",
            float: "left",
          }}
        >
          {processing ? (
            <Button loading style={{ marginRight: "24px" }} content="Loading" />
          ) : (
            <Button
              disabled={processing}
              onClick={() => downloadPdf()}
              style={{ marginRight: isMobile ? "4px" : "24px" }}
              icon={isMobile ? "download" : null}
              content={isMobile ? "" : "Download"}
              className={isMobile ? "compact" : null}
              size={isMobile ? "mini" : null}
            />
          )}

          <Icon
            size="large"
            color="grey"
            name="minus circle"
            onClick={() => handleZoomOut()}
            style={{ cursor: "pointer" }}
          />
          <p
            className="zero bottom margin"
            style={{
              marginLeft: "3px",
              marginRight: "6px",
              fontWeight: "bold",
            }}
          >
            {Math.round(pageScaleFill * 100)}%
          </p>
          <Icon
            size="large"
            color="grey"
            name="plus circle"
            onClick={() => handleZoomIn()}
            style={{ cursor: "pointer" }}
          />
        </div>
        <div
          className={isMobile ? "mini right margin" : "large right margin"}
          style={{
            display: "flex",
            alignItems: "center",
            height: "100%",
            float: "right",
          }}
        >
          <Button
            onClick={() => closeDocument()}
            icon={isMobile ? "close" : null}
            content={isMobile ? "" : "Close"}
            style={{ marginRight: isMobile ? "2px" : "10px" }}
            size={isMobile ? "mini" : null}
            className={isMobile ? "compact" : null}
          />
          <Button
            primary
            onClick={() => handleAddClause()}
            icon={isMobile ? "list" : null}
            content={isMobile ? "" : "Clauses"}
            style={{ marginRight: isMobile ? "2px" : "10px" }}
            size={isMobile ? "mini" : null}
            className={isMobile ? "compact" : null}
          />
          {doc.canApplyDeadlinesTemplate && (
            <Button
              primary
              onClick={() => handleDeadlineTemplate()}
              icon={isMobile ? "calendar outline" : null}
              content={isMobile ? "" : "Deadlines"}
              style={{ marginRight: isMobile ? "2px" : "10px" }}
              size={isMobile ? "mini" : null}
              className={isMobile ? "compact" : null}
            />
          )}
          {doc.canImportFromListing && (
            <Button
              primary
              onClick={() => handleImportMls()}
              content={isMobile ? "MLS" : "Import MLS"}
              style={{ marginRight: isMobile ? "2px" : "10px" }}
              size={isMobile ? "mini" : null}
              className={isMobile ? "compact" : null}
            />
          )}
          <Button
            primary
            onClick={() => saveAnnots()}
            content={isMobile ? "Save" : "Save"}
            style={{ marginRight: isMobile ? "2px" : "10px" }}
            size={isMobile ? "mini" : null}
            className={isMobile ? "compact" : null}
          />
          <Button
            onClick={() => prepareForSigning()}
            content={isMobile ? "Sigs" : "Prepare For Signing"}
            size={isMobile ? "mini" : null}
            className={isMobile ? "compact dark-blue" : "dark-blue"}
          />
        </div>
      </div>
    </>
  );
}
