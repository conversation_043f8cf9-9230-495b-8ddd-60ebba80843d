import React, { useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Header,
  Input,
  Segment,
  Message,
} from "semantic-ui-react";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { updateDocInDb } from "../../../app/firestore/firestoreService";
import { isValidName } from "../../../app/common/util/util";
import { toast } from "react-toastify";

export default function DocEditArchiveLabelModal({ doc }) {
  const dispatch = useDispatch();
  const [textInput, setTextInput] = useState(doc.archivedLabel || "");
  const [validationError, setValidationError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef(null);

  function handleTextInputChange(event) {
    let value = event.target.value;
    // Replace multiple consecutive spaces with single space
    value = value.replace(/\s+/g, ' ');
    setTextInput(value);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError("");
    }
  }

  async function handleSubmit() {
    // Validate the input if not empty
    if (textInput.trim() && !isValidName(textInput.trim())) {
      setValidationError("Label can only contain letters, numbers, underscores, parentheses, dashes, and spaces");
      return;
    }

    setIsSubmitting(true);
    try {
      const updateFields = textInput.trim() 
        ? { archivedLabel: textInput.trim() }
        : { archivedLabel: null }; // Remove the label if empty
      
      await updateDocInDb(doc.id, updateFields);
      toast.success(textInput.trim() ? "Archive label updated successfully" : "Archive label removed successfully");
      dispatch(
        closeModal({
          modalType: "DocEditArchiveLabelModal",
        })
      );
    } catch (error) {
      toast.error(error.message);
      setValidationError("Failed to update archive label. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <ModalWrapper size="small">
        <Segment>
          <Grid>
            <Grid.Column>
              <Header size="large" color="blue">
                Edit Archive Label
              </Header>
              <Divider />
              <p>
                Update the archive label for this document. Leave empty to remove the label.
              </p>
              <Input
                fluid
                ref={inputRef}
                placeholder="Enter archive label (leave empty to remove)"
                value={textInput}
                onChange={handleTextInputChange}
                error={!!validationError}
                disabled={isSubmitting}
              />
              {validationError && (
                <Message negative size="small">
                  {validationError}
                </Message>
              )}
              <Divider />
              <Grid>
                <Grid.Column width={8}>
                  <Button
                    fluid
                    color="blue"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    loading={isSubmitting}
                  >
                    Update Label
                  </Button>
                </Grid.Column>
                <Grid.Column width={8}>
                  <Button
                    fluid
                    onClick={() => dispatch(closeModal())}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </Grid.Column>
              </Grid>
            </Grid.Column>
          </Grid>
        </Segment>
      </ModalWrapper>
    </>
  );
}
