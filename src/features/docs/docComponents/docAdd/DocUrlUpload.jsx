import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Message, Button, Form } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import LoadingComponent from "../../../../app/layout/LoadingComponent";
import { functionUploadPdfFromUrl } from "../../../../app/firestore/functionsService";
import { addUploadedDocToDb } from "../../../../app/firestore/firestoreService";
import { updateTransUpdatedInDb } from "../../../../app/firestore/firestoreService";
import {
  parseCTMecontractsEmailLink,
  isValidCTMecontractsEmailLink
} from "../../../../app/common/util/ctmecontractsParser";

export default function DocUrlUpload({ setOpenDocUploadModal }) {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState(null);
  const [url, setUrl] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!url.trim()) {
      setError("Please enter a URL");
      return;
    }

    // Validate URL format
    if (!isValidCTMecontractsEmailLink(url)) {
      setError("Please enter a valid CTMecontracts.com email link. Supported formats include SafeLinks URLs from Outlook emails and direct CTMecontracts.com PDF links.");
      return;
    }

    // Validate transaction data
    if (!transaction || !transaction.id || !transaction.userId) {
      setError("Transaction information is missing. Please refresh the page and try again.");
      return;
    }

    setLoading(true);
    setError(null);
    setMessage(null);

    try {
      // Parse the email link to extract the actual PDF URL and filename
      const parsedLink = parseCTMecontractsEmailLink(url);

      console.log("Parsed link:", parsedLink);

      // Validate parsed link
      if (!parsedLink.isValid || !parsedLink.url) {
        throw new Error("Unable to extract PDF URL from the provided link");
      }

      // Call the Cloud Function to download and upload the PDF
      const result = await functionUploadPdfFromUrl({
        url: parsedLink.url,
        filename: parsedLink.filename,
        transactionId: transaction.id,
        userId: transaction.userId
      });

      console.log("Upload result:", result);

      if (result.success) {
        // Get the actual download URL using the existing Firebase service
        const { getDocDownloadUrl } = await import("../../../../app/firestore/firebaseService");
        let downloadUrl = result.downloadUrl;

        // If we got a storage path (gs://), convert it to a download URL
        if (downloadUrl.startsWith('gs://')) {
          try {
            downloadUrl = await getDocDownloadUrl(result.docPath);
          } catch (error) {
            console.warn("Could not get download URL, using storage path:", error);
            downloadUrl = result.downloadUrl;
          }
        }

        // Add the document to the database
        await addUploadedDocToDb(
          transaction,
          result.docPath,
          result.docId,
          result.filename,
          "application/pdf",
          result.fileSize,
          downloadUrl
        );

        // Update transaction timestamp
        await updateTransUpdatedInDb(transaction.id);

        setMessage(`Successfully uploaded: ${result.filename}`);

        // Clear the URL input
        setUrl("");

        // Close modal after a short delay
        setTimeout(() => {
          if (setOpenDocUploadModal) {
            setOpenDocUploadModal(false);
          }
          dispatch(closeModal({ modalType: "DocAddDocumentsModal" }));
        }, 2000);
      } else {
        setError("Failed to upload PDF from URL. Please check the link and try again.");
      }
    } catch (error) {
      console.error("Error uploading PDF from URL:", error);

      // Extract and format error message
      let errorMessage = "Failed to upload PDF from URL";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.details && error.details.error) {
        errorMessage = error.details.error;
      } else if (error.code) {
        // Handle Firebase function errors
        switch (error.code) {
          case 'unauthenticated':
            errorMessage = "Authentication required. Please log in and try again.";
            break;
          case 'permission-denied':
            errorMessage = "Permission denied. You don't have access to upload documents to this transaction.";
            break;
          case 'unavailable':
            errorMessage = "Service temporarily unavailable. Please try again in a few moments.";
            break;
          case 'deadline-exceeded':
            errorMessage = "Request timed out. The PDF source may be slow or unavailable.";
            break;
          default:
            errorMessage = `Upload failed: ${error.message || error.code}`;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Add helpful suggestions based on error type
      if (errorMessage.includes('timeout') || errorMessage.includes('unavailable')) {
        errorMessage += " Please check your internet connection and try again.";
      } else if (errorMessage.includes('not found') || errorMessage.includes('404')) {
        errorMessage += " Please verify the PDF link is correct and accessible.";
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
    // Clear errors when user starts typing
    if (error) setError(null);
    if (message) setMessage(null);
  };

  const containerStyles = {
    padding: "20px",
    textAlign: "center",
    minHeight: "200px",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center"
  };

  return (
    <Grid>
      <Grid.Column width={16}>
        <div style={containerStyles}>
          <Header icon color="grey">
            <Icon name="linkify" />
            <br />
            From Email CTMe Link
          </Header>

          <Form
            onSubmit={handleSubmit}
            style={{ width: "100%", maxWidth: "500px" }}
          >
            <Form.Field>
              <textarea
                placeholder="Paste your CTMecontracts email link 'PDF Print (only)' here..."
                value={url}
                onChange={handleUrlChange}
                rows={3}
                style={{
                  width: "100%",
                  padding: "10px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  fontSize: "14px",
                  resize: "vertical",
                }}
              />
            </Form.Field>

            <Button
              type="submit"
              primary
              disabled={loading || !url.trim()}
              style={{ marginTop: "10px" }}
            >
              {loading ? "Uploading..." : "Upload PDF"}
            </Button>
          </Form>

          <div
            style={{
              marginTop: "15px",
              fontSize: "12px",
              color: "#666",
              textAlign: "left",
            }}
          >
            <p
              style={{
                marginTop: "10px",
                fontSize: "11px",
                fontStyle: "italic",
              }}
            >
              Copy link from your email for the PDF{" "}
              <Icon name="file pdf outline" /> "PDF Print (only)" and paste
              above. TransActioner will automatically extract the PDF and add it
              to your transaction.
            </p>
          </div>
        </div>

        {loading && <LoadingComponent />}

        {(error || message) && (
          <Message
            negative={!!error}
            positive={!!message}
            style={{ marginTop: "15px" }}
          >
            <Message.Header>
              {error ? "Upload Failed" : "Upload Successful"}
            </Message.Header>
            <p>{error || message}</p>
          </Message>
        )}
      </Grid.Column>
    </Grid>
  );
}
