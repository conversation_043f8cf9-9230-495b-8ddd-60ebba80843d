import React, { useState } from "react";
import { Segment, Tab } from "semantic-ui-react";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import DocFileUpload from "./DocFileUpload";
import DocUrlUpload from "./DocUrlUpload";

export default function DocUploadModal() {
  const [, setOpenDocUploadModal] = useState(true);

  const panes = [
    {
      menuItem: { key: "file", icon: "upload", content: "Upload File" },
      render: () => (
        <Tab.Pane>
          <DocFileUpload
            status={"In Progress"}
            setOpenDocUploadModal={setOpenDocUploadModal}
          />
        </Tab.Pane>
      ),
    },
    {
      menuItem: { key: "url", icon: "linkify", content: "From Email Link" },
      render: () => (
        <Tab.Pane>
          <DocUrlUpload setOpenDocUploadModal={setOpenDocUploadModal} />
        </Tab.Pane>
      ),
    },
  ];

  return (
    <>
      <ModalWrapper>
        <Segment>
          <Tab
            panes={panes}
            defaultActiveIndex={0}
            menu={{ secondary: true, pointing: true }}
          />
        </Segment>
      </ModalWrapper>
    </>
  );
}
