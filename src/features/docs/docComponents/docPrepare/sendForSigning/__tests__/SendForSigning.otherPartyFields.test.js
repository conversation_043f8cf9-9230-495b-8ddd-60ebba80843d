import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import SendForSigning from '../SendForSigning';

// Mock dependencies
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(() => jest.fn())
}));

jest.mock('react-responsive', () => ({
  useMediaQuery: jest.fn(() => false)
}));

jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('../../../../../../app/firestore/firebaseService', () => ({
  uploadBlobToStorage: jest.fn()
}));

jest.mock('../../../../../../app/pdfLib/pdfLib', () => ({
  fillAndFlattenPdf: jest.fn()
}));

jest.mock('../../../../../../app/firestore/firestoreService', () => ({
  updateDocSentForSigningInDb: jest.fn(),
  sendDocSharingEmail: jest.fn(),
  addHistoryToDb: jest.fn()
}));

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockState = {
  doc: {
    doc: {
      id: 'test-doc',
      annotsInProgress: [
        {
          uniqueId: 'annot-1',
          type: 'signature',
          signerRole: 'Buyer',
          agentsField: false
        },
        {
          uniqueId: 'annot-2',
          type: 'signature', 
          signerRole: 'Seller',
          agentsField: false
        }
      ]
    }
  },
  transaction: {
    transaction: {
      agentRepresents: 'Buyer'
    },
    transClients: [{ role: 'Buyer', firstName: 'John', lastName: 'Doe' }],
    parties: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ],
    allParties: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ]
  },
  profile: {
    currentUserProfile: {
      firstName: 'Agent',
      lastName: 'User'
    }
  }
};

describe('SendForSigning - Other Party Fields', () => {
  test('displays other party signature fields information when present', () => {
    const store = createMockStore(mockState);
    
    render(
      <Provider store={store}>
        <SendForSigning />
      </Provider>
    );

    // Should show the additional signature fields section
    expect(screen.getByText('Additional Signature Fields Set Up')).toBeInTheDocument();
    expect(screen.getByText(/The following signature fields are set up for the other agent/)).toBeInTheDocument();
    
    // Should show the seller field (other party for buyer agent)
    expect(screen.getByText('Seller:')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    
    // Should show the informational note
    expect(screen.getByText(/You do not share or send for signing directly/)).toBeInTheDocument();
    expect(screen.getByText(/If you are acting as a dual agent/)).toBeInTheDocument();
  });

  test('does not display other party section when no other party fields exist', () => {
    const stateWithoutOtherPartyFields = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          annotsInProgress: [
            {
              uniqueId: 'annot-1',
              type: 'signature',
              signerRole: 'Buyer',
              agentsField: false
            }
          ]
        }
      }
    };
    
    const store = createMockStore(stateWithoutOtherPartyFields);
    
    render(
      <Provider store={store}>
        <SendForSigning />
      </Provider>
    );

    // Should not show the additional signature fields section
    expect(screen.queryByText('Additional Signature Fields Set Up')).not.toBeInTheDocument();
  });

  test('shows role name when party info is not available', () => {
    const stateWithoutPartyInfo = {
      ...mockState,
      transaction: {
        ...mockState.transaction,
        allParties: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Seller info missing
        ]
      }
    };
    
    const store = createMockStore(stateWithoutPartyInfo);
    
    render(
      <Provider store={store}>
        <SendForSigning />
      </Provider>
    );

    // Should show role name when party info is missing
    expect(screen.getByText('Seller:')).toBeInTheDocument();
    expect(screen.getByText('Name not provided')).toBeInTheDocument();
  });
});
