import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, Popup } from "semantic-ui-react";
import {
  changeEditMode,
  changeSelectedSigner,
} from "../../../../../app/annots/annotSlice";
import { openModal } from "../../../../../app/common/modals/modalSlice";
import { roleIsOtherAgentsClients } from "../sendForSigning/sendForSigningUtils";
import DocPrepareSignerListItem from "./DocPrepareSignerListItem";
import { useMediaQuery } from "react-responsive";

export default function DocPrepareSignerList() {
  const dispatch = useDispatch();
  const { signerListDisplay, signerListPossible } = useSelector(
    (state) => state.annot
  );
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { editMode } = useSelector((state) => state.annot);
  const { transaction } = useSelector((state) => state.transaction);
  const { doc } = useSelector((state) => state.doc);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleAddSigner() {
    dispatch(
      openModal({
        modalType: "DocPrepareAddSigner",
      })
    );
  }

  function handleYourFieldsClick() {
    dispatch(changeSelectedSigner(currentUserProfile));
    dispatch(changeEditMode("agent"));
  }

  const annotColor = "200, 200, 200";

  // Separate current agent's clients from other party clients
  const currentAgentClients = signerListDisplay.filter(
    (signer) => !roleIsOtherAgentsClients(signer.role, transaction)
  );

  // Check if there are annotsInProgress for other side clients
  const hasOtherSideAnnots =
    doc.annotsInProgress &&
    doc.annotsInProgress.some(
      (annot) =>
        roleIsOtherAgentsClients(annot.signerRole, transaction) &&
        !annot.agentsField
    );

  // Show other party clients if they are in signerListDisplay (explicitly added via modal)
  // Filter from signerListDisplay to show clients that were explicitly added
  const otherPartyClients = signerListDisplay.filter(
    (signer) =>
      roleIsOtherAgentsClients(signer.role, transaction) &&
      signer.firstName &&
      signer.lastName // Only show if they have names from parties
  );

  // Debug logging
  // console.log("DocPrepareSignerList - signerListDisplay:", signerListDisplay);
  // console.log("DocPrepareSignerList - currentAgentClients:", currentAgentClients);
  // console.log("DocPrepareSignerList - otherPartyClients:", otherPartyClients);
  // console.log("DocPrepareSignerList - agentRepresents:", transaction.agentRepresents);

  // Determine the title for other party clients
  const otherPartyTitle =
    transaction.agentRepresents === "Buyer" ? "Seller" : "Buyer";

  return (
    <>
      {" "}
      <div
        onClick={() => handleYourFieldsClick()}
        style={{
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
        }}
      >
        <Icon
          className="small right margin"
          name={
            editMode === "agent" ? "square check outline" : "square outline"
          }
          size="large"
        />
        <div style={{ width: "20px", height: "15px" }}>
          <div
            className="annot-outer-wrapper"
            style={{
              backgroundColor: `rgba(${annotColor}, 0.2)`,
            }}
          >
            <div
              className="annot-inner-wrapper"
              style={{
                boxShadow: `rgb(${annotColor}) 0px 0px 0px 1px`,
                zIndex: 5,
              }}
            ></div>
          </div>
        </div>
        <h5
          className="zero top margin small left margin"
          style={{ color: "#666" }}
        >
          Me:&nbsp;
          {currentUserProfile.firstName + " " + currentUserProfile.lastName}
        </h5>
      </div>
      <br />
      {currentAgentClients.map((client, index) => (
        <DocPrepareSignerListItem
          signer={client}
          key={client.role}
          index={index}
        />
      ))}
      {otherPartyClients.length > 0 && (
        <>
          <div className="tiny vertical margin">
            <div
              style={{
                color: "#9a9a9a",
                marginBottom: "2px",
                marginTop: "0px",
              }}
            >
              {otherPartyTitle} Clients:{" "}
              {!isMobile && (
                <Popup
                  flowing
                  size="small"
                  trigger={
                    <Icon
                      name="info"
                      color="blue"
                      circular
                      inverted
                      size="tiny"
                      style={{ marginLeft: "3px", marginBottom: "3px" }}
                    />
                  }
                >
                  <p className="bold text blue mini bottom margin">
                    Other Side's Clients
                  </p>
                  <p style={{ margin: "0 0 8px 0" }}>
                    <strong>Note:</strong> You do not share or send for signing
                    directly to the other side's clients.
                    <br />
                    When the other agent logs in (as a TransActioner subscriber
                    or not), <br /> they can easily send these documents out to
                    their clients for signatures.
                  </p>
                  <p style={{ margin: "0" }}>
                    <strong>Dual Agent:</strong>If you are acting as a dual
                    agent, <br />
                    you should set up the other side as a separate transaction,
                    link the two transactions, <br />
                    and then send for signatures to the other side's clients
                    inside that other transaction.
                  </p>
                </Popup>
              )}
            </div>
          </div>
          {otherPartyClients.map((client, index) => (
            <DocPrepareSignerListItem
              signer={client}
              key={client.role}
              index={index}
            />
          ))}
        </>
      )}
      <Button basic size="tiny" onClick={() => handleAddSigner()}>
        Add Signer
      </Button>
    </>
  );
}
