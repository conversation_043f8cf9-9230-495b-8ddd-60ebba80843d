import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import DocPrepareSignerList from '../DocPrepareSignerList';

// Mock dependencies
jest.mock('react-responsive', () => ({
  useMediaQuery: jest.fn(() => false)
}));

// Mock firestore services
jest.mock('../../../../../app/firestore/firestoreService', () => ({}));
jest.mock('../../../../../app/firestore/firebaseService', () => ({}));

// Mock the child component
jest.mock('../DocPrepareSignerListItem', () => {
  return function MockDocPrepareSignerListItem({ signer }) {
    return (
      <div data-testid={`signer-item-${signer.role}`}>
        {signer.role}: {signer.firstName} {signer.lastName}
      </div>
    );
  };
});

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockState = {
  annot: {
    signerListDisplay: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Buyer 2', firstName: 'Jane', lastName: 'Doe' }
    ],
    signerListPossible: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Buyer 2', firstName: 'Jane', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Bob', lastName: 'Smith' },
      { role: 'Seller 2', firstName: 'Alice', lastName: 'Smith' }
    ],
    editMode: 'client'
  },
  profile: {
    currentUserProfile: {
      firstName: 'Agent',
      lastName: 'User'
    }
  },
  transaction: {
    transaction: {
      agentRepresents: 'Buyer'
    }
  },
  doc: {
    doc: {
      annotsInProgress: [
        { signerRole: 'Seller', agentsField: false, type: 'signature' },
        { signerRole: 'Seller 2', agentsField: false, type: 'signature' }
      ]
    }
  }
};

describe('DocPrepareSignerList - Other Party Separation', () => {
  test('separates current agent clients from other party clients for buyer agent', () => {
    const store = createMockStore(mockState);
    
    render(
      <Provider store={store}>
        <DocPrepareSignerList />
      </Provider>
    );

    // Should show current agent's clients (buyers) normally
    expect(screen.getByTestId('signer-item-Buyer')).toBeInTheDocument();
    expect(screen.getByTestId('signer-item-Buyer 2')).toBeInTheDocument();
    
    // Should show other party title
    expect(screen.getByText('Seller Clients:')).toBeInTheDocument();

    // Should show other party clients (now interactive)
    expect(screen.getByTestId('signer-item-Seller')).toBeInTheDocument();
    expect(screen.getByTestId('signer-item-Seller 2')).toBeInTheDocument();
  });

  test('shows correct title for seller agent', () => {
    const sellerAgentState = {
      ...mockState,
      transaction: {
        transaction: {
          agentRepresents: 'Seller'
        }
      }
    };
    
    const store = createMockStore(sellerAgentState);
    
    render(
      <Provider store={store}>
        <DocPrepareSignerList />
      </Provider>
    );

    // Should show "Buyer Clients:" title for seller agent
    expect(screen.getByText('Buyer Clients:')).toBeInTheDocument();
  });

  test('does not show other party section when no other party clients have names', () => {
    const stateWithoutOtherPartyNames = {
      ...mockState,
      annot: {
        ...mockState.annot,
        signerListPossible: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
          { role: 'Buyer 2', firstName: 'Jane', lastName: 'Doe' },
          { role: 'Seller' }, // No first/last name
          { role: 'Seller 2' } // No first/last name
        ]
      }
    };
    
    const store = createMockStore(stateWithoutOtherPartyNames);
    
    render(
      <Provider store={store}>
        <DocPrepareSignerList />
      </Provider>
    );

    // Should not show other party section when no names are available
    expect(screen.queryByText('Seller Clients:')).not.toBeInTheDocument();
    expect(screen.queryByTestId('signer-item-Seller')).not.toBeInTheDocument();
  });

  test('shows Add Signer button after other party clients', () => {
    const store = createMockStore(mockState);

    render(
      <Provider store={store}>
        <DocPrepareSignerList />
      </Provider>
    );

    const addSignerButton = screen.getByText('Add Signer');
    expect(addSignerButton).toBeInTheDocument();

    // Add Signer button should appear after other party clients
    const sellerClients = screen.getByText('Seller Clients:');
    const sellerItem = screen.getByTestId('signer-item-Seller');
    expect(sellerClients).toBeInTheDocument();
    expect(sellerItem).toBeInTheDocument();
  });

  test('does not show other party section when no annotsInProgress for other side', () => {
    const stateWithoutOtherSideAnnots = {
      ...mockState,
      doc: {
        doc: {
          annotsInProgress: [
            { signerRole: 'Buyer', agentsField: false, type: 'signature' },
            { signerRole: 'Buyer 2', agentsField: false, type: 'signature' }
          ]
        }
      }
    };

    const store = createMockStore(stateWithoutOtherSideAnnots);

    render(
      <Provider store={store}>
        <DocPrepareSignerList />
      </Provider>
    );

    // Should not show other party section when no annotsInProgress for other side
    expect(screen.queryByText('Seller Clients:')).not.toBeInTheDocument();
    expect(screen.queryByTestId('signer-item-Seller')).not.toBeInTheDocument();
    expect(screen.queryByTestId('signer-item-Seller 2')).not.toBeInTheDocument();
  });
});
