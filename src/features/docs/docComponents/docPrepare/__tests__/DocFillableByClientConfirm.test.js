import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { createStore } from 'redux';
import DocFillableByClientConfirm from '../DocFillableByClientConfirm';
import * as firestoreService from '../../../../../app/firestore/firestoreService';

// Mock the firestore service
jest.mock('../../../../../app/firestore/firestoreService', () => ({
  updateDocInDb: jest.fn(() => Promise.resolve()),
  updateDocAddSharingInDb: jest.fn(() => Promise.resolve()),
  updateTransAddSharingInDb: jest.fn(() => Promise.resolve()),
  sendDocSharingEmail: jest.fn(() => Promise.resolve()),
  addHistoryToDb: jest.fn(() => Promise.resolve()),
}));

// Mock react-responsive
jest.mock('react-responsive', () => ({
  useMediaQuery: jest.fn(() => false),
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Create a mock store
const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockProps = {
  doc: {
    id: 'test-doc-id',
    transactionId: 'test-transaction-id',
    name: 'Test Document.pdf',
  },
  transaction: {
    id: 'test-transaction-id',
    agentProfile: {
      firstName: 'Agent',
      lastName: 'Smith',
    },
  },
  allParties: [
    {
      id: 'buyer-1',
      role: 'Buyer',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    {
      id: 'seller-1',
      role: 'Seller',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
    },
  ],
  annots: [],
  signerListDisplay: [],
  selectedSigner: null,
};

const mockState = {
  profile: {
    currentUserProfile: {
      id: 'agent-1',
      firstName: 'Agent',
      lastName: 'Smith',
    },
  },
  modals: {
    modalType: 'DocFillableByClientConfirm',
  },
};

const renderWithProviders = (component, state = mockState) => {
  const store = createMockStore(state);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('DocFillableByClientConfirm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders modal with correct content', () => {
    renderWithProviders(<DocFillableByClientConfirm {...mockProps} />);
    
    expect(screen.getByText('Client Fillable Form')).toBeInTheDocument();
    expect(screen.getByText(/This form is designed to be filled out by the client first/)).toBeInTheDocument();
    expect(screen.getByText('Let Clients Fill Out')).toBeInTheDocument();
    expect(screen.getByText('Send for Signatures Now')).toBeInTheDocument();
  });

  test('displays client list', () => {
    renderWithProviders(<DocFillableByClientConfirm {...mockProps} />);
    
    expect(screen.getByText('John Doe (Buyer) - <EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith (Seller) - <EMAIL>')).toBeInTheDocument();
  });

  test('shares document with clients when "Let Clients Fill Out" is clicked', async () => {
    const store = createMockStore(mockState);
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    
    render(
      <Provider store={store}>
        <BrowserRouter>
          <DocFillableByClientConfirm {...mockProps} />
        </BrowserRouter>
      </Provider>
    );
    
    const letClientsFillButton = screen.getByText('Let Clients Fill Out');
    fireEvent.click(letClientsFillButton);
    
    await waitFor(() => {
      // Check that sharing functions were called for each client
      expect(firestoreService.updateDocAddSharingInDb).toHaveBeenCalledTimes(2);
      expect(firestoreService.updateTransAddSharingInDb).toHaveBeenCalledTimes(2);
      expect(firestoreService.sendDocSharingEmail).toHaveBeenCalledTimes(2);
      expect(firestoreService.addHistoryToDb).toHaveBeenCalledTimes(2);
    });
    
    // Check that modal is closed
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/closeModal',
        payload: expect.objectContaining({
          modalType: 'DocFillableByClientConfirm',
        }),
      })
    );
    
    // Check that navigation occurred
    expect(mockNavigate).toHaveBeenCalledWith('/transactions/test-transaction-id/documents');
  });

  test('proceeds to SendForSigning when "Send for Signatures Now" is clicked', async () => {
    const store = createMockStore(mockState);
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    
    render(
      <Provider store={store}>
        <BrowserRouter>
          <DocFillableByClientConfirm {...mockProps} />
        </BrowserRouter>
      </Provider>
    );
    
    const sendForSigningButton = screen.getByText('Send for Signatures Now');
    fireEvent.click(sendForSigningButton);
    
    await waitFor(() => {
      // Check that document is updated
      expect(firestoreService.updateDocInDb).toHaveBeenCalledWith(
        'test-doc-id',
        expect.objectContaining({
          annotsInProgress: [],
          signerListInProgress: [],
          selectedSignerInProgress: null,
        })
      );
    });
    
    // Check that modals are handled correctly
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/closeModal',
        payload: expect.objectContaining({
          modalType: 'DocFillableByClientConfirm',
        }),
      })
    );
    
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/openModal',
        payload: expect.objectContaining({
          modalType: 'SendForSigning',
        }),
      })
    );
  });
});
