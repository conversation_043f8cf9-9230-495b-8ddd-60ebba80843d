import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import DocPrepareDashboard from '../DocPrepareDashboard';

// Mock all the complex dependencies
jest.mock('react-pdf/dist/umd/entry.webpack', () => ({
  Document: ({ children }) => <div data-testid="pdf-document">{children}</div>,
  Page: ({ pageNumber }) => <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
}));

jest.mock('react-rnd', () => ({
  Rnd: ({ children, ...props }) => <div data-testid="rnd-component" {...props}>{children}</div>
}));

jest.mock('../annots/AnnotDraggableField', () => {
  return function MockAnnotDraggableField({ annot }) {
    return <div data-testid={`draggable-field-${annot.uniqueId}`}>Draggable: {annot.type}</div>;
  };
});

jest.mock('../annots/AnnotOtherPartyField', () => {
  return function MockAnnotOtherPartyField({ annot }) {
    return <div data-testid={`other-party-field-${annot.uniqueId}`}>Other Party: {annot.signerRole} - {annot.type}</div>;
  };
});

jest.mock('../formFields/FormFieldSelector', () => {
  return function MockFormFieldSelector() {
    return <div data-testid="form-field-selector">Form Field</div>;
  };
});

jest.mock('../DocPrepareActionButtons', () => {
  return function MockDocPrepareActionButtons() {
    return <div data-testid="action-buttons">Action Buttons</div>;
  };
});

jest.mock('../signerList/DocPrepareSignerList', () => {
  return function MockDocPrepareSignerList() {
    return <div data-testid="signer-list">Signer List</div>;
  };
});

jest.mock('../annots/AnnotTypeMenu', () => {
  return function MockAnnotTypeMenu() {
    return <div data-testid="annot-type-menu">Annot Type Menu</div>;
  };
});

jest.mock('../annots/AnnotEditButtons', () => {
  return function MockAnnotEditButtons() {
    return <div data-testid="annot-edit-buttons">Edit Buttons</div>;
  };
});

// Mock firestore service
jest.mock('../../../../../app/firestore/firestoreService', () => ({
  updateDocInDb: jest.fn()
}));

// Mock util functions
jest.mock('../../../../../app/common/util/util', () => ({
  createSuggestedAnnots: jest.fn(),
  createAllSuggestedAnnots: jest.fn(),
  getAndSavePdfDimensions: jest.fn(),
  getFormFieldValues: jest.fn(() => [])
}));

// Mock annotUtils functions
jest.mock('../annots/annotUtils', () => ({
  createSigningParties: jest.fn(),
  createSignerListFromAnnots: jest.fn(),
  addAnnotToCanvas: jest.fn()
}));

// Mock axios
jest.mock('axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: { ip: '127.0.0.1' } }))
}));

// Mock React PDF
jest.mock('react-pdf/dist/umd/entry.webpack', () => ({
  Document: ({ children }) => <div data-testid="pdf-document">{children}</div>,
  Page: ({ children }) => <div data-testid="pdf-page">{children}</div>
}));

// Mock other components
jest.mock('../annots/AnnotTypeMenu', () => () => <div data-testid="annot-type-menu" />);
jest.mock('../DocPrepareActionButtons', () => () => <div data-testid="action-buttons" />);
jest.mock('../signerList/DocPrepareSignerList', () => () => <div data-testid="signer-list" />);
jest.mock('../annots/AnnotEditButtons', () => () => <div data-testid="edit-buttons" />);
jest.mock('../annots/AnnotDraggableField', () => () => <div data-testid="draggable-field" />);
jest.mock('../formFields/FormFieldSelector', () => () => <div data-testid="form-field-selector" />);
jest.mock('../../../../../app/layout/LoadingComponent', () => () => <div data-testid="loading" />);

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockState = {
  doc: {
    doc: {
      id: 'test-doc',
      docUrl: 'test-url',
      pdfBurnVersion: false,
      annotsInProgress: [
        {
          uniqueId: 'annot-1',
          page: 0,
          type: 'signature',
          signerRole: 'Buyer',
          agentsField: false,
          x: 100,
          y: 200,
          width: 150,
          height: 30
        },
        {
          uniqueId: 'annot-2',
          page: 0,
          type: 'signature',
          signerRole: 'Seller',
          agentsField: false,
          x: 300,
          y: 400,
          width: 150,
          height: 30
        }
      ]
    },
    docUrl: 'test-url'
  },
  annot: {
    annots: [
      {
        uniqueId: 'annot-1',
        page: 0,
        type: 'signature',
        signerRole: 'Buyer',
        agentsField: false,
        x: 100,
        y: 200,
        width: 150,
        height: 30
      },
      {
        uniqueId: 'annot-2',
        page: 0,
        type: 'signature',
        signerRole: 'Seller',
        agentsField: false,
        x: 300,
        y: 400,
        width: 150,
        height: 30
      }
    ],
    selectedAnnot: {},
    signerListDisplay: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ],
    editMode: 'client',
    pageScalePrepare: 1
  },
  transaction: {
    transaction: {
      agentRepresents: 'Buyer'
    },
    transClients: [{ role: 'Buyer', firstName: 'John', lastName: 'Doe' }],
    parties: [
      { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
      { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
    ]
  },
  profile: {
    currentUserProfile: {
      firstName: 'Test',
      lastName: 'User'
    }
  }
};

describe('DocPrepareDashboard - Other Party Fields', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Set up default mocks
    const { createSigningParties, createSignerListFromAnnots } = require('../annots/annotUtils');
    createSigningParties.mockReturnValue({
      possibleSigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 },
        { role: 'Seller', firstName: 'Jane', lastName: 'Smith', index: 1 }
      ],
      displaySigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 }
      ]
    });
    createSignerListFromAnnots.mockReturnValue(['Buyer', 'Seller']);
  });

  test('renders signature fields for both parties when other party is in signer list', () => {
    const store = createMockStore(mockState);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for buyer (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();

    // Should render draggable field for seller (other party client in signer list)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();
  });

  test('renders signature fields for both parties when agent represents seller', () => {
    const sellerAgentState = {
      ...mockState,
      transaction: {
        ...mockState.transaction,
        transaction: {
          agentRepresents: 'Seller'
        }
      }
    };

    const store = createMockStore(sellerAgentState);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for seller (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();

    // Should render draggable field for buyer (other party client in signer list)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();
  });

  test('does not render other party fields when they are not in signer list', () => {
    const stateWithoutOtherPartyInSignerList = {
      ...mockState,
      annot: {
        ...mockState.annot,
        signerListDisplay: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Seller not in signer list
        ]
      }
    };

    const store = createMockStore(stateWithoutOtherPartyInSignerList);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Should render draggable field for buyer (current agent's client)
    expect(screen.getByTestId('draggable-field-annot-1')).toBeInTheDocument();

    // Should still render seller annotation in the second section (other party not in signer list)
    expect(screen.getByTestId('draggable-field-annot-2')).toBeInTheDocument();
  });

  test('adds annotations only for newly added parties while preserving existing annotations', async () => {
    const { updateDocInDb } = require('../../../../../app/firestore/firestoreService');

    const stateWithExistingAnnots = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgressSuggestedAdded: true, // Already processed initial annotations
          annotsInProgressSuggested: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
            { signerRole: 'Seller 2', type: 'signature', page: 0, x: 500, y: 600 } // New party template
          ],
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 150, y: 250 }, // User moved this
            { signerRole: 'Seller', type: 'signature', page: 0, x: 350, y: 450 }  // User moved this
            // Seller 2 doesn't exist yet (newly added party)
          ]
        },
        docUrl: 'test-url'
      },
      transaction: {
        ...mockState.transaction,
        parties: [
          ...mockState.transaction.parties,
          { role: 'Seller 2', firstName: 'New', lastName: 'Seller' } // Newly added party
        ]
      }
    };

    const store = createMockStore(stateWithExistingAnnots);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Wait for the effect to run
    await waitFor(() => {
      expect(updateDocInDb).toHaveBeenCalledWith(
        'test-doc',
        {
          annotsInProgress: expect.arrayContaining([
            // Should preserve existing annotations with user's modified positions
            expect.objectContaining({ signerRole: 'Buyer', x: 150, y: 250 }),
            expect.objectContaining({ signerRole: 'Seller', x: 350, y: 450 }),
            // Should add new annotation for newly added party with template position
            expect.objectContaining({ signerRole: 'Seller 2', x: 500, y: 600 })
          ])
        },
        false
      );
    });
  });

  test('automatically adds parties with annotations to signer list', () => {
    const { createSigningParties, createSignerListFromAnnots } = require('../annots/annotUtils');

    // Mock the functions
    createSigningParties.mockReturnValue({
      possibleSigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 },
        { role: 'Seller', firstName: 'Jane', lastName: 'Smith', index: 1 },
        { role: 'Title Company', firstName: 'Title', lastName: 'Corp', index: 2 }
      ],
      displaySigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 } // Only buyer by default
      ]
    });

    createSignerListFromAnnots.mockReturnValue(['Buyer', 'Seller', 'Title Company']);

    const stateWithAnnotations = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
            { signerRole: 'Title Company', type: 'signature', page: 0, x: 500, y: 600 }
          ]
        },
        docUrl: 'test-url'
      }
    };

    const store = createMockStore(stateWithAnnotations);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Verify that setSignerListDisplay was called with all parties that have annotations
    const actions = store.getActions();
    const setSignerListDisplayAction = actions.find(action => action.type === 'annot/setSignerListDisplay');

    expect(setSignerListDisplayAction).toBeDefined();
    expect(setSignerListDisplayAction.payload).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ role: 'Buyer' }),
        expect.objectContaining({ role: 'Seller' }),
        expect.objectContaining({ role: 'Title Company' })
      ])
    );
  });

  test('includes Title Company in signer list when they have annotations', () => {
    const { createSigningParties, createSignerListFromAnnots } = require('../annots/annotUtils');

    // Mock the functions
    createSigningParties.mockReturnValue({
      possibleSigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 },
        { role: 'Title Company', firstName: 'Title', lastName: 'Corp', index: 1 }
      ],
      displaySigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 }
      ]
    });

    createSignerListFromAnnots.mockReturnValue(['Buyer', 'Title Company']);

    const stateWithTitleAnnotations = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Title Company', type: 'signature', page: 0, x: 300, y: 400 }
          ]
        },
        docUrl: 'test-url'
      }
    };

    const store = createMockStore(stateWithTitleAnnotations);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Verify that Title Company is included in the signer list
    const actions = store.getActions();
    const setSignerListDisplayAction = actions.find(action => action.type === 'annot/setSignerListDisplay');

    expect(setSignerListDisplayAction).toBeDefined();
    expect(setSignerListDisplayAction.payload).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ role: 'Title Company' })
      ])
    );
  });

  test('removes annotations for deleted parties', async () => {
    const { updateDocInDb } = require('../../../../../app/firestore/firestoreService');

    const stateWithDeletedParty = {
      ...mockState,
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgressSuggestedAdded: true,
          annotsInProgressSuggested: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
            { signerRole: 'Seller 2', type: 'signature', page: 0, x: 500, y: 600 }
          ],
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 },
            { signerRole: 'Seller 2', type: 'signature', page: 0, x: 500, y: 600 } // This party will be deleted
          ]
        },
        docUrl: 'test-url'
      },
      transaction: {
        ...mockState.transaction,
        parties: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
          { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
          // Seller 2 has been removed from parties
        ]
      }
    };

    const store = createMockStore(stateWithDeletedParty);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Wait for the effect to run
    await waitFor(() => {
      expect(updateDocInDb).toHaveBeenCalledWith(
        'test-doc',
        {
          annotsInProgress: expect.not.arrayContaining([
            expect.objectContaining({ signerRole: 'Seller 2' })
          ])
        },
        false
      );
    });

    // Verify that other annotations are preserved
    await waitFor(() => {
      expect(updateDocInDb).toHaveBeenCalledWith(
        'test-doc',
        {
          annotsInProgress: expect.arrayContaining([
            expect.objectContaining({ signerRole: 'Buyer' }),
            expect.objectContaining({ signerRole: 'Seller' })
          ])
        },
        false
      );
    });
  });

  test('removes deleted clients from signer list', () => {
    const { createSigningParties, createSignerListFromAnnots } = require('../annots/annotUtils');

    // Mock the functions - simulate Buyer 2 being removed
    createSigningParties.mockReturnValue({
      possibleSigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 },
        { role: 'Buyer 2', firstName: '', lastName: '', index: 1 }, // No names = deleted
        { role: 'Seller', firstName: 'Jane', lastName: 'Smith', index: 2 }
      ],
      displaySigners: [
        { role: 'Buyer', firstName: 'John', lastName: 'Doe', index: 0 },
        { role: 'Buyer 2', firstName: '', lastName: '', index: 1 } // Would normally be included
      ]
    });

    createSignerListFromAnnots.mockReturnValue(['Buyer', 'Seller']);

    const stateWithDeletedClient = {
      ...mockState,
      transaction: {
        ...mockState.transaction,
        transClients: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Buyer 2 has been removed (no firstName/lastName)
        ]
      },
      doc: {
        doc: {
          id: 'test-doc',
          docUrl: 'test-url',
          pdfBurnVersion: false,
          annotsInProgress: [
            { signerRole: 'Buyer', type: 'signature', page: 0, x: 100, y: 200 },
            { signerRole: 'Seller', type: 'signature', page: 0, x: 300, y: 400 }
            // Buyer 2 annotations have been removed
          ]
        },
        docUrl: 'test-url'
      }
    };

    const store = createMockStore(stateWithDeletedClient);

    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Verify that setSignerListDisplay was called without Buyer 2
    const actions = store.getActions();
    const setSignerListDisplayAction = actions.find(action => action.type === 'annot/setSignerListDisplay');

    expect(setSignerListDisplayAction).toBeDefined();
    expect(setSignerListDisplayAction.payload).toEqual(
      expect.not.arrayContaining([
        expect.objectContaining({ role: 'Buyer 2' })
      ])
    );

    // But should still include Buyer and Seller
    expect(setSignerListDisplayAction.payload).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ role: 'Buyer' }),
        expect.objectContaining({ role: 'Seller' })
      ])
    );
  });
});
