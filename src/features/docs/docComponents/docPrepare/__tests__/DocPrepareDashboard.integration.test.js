import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import '@testing-library/jest-dom';
import { DocPrepareDashboard } from '../DocPrepareDashboard';

// Mock all the complex dependencies
jest.mock('react-pdf/dist/umd/entry.webpack', () => ({
  Document: ({ children, onLoadSuccess }) => {
    // Simulate PDF loading
    React.useEffect(() => {
      if (onLoadSuccess) {
        onLoadSuccess({ numPages: 1 });
      }
    }, [onLoadSuccess]);
    return <div data-testid="pdf-document">{children}</div>;
  },
  Page: ({ pageNumber }) => <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
}));

jest.mock('../annots/AnnotDraggableField', () => {
  return function MockAnnotDraggableField({ annot }) {
    return (
      <div data-testid={`draggable-field-${annot.uniqueId}`} data-signer-role={annot.signerRole}>
        {annot.signerRole} - {annot.type}
      </div>
    );
  };
});

// Mock other components
jest.mock('../formFields/FormFieldSelector', () => () => null);
jest.mock('../DocPrepareActionButtons', () => () => null);
jest.mock('../signerList/DocPrepareSignerList', () => () => null);
jest.mock('../annots/AnnotTypeMenu', () => () => null);
jest.mock('../annots/AnnotEditButtons', () => () => null);
jest.mock('../docView/DocViewAgentAnnotField', () => () => null);
jest.mock('../docView/DocViewSignedAnnotField', () => () => null);

// Mock services and utilities
jest.mock('../../../../../app/firestore/firestoreService', () => ({
  updateDocInDb: jest.fn()
}));

jest.mock('../../../../../app/common/util/util', () => ({
  createSuggestedAnnots: jest.fn(() => []),
  getAndSavePdfDimensions: jest.fn((doc, pdfObject, setNumPages, setPageDimensions) => {
    setNumPages(1);
    setPageDimensions({ width: 612, height: 792 });
  }),
  getFormFieldValues: jest.fn((doc, transaction, setFormFieldValues) => {
    setFormFieldValues([]);
  })
}));

const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

describe('DocPrepareDashboard - Other Party Integration', () => {
  test('displays annotations for other party clients when they are in signer list', () => {
    const mockState = {
      doc: {
        doc: {
          id: 'test-doc',
          dimensions: { width: 612, height: 792 },
          annotsInProgressSuggestedAdded: true
        },
        docUrl: 'test-url'
      },
      annot: {
        annots: [
          {
            uniqueId: 'buyer-annot',
            page: 0,
            type: 'signature',
            signerRole: 'Buyer',
            agentsField: false
          },
          {
            uniqueId: 'seller-annot',
            page: 0,
            type: 'signature',
            signerRole: 'Seller',
            agentsField: false
          }
        ],
        pageScalePrepare: 1,
        activeAnnotType: 'signature',
        selectedSigner: { role: 'Buyer' },
        editMode: 'signer',
        signerListDisplay: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
          { role: 'Seller', firstName: 'Jane', lastName: 'Smith' } // Other party in signer list
        ]
      },
      transaction: {
        transaction: { agentRepresents: 'Buyer' },
        transClients: [{ role: 'Buyer', firstName: 'John', lastName: 'Doe' }],
        parties: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' },
          { role: 'Seller', firstName: 'Jane', lastName: 'Smith' }
        ]
      },
      profile: {
        currentUserProfile: { firstName: 'Agent', lastName: 'User' }
      }
    };

    const store = createMockStore(mockState);
    
    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Both buyer and seller annotations should be displayed as draggable fields
    expect(screen.getByTestId('draggable-field-buyer-annot')).toBeInTheDocument();
    expect(screen.getByTestId('draggable-field-seller-annot')).toBeInTheDocument();
    
    // Verify the content shows the correct roles
    expect(screen.getByText('Buyer - signature')).toBeInTheDocument();
    expect(screen.getByText('Seller - signature')).toBeInTheDocument();
  });

  test('displays annotations correctly when other party is not in signer list', () => {
    const mockState = {
      doc: {
        doc: {
          id: 'test-doc',
          dimensions: { width: 612, height: 792 },
          annotsInProgressSuggestedAdded: true
        },
        docUrl: 'test-url'
      },
      annot: {
        annots: [
          {
            uniqueId: 'buyer-annot',
            page: 0,
            type: 'signature',
            signerRole: 'Buyer',
            agentsField: false
          },
          {
            uniqueId: 'seller-annot',
            page: 0,
            type: 'signature',
            signerRole: 'Seller',
            agentsField: false
          }
        ],
        pageScalePrepare: 1,
        activeAnnotType: 'signature',
        selectedSigner: { role: 'Buyer' },
        editMode: 'signer',
        signerListDisplay: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Seller NOT in signer list
        ]
      },
      transaction: {
        transaction: { agentRepresents: 'Buyer' },
        transClients: [{ role: 'Buyer', firstName: 'John', lastName: 'Doe' }],
        parties: [
          { role: 'Buyer', firstName: 'John', lastName: 'Doe' }
          // Seller not in parties or without name
        ]
      },
      profile: {
        currentUserProfile: { firstName: 'Agent', lastName: 'User' }
      }
    };

    const store = createMockStore(mockState);
    
    render(
      <Provider store={store}>
        <DocPrepareDashboard />
      </Provider>
    );

    // Buyer annotation should be displayed
    expect(screen.getByTestId('draggable-field-buyer-annot')).toBeInTheDocument();
    
    // Seller annotation should still be displayed (in the second section for other party not in signer list)
    expect(screen.getByTestId('draggable-field-seller-annot')).toBeInTheDocument();
  });
});
