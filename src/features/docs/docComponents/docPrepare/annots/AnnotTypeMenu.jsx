import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Icon, Menu } from "semantic-ui-react";
import { changeActiveAnnotType } from "../../../../../app/annots/annotSlice";
import { useMediaQuery } from "react-responsive";

export default function AnnotTypeMenu() {
  const dispatch = useDispatch();
  const { activeAnnotType } = useSelector((state) => state.annot);
  const isMobile = useMediaQuery({ query: "(max-width:770px)" });
    const isMobileSig = useMediaQuery({ query: "(max-width:650px)" });


  return (
    <>
      <Menu
        widths={6}
        style={{
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          maxWidth: "720px",
        }}
      >
        <Menu.Item
          draggable
          name="signature"
          active={activeAnnotType === "signature"}
          onDragStart={() => dispatch(changeActiveAnnotType("signature"))}
          onClick={() => dispatch(changeActiveAnnotType("signature"))}
          style={{
            cursor: "pointer",
          }}
        >
          <Icon name="pencil" />
          {!isMobileSig ? <>Signature</> : <>Sig</>}
        </Menu.Item>
        <Menu.Item
          draggable
          onDragStart={() => dispatch(changeActiveAnnotType("initials"))}
          onClick={() => dispatch(changeActiveAnnotType("initials"))}
          name="initials"
          active={activeAnnotType === "initials"}
          style={{
            cursor: "pointer",
          }}
        >
          <Icon name="pencil square" />
          {!isMobileSig ? <>Initials</> : <>Initial</>}
        </Menu.Item>
        <Menu.Item
          draggable
          name="date"
          active={activeAnnotType === "date"}
          onDragStart={() => dispatch(changeActiveAnnotType("date"))}
          onClick={() => dispatch(changeActiveAnnotType("date"))}
          style={{
            cursor: "pointer",
          }}
        >
          <Icon name="calendar outline" />
          Date {!isMobileSig && <>Signed</>}
        </Menu.Item>
        <Menu.Item
          draggable
          name="text"
          active={activeAnnotType === "text"}
          onDragStart={() => dispatch(changeActiveAnnotType("text"))}
          onClick={() => dispatch(changeActiveAnnotType("text"))}
          style={{
            cursor: "pointer",
          }}
        >
          <Icon name="font" />
          Text
        </Menu.Item>
        {!isMobileSig && (
          <Menu.Item
            draggable
            name="strikethrough"
            active={activeAnnotType === "strikethrough"}
            onDragStart={() => dispatch(changeActiveAnnotType("strikethrough"))}
            style={{
              cursor: "pointer",
            }}
            onClick={() => dispatch(changeActiveAnnotType("strikethrough"))}
          >
            <Icon name="strikethrough" />
            {!isMobile ? <>Strikethrough</> : <>Strike</>}
          </Menu.Item>
        )}
        <Menu.Item
          draggable
          name="checkbox"
          active={activeAnnotType === "checkbox"}
          onDragStart={() => dispatch(changeActiveAnnotType("checkbox"))}
          onClick={() => dispatch(changeActiveAnnotType("checkbox"))}
          style={{
            cursor: "pointer",
          }}
        >
          <Icon name="check square outline" />
          {!isMobileSig && <>Checkbox</>}
        </Menu.Item>
      </Menu>
    </>
  );
}
