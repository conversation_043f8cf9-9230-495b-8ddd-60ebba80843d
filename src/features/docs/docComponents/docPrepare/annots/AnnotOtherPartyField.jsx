import React, { useEffect, useRef, useState } from "react";
import { Rnd } from "react-rnd";
import { Icon } from "semantic-ui-react";
import { createAnnotColor } from "./annotUtils";

export default function AnnotOtherPartyField({ annot, pageScale, transaction }) {
  const ref = useRef(null);
  const [annotPosition] = useState({
    x: annot.x,
    y: annot.y,
  });
  const [annotPositionDisplay, setAnnotPositionDisplay] = useState({
    x: annot.x,
    y: annot.y,
  });

  useEffect(() => {
    const newAnnotPosition = {
      x: annotPosition.x * pageScale,
      y: annotPosition.y * pageScale,
    };
    setAnnotPositionDisplay(newAnnotPosition);
  }, [pageScale, annotPosition.x, annotPosition.y]);

  const color = createAnnotColor(annot.signerRole, transaction);

  function convertTypeToPlaceholder(annotType) {
    switch (annotType) {
      case "signature":
        return "Signature";
      case "initials":
        return "Initials";
      case "date":
        return "mm/dd/yyyy";
      case "text":
        return "Sample text";
      case "checkbox":
        return "";
      default:
        return "";
    }
  }

  return (
    <Rnd
            key={annot.uniqueId}
            width={annot.width * pageScale}
            height={annot.height * pageScale}
            bounds={`#canvas-${annot.page.toString()}`}
            disableDragging={true}
            enableResizing={false}
            position={{
              x: annotPositionDisplay.x,
              y: annotPositionDisplay.y,
            }}
          >
            <div
              ref={ref}
              id={annot.uniqueId}
              style={{
                width: annot.width * pageScale,
                height: annot.height * pageScale,
                minWidth: 6 * pageScale,
                minHeight: 2 * pageScale,
                maxWidth: 960 * pageScale,
                maxHeight: 960 * pageScale,
                position: "absolute",
                display: "inline-block",
                cursor: "default",
                zIndex: 1, // Lower z-index than draggable fields
                lineHeight: 0.6,
                whiteSpace: "nowrap",
                opacity: 0.6, // Make it more subtle
              }}
            >
              <div
                className="annot-outer-wrapper"
                style={{ 
                  backgroundColor: `rgba(${color}, 0.1)`, // More subtle background
                  border: `1px dashed rgba(${color}, 0.5)` // Dashed border to differentiate
                }}
              >
                <div
                  className="annot-inner-wrapper"
                  style={{
                    boxShadow: `rgba(${color}, 0.3) 0px 0px 0px 1px`, // More subtle shadow
                    zIndex: 5,
                  }}
                >
                  {annot.agentsField === false && (
                    <span
                      style={{
                        fontSize: `${annot.fontSize * pageScale}px`,
                        color: `rgba(${color}, 0.7)`, // More subtle text color
                        fontFamily: `${annot.fontFamily}`,
                        fontStyle: 'italic', // Italicize to show it's informational
                      }}
                    >
                      {convertTypeToPlaceholder(annot.type)}
                    </span>
                  )}
                  {annot.agentsField === false && annot.type === "checkbox" && (
                    <Icon
                      name="square outline"
                      style={{
                        fontSize: `${annot.fontSize * pageScale}px`,
                        color: `rgba(${color}, 0.7)`,
                        textAlign: "left",
                        height: "unset",
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </Rnd>
  );
}
