import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnnotOtherPartyField from '../AnnotOtherPartyField';

// Mock react-rnd
jest.mock('react-rnd', () => ({
  Rnd: ({ children, ...props }) => <div data-testid="rnd-component" {...props}>{children}</div>
}));

describe('AnnotOtherPartyField', () => {
  const mockAnnot = {
    uniqueId: 'test-annot-1',
    x: 100,
    y: 200,
    width: 150,
    height: 30,
    page: 0,
    type: 'signature',
    signerRole: 'Seller',
    agentsField: false,
    fontSize: 12,
    fontFamily: 'Arial'
  };

  const mockTransaction = {
    agentRepresents: 'Buyer'
  };

  const mockPageScale = 1;

  test('renders signature field with correct styling', () => {
    render(
      <AnnotOtherPartyField 
        annot={mockAnnot}
        pageScale={mockPageScale}
        transaction={mockTransaction}
      />
    );

    const rndComponent = screen.getByTestId('rnd-component');
    expect(rndComponent).toBeInTheDocument();
    
    // Check that the signature placeholder text is rendered
    expect(screen.getByText('Signature')).toBeInTheDocument();
  });

  test('renders initials field correctly', () => {
    const initialsAnnot = { ...mockAnnot, type: 'initials' };
    
    render(
      <AnnotOtherPartyField 
        annot={initialsAnnot}
        pageScale={mockPageScale}
        transaction={mockTransaction}
      />
    );

    expect(screen.getByText('Initials')).toBeInTheDocument();
  });

  test('renders date field correctly', () => {
    const dateAnnot = { ...mockAnnot, type: 'date' };
    
    render(
      <AnnotOtherPartyField 
        annot={dateAnnot}
        pageScale={mockPageScale}
        transaction={mockTransaction}
      />
    );

    expect(screen.getByText('mm/dd/yyyy')).toBeInTheDocument();
  });

  test('renders checkbox field correctly', () => {
    const checkboxAnnot = { ...mockAnnot, type: 'checkbox' };
    
    render(
      <AnnotOtherPartyField 
        annot={checkboxAnnot}
        pageScale={mockPageScale}
        transaction={mockTransaction}
      />
    );

    // Should render a checkbox icon
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
  });

  test('applies correct positioning and scaling', () => {
    const pageScale = 1.5;
    
    render(
      <AnnotOtherPartyField 
        annot={mockAnnot}
        pageScale={pageScale}
        transaction={mockTransaction}
      />
    );

    const rndComponent = screen.getByTestId('rnd-component');
    
    // Check that width and height are scaled
    expect(rndComponent).toHaveAttribute('width', String(mockAnnot.width * pageScale));
    expect(rndComponent).toHaveAttribute('height', String(mockAnnot.height * pageScale));
  });

  test('has default cursor and lower z-index for non-interactive display', () => {
    render(
      <AnnotOtherPartyField
        annot={mockAnnot}
        pageScale={mockPageScale}
        transaction={mockTransaction}
      />
    );

    const annotDiv = screen.getByText('Signature').closest('div');
    expect(annotDiv).toHaveStyle('cursor: default');
    expect(annotDiv).toHaveStyle('z-index: 1');
  });
});
