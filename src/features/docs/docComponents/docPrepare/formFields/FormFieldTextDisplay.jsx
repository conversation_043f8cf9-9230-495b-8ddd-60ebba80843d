import React from "react";

export function FormFieldTextDisplay({ formField, pageScale }) {
  return (
    <div
      className="pdfFormFieldDisplay"
      style={{
        top: `${
          (formField.top + formField.height - formField.fontSize) * pageScale
        }px`,
        left: `${formField.left * pageScale}px`,
        height: `${formField.height * pageScale}px`,
        width: `${formField.width * pageScale}px`,
        fontSize: `${formField.fontSize * pageScale}px`,
      }}
    >
      <span
        className="textFieldDisplay"
        style={{
          fontFamily: "helvetica",
          fontSize: `${formField.fontSize * pageScale}px`,
          margin: "0px",
          textAlign: "left",
          pointerEvents: "none",
        }}
      >
        {formField.value}
      </span>
    </div>
  );
}
