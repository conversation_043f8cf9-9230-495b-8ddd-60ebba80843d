import React, { useEffect, useRef, useState } from "react";
import { Icon } from "semantic-ui-react";
import { format } from "date-fns";
import { Rnd } from "react-rnd";

export default function DocViewSignedAnnotField({ annot, pageScale }) {
  const ref = useRef();

  const [annotPosition] = useState({
    x: annot.x,
    y: annot.y,
  });
  const [annotPositionDisplay, setAnnotPositionDisplay] = useState({
    x: annot.x,
    y: annot.y,
  });

  useEffect(() => {
    const newAnnotPosition = {
      x: annotPosition.x * pageScale,
      y: annotPosition.y * pageScale,
    };
    setAnnotPositionDisplay(newAnnotPosition);
  }, [pageScale, annotPosition.x, annotPosition.y]);

  return (
    <Rnd
      key={annot.id}
      width={annot.width * pageScale}
      height={annot.height * pageScale}
      disableDragging={true}
      position={{
        x: annotPositionDisplay.x,
        y: annotPositionDisplay.y,
      }}
    >
      <div
        ref={ref}
        id={annot.uniqueId}
        style={{
          width: annot.width * pageScale,
          height: annot.height * pageScale,
          minWidth: 6 * pageScale,
          minHeight: 2 * pageScale,
          maxWidth: 960 * pageScale,
          maxHeight: 960 * pageScale,
          position: "absolute",
          display: "inline-block",
          zIndex: 2,
          // Needed to align text properly
          lineHeight: 0.6,
          whiteSpace: "nowrap",
        }}
      >
        <div className="annot-outer-wrapper">
          <div className="annot-inner-wrapper">
            {annot.type === "checkbox" && annot.text === "checked" && (
              <Icon
                name="check"
                style={{
                  fontSize: `${annot.fontSize * pageScale}px`,
                  color: `${annot.fontColor}`,
                  textAlign: "left",
                  height: "unset",
                }}
              />
            )}
            {annot.type !== "checkbox" && (
              <span
                style={{
                  fontSize: `${annot.fontSize * pageScale}px`,
                  color: `${annot.fontColor}`,
                  fontFamily: `${annot.signedFontFamily || annot.fontFamily}`,
                }}
              >
                {annot.type === "date" ? (
                  format(
                    annot.signedAt && annot.signedAt.toDate(),
                    "MM/dd/yyyy"
                  )
                ) : (
                  <>{annot.text}</>
                )}
              </span>
            )}
          </div>
        </div>
      </div>
    </Rnd>
  );
}
