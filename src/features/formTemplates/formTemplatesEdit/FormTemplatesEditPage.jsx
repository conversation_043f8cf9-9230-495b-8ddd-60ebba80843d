import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import { useLocation, useParams } from "react-router-dom";
import { clearAnnots, fetchAnnots } from "../../../app/annots/annotSlice";
import FormTemplatesFillDashboard from "../FormTemplatesDashboard";
import { getDocDownloadUrl } from "../../../app/firestore/firebaseService";
import { getDocAndUrl } from "../../docs/docSlice";

export default function FormTemplatesEditPage() {
  const dispatch = useDispatch();
  const { docsTrans, docsTransShared, doc, docUrl } = useSelector(
    (state) => state.doc
  );
  let { docId } = useParams();
  const location = useLocation();
  const lastPath = location.pathname.substring(
    location.pathname.lastIndexOf("/") + 1
  );

  useEffect(() => {
    if (docsTrans?.length > 0 || docsTransShared?.docs?.length > 0) {
      const allDocs = [...docsTrans, ...docsTransShared?.docs];
      const newDoc = allDocs.filter((doc) => doc.id === docId)[0];
      if (newDoc) {
        getDocDownloadUrl(newDoc.docRef).then((url) => {
          dispatch(getDocAndUrl({ doc: newDoc, docUrl: url }));
        });
      }
    }
  }, [doc.docRef, docsTrans, docsTransShared.docs, docId, dispatch]);

  useEffect(() => {
    if (doc.annotsInProgress) {
      dispatch(fetchAnnots(doc.annotsInProgress));
    } else {
      dispatch(clearAnnots());
    }
  }, [doc, dispatch]);

  // doc.id !== docId is for switching documents and making sure we
  // have new document loaded in store
  if (!doc.id || !doc.docRef || !lastPath || doc.id !== docId || !docUrl) {
    return <LoadingComponent />;
  }

  return <>{lastPath === "fill" && <FormTemplatesFillDashboard />}</>;
}
