import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { updateFormTemplateInDb } from "../../../../app/firestore/firestoreService";

export function FormTemplateFieldTextarea({
  formField,
  index,
  populatingFieldsStatus,
  newPopulatedFields,
}) {
  const { pageScaleFill } = useSelector((state) => state.annot);
  const { doc } = useSelector((state) => state.doc);
  const [value, setValue] = useState(
    (doc.formFieldValues && doc.formFieldValues[formField.name]) || ""
  );
  const pageScale = pageScaleFill;

  // This is a catch for prepopulating data to make sure it rerenders
  useEffect(() => {
    if (
      populatingFieldsStatus === "Complete" &&
      newPopulatedFields?.[formField.name]
    ) {
      setValue(newPopulatedFields[formField.name]);
    }
  }, [populatingFieldsStatus, formField.name, newPopulatedFields]);

  function handleChange(e) {
    setValue(e.target.value);
  }

  function handleBlur(e) {
    updateFormTemplateInDb(doc.id, {
      [`formFieldValues.${formField.name}`]: e.target.value,
    });
  }

  return (
    <div
      className="pdfFormField"
      style={{
        top: `${formField.top * pageScale}px`,
        left: `${formField.left * pageScale}px`,
        height: `${(formField.height - 5) * pageScale}px`,
        width: `${formField.width * pageScale}px`,
        fontSize: `${formField.fontSize * pageScale}px`,
      }}
    >
      <textarea
        className="fill-input-field"
        type={formField.type}
        name={formField.name}
        aria-label={formField.name}
        tabIndex={index}
        data-tabindex={index}
        autoComplete="off"
        value={value}
        onChange={(e) => handleChange(e)}
        onBlur={(e) => handleBlur(e)}
        style={{
          maxHeight: `${formField.height * pageScale}px`,
          borderWidth: "0px",
          fontFamily: "Helvetica",
          fontSize: `${formField.fontSize * pageScale}px`,
          margin: "0px",
          textAlign: "left",
          overFlow: "hidden",
          resize: "none",
          paddingTop: "5px",
          lineHeight: "1.1em",
          height: "100%",
        }}
      ></textarea>
    </div>
  );
}
