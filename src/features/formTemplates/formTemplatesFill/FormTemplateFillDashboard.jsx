import React, { useEffect, useRef, useState } from "react";
import { Document, Page } from "react-pdf/dist/umd/entry.webpack";
import { Grid } from "semantic-ui-react";
import { useSelector } from "react-redux";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import DocFillAgentAnnotField from "../../docs/docComponents/docFill/DocFillAgentAnnotField";
import FormTemplateFillActionButtons from "./FormTemplateFillActionButtons";
import { getFormFieldData } from "../../docs/docComponents/docFill/formFieldData/formFieldData";
import { FormTemplateFieldTextarea } from "./formTemplateFieldTypes/FormTemplateFieldTextarea";
import { FormTemplateFieldSignature } from "./formTemplateFieldTypes/FormTemplateFieldSignature";
import { FormTemplateFieldCheckbox } from "./formTemplateFieldTypes/FormTemplateFieldCheckbox";
import { FormTemplateFieldDate } from "./formTemplateFieldTypes/FormTemplateFieldDate";
import { FormTemplateFieldText } from "./formTemplateFieldTypes/FormTemplateFieldText";
import { pdfHasNonStandardDimensions } from "../../../app/common/util/util";

const options = {
  cMapUrl: "cmaps/",
  cMapPacked: true,
  standardFontDataUrl: "standard_fonts/",
};

export function FormTemplateFillDashboard() {
  const [numPages, setNumPages] = useState(null);
  const { agentAnnots, pageScaleFill } = useSelector((state) => state.annot);
  const { doc, docUrl } = useSelector((state) => state.doc);
  const { transaction } = useSelector((state) => state.transaction);
  const [formFieldData, setFormFieldData] = useState();
  const [pageDimensions, setPageDimensions] = useState(
    doc.dimensions || {
      width: 612,
      height: 792,
    }
  );
  const pageScale = pageScaleFill;
  const populatingFieldsStatus = null;
  const newPopulatedFields = {};

  useEffect(() => {
    const newFormFieldData = getFormFieldData(doc.title, transaction);

    if (newFormFieldData?.length > 0) {
      setFormFieldData(newFormFieldData);
    }
  }, [doc.title, transaction]);

  async function onDocumentLoadSuccess(pdfObject) {
    const page = await pdfObject.getPage(1);
    let docWidth = page.view?.[2] || 612;
    let docHeight = page.view?.[3] || 792;
    if (page.rotate && (page.rotate === 90 || page.rotate === 270)) {
      docWidth = page.view?.[3] || 792;
      docHeight = page.view?.[2] || 612;
    }
    if (pdfHasNonStandardDimensions(page)) {
      setPageDimensions({ width: docWidth, height: docHeight });
    }
    setNumPages(pdfObject.numPages);
  }

  function Canvas(props) {
    const canvasRef = useRef(null);
    useEffect(() => {
      const canvas = canvasRef.current;
      canvas.getContext("2d");
    }, []);
    return <canvas ref={canvasRef} {...props} />;
  }

  return (
    <>
      <div
        id="pdf-page"
        style={{
          position: "fixed",
          bottom: "80px",
          top: "75px",
          left: 0,
          right: 0,
          overflow: "auto",
          backgroundColor: "#f3f6f8",
        }}
      >
        <Grid className="zero horizontal margin">
          <Grid.Column width={16} className="zero top padding">
            <div>
              <Document
                file={docUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                loading={
                  <div style={{ height: window.innerHeight }}>
                    <LoadingComponent />
                  </div>
                }
                options={options}
              >
                {Array.from(new Array(numPages), (el, index) => (
                  <div
                    className="pdf-page-wrapper"
                    id={`page-${index}`}
                    key={`page-${index}`}
                  >
                    <div className="pdf-page-number">
                      Page {index + 1}/{numPages}
                    </div>
                    <div
                      className="pdf-page-container"
                      style={{
                        height: `${pageDimensions.height * pageScale}px`,
                        width: `${pageDimensions.width * pageScale}px`,
                      }}
                    >
                      <Page
                        scale={pageScale}
                        renderAnnotationLayer={false}
                        renderTextLayer={false}
                        key={`page_${index + 1}`}
                        pageNumber={index + 1}
                      />
                      <Canvas
                        className="pdf-canvas"
                        id={`canvas-${index}`}
                        style={{
                          width: `${pageDimensions.width * pageScale}px`,
                          height: `${pageDimensions.height * pageScale}px`,
                        }}
                      />
                      {formFieldData?.map((formField) => (
                        <React.Fragment key={formField.name}>
                          {formField.page === index && (
                            <>
                              {formField.type === "text" && (
                                <FormTemplateFieldText
                                  formField={formField}
                                  index={index}
                                  populatingFieldsStatus={
                                    populatingFieldsStatus
                                  }
                                  newPopulatedFields={newPopulatedFields}
                                />
                              )}
                              {formField.type === "date" && (
                                <FormTemplateFieldDate
                                  formField={formField}
                                  index={index}
                                  populatingFieldsStatus={
                                    populatingFieldsStatus
                                  }
                                  newPopulatedFields={newPopulatedFields}
                                />
                              )}
                              {formField.type === "checkbox" && (
                                <FormTemplateFieldCheckbox
                                  formField={formField}
                                  index={index}
                                  populatingFieldsStatus={
                                    populatingFieldsStatus
                                  }
                                  newPopulatedFields={newPopulatedFields}
                                />
                              )}
                              {formField.type === "signature" && (
                                <FormTemplateFieldSignature
                                  formField={formField}
                                  index={index}
                                  newPopulatedFields={newPopulatedFields}
                                />
                              )}
                              {formField.type === "textarea" && (
                                <FormTemplateFieldTextarea
                                  formField={formField}
                                  index={index}
                                  populatingFieldsStatus={
                                    populatingFieldsStatus
                                  }
                                  newPopulatedFields={newPopulatedFields}
                                />
                              )}
                            </>
                          )}
                        </React.Fragment>
                      ))}
                      {numPages &&
                        agentAnnots?.map((annot) => (
                          <React.Fragment key={annot.uniqueId}>
                            {annot.page === index && (
                              <DocFillAgentAnnotField annot={annot} />
                            )}
                          </React.Fragment>
                        ))}
                    </div>
                  </div>
                ))}
              </Document>
            </div>
          </Grid.Column>
        </Grid>
      </div>
      <div
        style={{
          position: "fixed",
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: "#f3f6f8",
        }}
      >
        <FormTemplateFillActionButtons />
      </div>
    </>
  );
}
