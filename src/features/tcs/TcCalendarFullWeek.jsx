import { addDays, format, startOfWeek, isSameDay } from "date-fns";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Grid, Card } from "semantic-ui-react";
import TaskActionButtons from "../tasks/taskComponents/TaskActionButtons";
import { useSelector } from "react-redux";

export function TcCalendarFullWeek({ tasksByDay }) {
  const navigate = useNavigate();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [filteredTasksByDay, setFilteredTasksByDay] = useState([
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
    {},
  ]);

  useEffect(() => {
    if (tasksByDay && currentUserProfile) {
      // Filter tasks based on user visibility
      const filteredDays = tasksByDay.map((day) => {
        const filteredDay = {};
        Object.keys(day).forEach((transactionId) => {
          const transaction = day[transactionId];
          if (transaction.tasks) {
            const visibleTasks = transaction.tasks.filter(
              (task) =>
                !task.visibleTo ||
                task.visibleTo.length === 0 ||
                task.visibleTo.includes(currentUserProfile.userId)
            );
            if (visibleTasks.length > 0) {
              filteredDay[transactionId] = {
                ...transaction,
                tasks: visibleTasks,
              };
            }
          }
        });
        return filteredDay;
      });
      setFilteredTasksByDay(filteredDays);
    }
  }, [tasksByDay, currentUserProfile]);

  function handleGoToTransaction(transactionId) {
    navigate(`/transactions/${transactionId}/overview`);
  }

  // Get the start of the current week (Sunday)
  const startOfCurrentWeek = startOfWeek(new Date(), { weekStartsOn: 0 });

  return (
    <div>
      {/* Current Week */}
      <div style={{ marginBottom: "20px" }}>
        <h4 style={{ marginBottom: "15px", color: "#666" }}>Current Week</h4>
        <Grid style={{ minHeight: "100px" }} stackable columns="equal">
          {filteredTasksByDay.slice(0, 7).map((day, index) => {
            const currentDay = addDays(startOfCurrentWeek, index);
            const isToday = isSameDay(currentDay, new Date());
            const isYesterday = isSameDay(currentDay, addDays(new Date(), -1));

            return (
              <Grid.Column
                key={"current-week-" + index}
                className={
                  isYesterday
                    ? "background-orange"
                    : isToday
                    ? "background-blue"
                    : "background-white"
                }
                style={{
                  paddingTop: "4px",
                  boxShadow: "0 0 0 1px #d4d4d5",
                }}
              >
                <p className="inline tiny bottom margin bold small right margin">
                  {format(currentDay, "EEE")}
                </p>
                {isYesterday && (
                  <span className="float-left text-small text orange">
                    Yesterday
                  </span>
                )}
                {isToday && (
                  <span className="float-left text-small text blue">Today</span>
                )}
                <span className="float-right bold text-small">
                  {format(currentDay, "d")}
                </span>
                {Object.values(day).map((value, index) => (
                  <div key={"key2" + index} className="medium bottom margin">
                    <Card
                      fluid
                      onClick={() => handleGoToTransaction(value.transactionId)}
                    >
                      <Card.Content>
                        <Card.Header className="text-normal">
                          {value.address || value.transactionTitle || ""}
                        </Card.Header>
                        {value.tasks.map((task) => (
                          <div
                            className="zero bottom margin bold"
                            key={"div" + task.id}
                          >
                            <TaskActionButtons
                              task={task}
                              textStyle={true}
                              key={task.id}
                            />
                          </div>
                        ))}
                        {value.agentName && (
                          <Card.Meta>
                            {value.agentRepresents === "Buyer"
                              ? `BA: ${value.agentName}`
                              : `LA: ${value.agentName}`}
                          </Card.Meta>
                        )}
                        <Card.Meta>
                          {value.closingDateTime &&
                            `Closing: ${format(
                              new Date(value.closingDateTime),
                              "M/d/yyyy"
                            )}`}
                        </Card.Meta>
                      </Card.Content>
                    </Card>
                  </div>
                ))}
              </Grid.Column>
            );
          })}
        </Grid>
      </div>

      {/* Next Week */}
      <div>
        <h4 style={{ marginBottom: "15px", color: "#666" }}>Next Week</h4>
        <Grid style={{ minHeight: "100px" }} stackable columns="equal">
          {filteredTasksByDay.slice(7, 14).map((day, index) => {
            const currentDay = addDays(startOfCurrentWeek, index + 7);

            return (
              <Grid.Column
                key={"next-week-" + index}
                className="background-white"
                style={{
                  paddingTop: "4px",
                  boxShadow: "0 0 0 1px #d4d4d5",
                }}
              >
                <p className="inline tiny bottom margin bold small right margin">
                  {format(currentDay, "EEE")}
                </p>
                <span className="float-right bold text-small">
                  {format(currentDay, "d")}
                </span>
                {Object.values(day).map((value, index) => (
                  <div key={"key2" + index} className="medium bottom margin">
                    <Card
                      fluid
                      onClick={() => handleGoToTransaction(value.transactionId)}
                    >
                      <Card.Content>
                        <Card.Header className="text-normal">
                          {value.address || value.transactionTitle || ""}
                        </Card.Header>
                        {value.tasks.map((task) => (
                          <div
                            className="zero bottom margin bold"
                            key={"div" + task.id}
                          >
                            <TaskActionButtons
                              task={task}
                              textStyle={true}
                              key={task.id}
                            />
                          </div>
                        ))}
                        {value.agentName && (
                          <Card.Meta>
                            {value.agentRepresents === "Buyer"
                              ? `BA: ${value.agentName}`
                              : `LA: ${value.agentName}`}
                          </Card.Meta>
                        )}
                        <Card.Meta>
                          {value.closingDateTime &&
                            `Closing: ${format(
                              new Date(value.closingDateTime),
                              "M/d/yyyy"
                            )}`}
                        </Card.Meta>
                      </Card.Content>
                    </Card>
                  </div>
                ))}
              </Grid.Column>
            );
          })}
        </Grid>
      </div>
    </div>
  );
}
