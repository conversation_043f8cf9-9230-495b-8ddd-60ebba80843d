import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import useFirestoreCollectionNoAsync from "../../../app/hooks/useFirestoreCollectionNoAsync";
import {
  fetchEmailTemplatesFromDb,
  fetchManagerEmailTemplatesFromDb,
} from "../../../app/firestore/firestoreService";
import { fetchEmailTemplates } from "../emailTemplatesSlice";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import EmailTemplatesList from "./EmailTemplatesList";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { on<PERSON>napshot } from "firebase/firestore";

export default function EmailTemplatesSelect() {
  const dispatch = useDispatch();
  const { emailTemplates } = useSelector((state) => state.emailTemplates);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [managerEmailTemplates, setManagerEmailTemplates] = useState([]);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  useFirestoreCollectionNoAsync({
    query: () => fetchEmailTemplatesFromDb(),
    data: (userEmailTemplates) => {
      const templatesWithSource = userEmailTemplates.map((template) => ({
        ...template,
        templateSource: "own",
      }));

      // Combine with manager templates
      const allTemplates = [...templatesWithSource, ...managerEmailTemplates];
      dispatch(fetchEmailTemplates(allTemplates));
    },
    deps: [dispatch, managerEmailTemplates],
  });

  // Fetch manager templates if user is an agent with a manager
  useEffect(() => {
    if (currentUserProfile?.role === "agent" && currentUserProfile?.managerId) {
      const unsubscribe = onSnapshot(
        fetchManagerEmailTemplatesFromDb(currentUserProfile.managerId),
        (snapshot) => {
          const managerTemplatesData = snapshot.docs.map((doc) => ({
            ...doc.data(),
            id: doc.id,
            templateSource: "manager",
          }));
          setManagerEmailTemplates(managerTemplatesData);
        },
        (error) => {
          console.error("Error fetching manager templates:", error);
        }
      );

      return () => unsubscribe();
    } else {
      setManagerEmailTemplates([]);
    }
  }, [currentUserProfile]);

  if (!emailTemplates) {
    return <LoadingComponent />;
  }

  return (
    <ModalWrapper style={{ backgroundColor: "#f9fafb" }}>
      <Segment clearing style={{ backgroundColor: "#f9fafb" }}>
        <Header color="blue" size="large">
          Select Email Template to Add
          {!isMobile && (
            <Button
              primary
              floated="right"
              content="Close"
              onClick={() =>
                dispatch(
                  closeModal({
                    modalType: "EmailTemplatesSelect",
                  })
                )
              }
            ></Button>
          )}
        </Header>
        <Divider />{" "}
        {emailTemplates?.length > 0 ? (
          <EmailTemplatesList
            emailTemplates={emailTemplates}
            actionButtonMode="apply"
            // column={emailTemplates.column}
            // direction={emailTemplates.direction}
          />
        ) : (
          <p>There are no email templates.</p>
        )}
      </Segment>
    </ModalWrapper>
  );
}
