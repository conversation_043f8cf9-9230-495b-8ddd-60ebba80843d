import React, { useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Header,
  Input,
  Segment,
  Message,
} from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { renameEmailTemplateInDb } from "../../../app/firestore/firestoreService";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { isValidName } from "../../../app/common/util/util";

export default function EmailTemplatesRename({ emailTemplate }) {
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const dispatch = useDispatch();
  const [textInput, setTextInput] = useState(emailTemplate.name);
  const [validationError, setValidationError] = useState("");
  const inputRef = useRef(null);

  function handleTextInputChange(event) {
    let value = event.target.value;
    // Replace multiple consecutive spaces with single space
    value = value.replace(/\s+/g, ' ');
    setTextInput(value);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError("");
    }
  }

  function handleSubmit() {
    // Validate the input
    if (!textInput.trim()) {
      setValidationError("Name cannot be empty");
      return;
    }

    if (!isValidName(textInput.trim())) {
      setValidationError("Name can only contain letters, numbers, underscores, parentheses, dashes, and spaces");
      return;
    }

    try {
      renameEmailTemplateInDb(emailTemplate.id, { name: textInput.trim() });
      dispatch(
        closeModal({
          modalType: "EmailTemplatesRename",
        })
      );
    } catch (error) {
      throw error;
    }
  }

  return (
    <>
      <ModalWrapper size="small">
        <Segment>
          <Grid>
            <Grid.Column>
              <Header size="large" color="blue">
                Rename Email Template
              </Header>
              <Divider />
              <Input
                fluid
                ref={inputRef}
                value={textInput}
                onChange={handleTextInputChange}
                error={!!validationError}
              ></Input>
              {validationError && (
                <Message negative size="small">
                  {validationError}
                </Message>
              )}
              <Divider />
              <div>
                <Button
                  primary
                  type="submit"
                  onClick={() => handleSubmit()}
                  floated={isMobile ? null : "right"}
                  content="Submit"
                  className={isMobile ? "fluid medium bottom margin" : null}
                />
                <Button
                  type="button"
                  onClick={() =>
                    dispatch(
                      closeModal({
                        modalType: "emailTemplatesRename",
                      })
                    )
                  }
                  floated={isMobile ? null : "right"}
                  content="Cancel"
                  className={isMobile ? "fluid medium bottom margin" : null}
                />
              </div>
            </Grid.Column>
          </Grid>
        </Segment>
      </ModalWrapper>
    </>
  );
}
