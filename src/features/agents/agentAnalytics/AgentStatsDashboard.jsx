import React from "react";
import { useSelector } from "react-redux";
import { Grid, Table } from "semantic-ui-react";
import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function AgentStatsDashboard() {
  const {
    transActive,
    transUnderContract,
    transActiveListing,
    transActiveBuyer,
    transClosedForAgent,
  } = useSelector((state) => state.transaction);

  if (!transUnderContract ||
    !transActiveListing ||
    !transActiveBuyer ||
    !Array.isArray(transClosedForAgent)
  ) {
    return <LoadingComponent />;
  }
  
  let closedTransactionsByAgent = {};
  transClosedForAgent.forEach((transaction) => {
    if (
      transaction.agentProfile?.firstName &&
      transaction.agentProfile?.lastName
    ) {
      let salesPrice = 0;
      if (transaction.salesPrice) {
        salesPrice = parseFloat(transaction.salesPrice.replace(/[$,]/g, ""));
      }
      const agentName = `${transaction.agentProfile?.firstName} ${transaction.agentProfile?.lastName}`;
      if (closedTransactionsByAgent[agentName]) {
        closedTransactionsByAgent[agentName].count += 1;
        closedTransactionsByAgent[agentName].totalSalesPrice += salesPrice;
      } else {
        closedTransactionsByAgent[agentName] = {
          count: 1,
          totalSalesPrice: salesPrice,
        };
      }
    }
  });

  let numberUnderContract = "0";
  if (
    transUnderContract?.transactions?.length > 0 &&
    transActive?.length > 0
  ) {
    numberUnderContract = (
      (transUnderContract.transactions.length / transActive.length) *
      100
    ).toString();
  }

  let numberActiveListing = "0";
  if (
    transActiveListing?.transactions?.length > 0 &&
    transActive?.length > 0
  ) {
    numberActiveListing = (
      (transActiveListing.transactions.length / transActive.length) *
      100
    ).toString();
  }

  let numberActiveBuyer = "0";
  if (
    transActiveBuyer?.transactions?.length > 0 &&
    transActive?.length > 0
  ) {
    numberActiveBuyer = (
      (transActiveBuyer.transactions.length / transActive.length) *
      100
    ).toString();
  }

  return (
    <div className="main-page-wrapper">
      <>
        <Grid stackable className="large bottom margin">
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h1
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                My Stats
              </h1>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row className="small top margin">
            <Grid.Column
              computer={4}
              className="small bottom margin text-center"
            >
              <div className="progress-bar-wrapper">
                <div
                  className="progress-bar"
                  style={{
                    background: `radial-gradient(closest-side, white 79%, transparent 80% 100%),\n conic-gradient(deepskyblue 100%, lightgray 0)`,
                  }}
                >
                  <h2>{transActive?.length || 0}</h2>
                </div>
              </div>
              <h3>Active Transactions</h3>
            </Grid.Column>
            <Grid.Column
              computer={4}
              className="small bottom margin text-center"
            >
              <div className="progress-bar-wrapper">
                <div
                  className="progress-bar"
                  style={{
                    background: `radial-gradient(closest-side, white 79%, transparent 80% 100%),\n conic-gradient(darkorange ${numberUnderContract}%, lightgray 0)`,
                  }}
                >
                  <h2>{transUnderContract?.transactions?.length || 0}</h2>
                </div>
              </div>
              <h3>Under Contract</h3>
            </Grid.Column>
            <Grid.Column
              computer={4}
              className="small bottom margin text-center"
            >
              <div className="progress-bar-wrapper">
                <div
                  className="progress-bar"
                  style={{
                    background: `radial-gradient(closest-side, white 79%, transparent 80% 100%),\n conic-gradient(mediumspringgreen ${numberActiveListing}%, lightgray 0)`,
                  }}
                >
                  <h2>{transActiveListing?.transactions?.length || 0}</h2>
                </div>
              </div>
              <h3>Active Listings</h3>
            </Grid.Column>
            <Grid.Column
              computer={4}
              className="small bottom margin text-center"
            >
              <div className="progress-bar-wrapper">
                <div
                  className="progress-bar"
                  style={{
                    background: `radial-gradient(closest-side, white 79%, transparent 80% 100%),\n conic-gradient(fuchsia ${numberActiveBuyer}%, lightgray 0)`,
                  }}
                >
                  <h2>{transActiveBuyer?.transactions?.length || 0}</h2>
                </div>
              </div>
              <h3>Active Buyers</h3>
            </Grid.Column>
          </Grid.Row>
        </Grid>
        <Grid>
          <Grid.Column computer={16} className="small bottom margin">
            <h3 className="large bottom margin">My Closings Year-to-Date</h3>
            <Table compact>
              <Table.Header className="mobile hidden">
                <Table.Row className="small-header">
                  <Table.HeaderCell>Agent</Table.HeaderCell>
                  <Table.HeaderCell>Number of Closings</Table.HeaderCell>
                  <Table.HeaderCell>Total Sales Price</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {Object.entries(closedTransactionsByAgent).map(
                  ([key, value]) => (
                    <Table.Row key={key}>
                      <Table.Cell>{key}</Table.Cell>
                      <Table.Cell>{value.count}</Table.Cell>
                      <Table.Cell>
                        {value.totalSalesPrice.toLocaleString("en-US", {
                          style: "currency",
                          currency: "USD",
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0,
                        })}
                      </Table.Cell>
                    </Table.Row>
                  )
                )}
              </Table.Body>
            </Table>
          </Grid.Column>
        </Grid>
      </>
    </div>
  );
}
