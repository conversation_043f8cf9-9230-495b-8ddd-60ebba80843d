import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>con,
  Message,
  Progress,
  Table,
  Segment,
  Divider,
  Popup,
} from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import { functionCreateUsersFromCsv } from "../../app/firestore/functionsService";

// CSV column mapping to user object fields
const CSV_COLUMN_MAPPING = {
  "BrokerageForms": "brokerageForms",
  "BrokerageLogoRef": "brokerageLogoRef",
  "Payments Amount": "paymentsAmount",
  "Payments Date Expires": "paymentsDateExpires",
  "First Name": "firstName",
  "Middle Name": "middleName",
  "Last Name": "lastName",
  "Email": "email",
  "Phone": "phone",
  "Brokerage Name": "brokerageName",
  "Brokerage License Number": "brokerageLicenseNumber",
  "Agent License Number": "brokerLicenseNumber",
  "MLS Access": "mlsAccess",
  "Additional Forms": "additionalFormsAccess",
  "Address Street": "addressStreet",
  "Address Unit": "addressUnit",
  "City": "addressCity",
  "State": "state", // Full state name (Colorado, Texas, etc.)
  "Address State": "addressState", // 2-letter abbreviation (CO, TX, etc.)
  "Zipcode": "addressZipcode"
};

// Default values for new users (matching AdminPage defaults)
const DEFAULT_USER_VALUES = {
  role: "agent",
  state: "Colorado",
  hasBrokerageLogo: "true",
  type: "user",
  managerId: "",
  notes: "",
  referral: "",
  brokerageId: "",
  brokerNrdsId: "",
};

// MLS access options mapping
const MLS_ACCESS_MAPPING = {
  "CREN": { mlsIdCode: "CO_CREN", mlsName: "CREN" },
  "IRES": { mlsIdCode: "CO_IRES", mlsName: "IRES" },
  "RECOLORADO": { mlsIdCode: "CO_RECOLORADO", mlsName: "RECOLORADO" },
  "PPMLS": { mlsIdCode: "CO_PPMLS", mlsName: "PPMLS" },
  "SUMMIT": { mlsIdCode: "CO_SUMMIT", mlsName: "SUMMIT" },
  "PUEBLO": { mlsIdCode: "CO_Pueblo", mlsName: "PUEBLO" },
  "VAIL": { mlsIdCode: "CO_VMLS", mlsName: "VAIL" },
  "ASPEN": { mlsIdCode: "CO_ASPENGLENWOOD", mlsName: "ASPEN / GLENWOOD" },
  "GLENWOOD": { mlsIdCode: "CO_ASPENGLENWOOD", mlsName: "ASPEN / GLENWOOD" },
  "GRAND COUNTY": { mlsIdCode: "CO_GRANDCOUNTY", mlsName: "GRAND COUNTY" },
  "GRAND JUNCTION": { mlsIdCode: "CO_GRANDJUNCTION", mlsName: "GRAND JUNCTION" },
};

// Additional forms access mapping
const ADDITIONAL_FORMS_MAPPING = {
  "COX": "LEGAL_COX",
  "FRASCONA": "LEGAL_FRASCONA", 
  "CREN MLS": "MLS_CREN",
};

// Component to display logo preview in table cell
function LogoPreviewCell({ brokerageLogoRef, isFullUrl = false }) {
  if (!brokerageLogoRef) {
    return <span>No Logo</span>;
  }

  // Determine the image URL based on whether it's a full URL or partial reference
  const imageUrl = isFullUrl
    ? brokerageLogoRef
    : `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${brokerageLogoRef}/logo.png`;

  return (
    <Popup
      trigger={
        <span
          style={{
            color: "#2185d0",
            cursor: "pointer",
            textDecoration: "underline",
            fontWeight: "bold",
          }}
        >
          Preview Logo
        </span>
      }
      content={
        <img
          src={imageUrl}
          alt="Brokerage Logo"
          style={{
            maxHeight: "100px",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
          onError={(e) => {
            e.target.style.display = "none";
          }}
        />
      }
      position="right center"
      on="hover"
    />
  );
}

export default function CsvUserUpload() {
  const [csvData, setCsvData] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [results, setResults] = useState([]);
  const [errors, setErrors] = useState([]);

  const downloadSampleCsv = () => {
    const sampleData = [
      ['BrokerageForms', 'BrokerageLogoRef', 'Payments Amount', 'Payments Date Expires', 'First Name', 'Middle Name', 'Last Name', 'Email', 'Phone', 'Brokerage Name', 'Brokerage License Number', 'Agent License Number', 'MLS Access', 'Additional Forms', 'Address Street', 'Address Unit', 'City', 'State', 'Address State', 'Zipcode'],
      ['KW', 'KW', '0 Resident Realty', '2024-12-31', 'John', 'Michael', 'Doe', '<EMAIL>', '************', 'ABC Realty', 'LIC123456', 'AGT789012', 'CREN;IRES', 'COX', '123 Main St', 'Suite 100', 'Denver', 'Colorado', 'CO', '80202'],
      ['KWSW', 'KWSW', '0 Free Trial', '2025-06-30', 'Jane', '', 'Smith', '<EMAIL>', '************', 'XYZ Properties', 'LIC654321', 'AGT345678', 'RECOLORADO', 'FRASCONA', '456 Oak Ave', '', 'Boulder', 'Colorado', 'CO', '80301']
    ];

    const csvContent = sampleData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'sample-users.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const parseCsvFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target.result;
          const lines = text.split('\n').filter(line => line.trim());

          if (lines.length < 2) {
            reject(new Error('CSV file must contain at least a header row and one data row'));
            return;
          }

          // Parse CSV with better handling of quoted values
          const parseCSVLine = (line) => {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
              const char = line[i];

              if (char === '"') {
                inQuotes = !inQuotes;
              } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
              } else {
                current += char;
              }
            }
            result.push(current.trim());
            return result;
          };

          const headers = parseCSVLine(lines[0]);
          const data = [];

          // Validate required headers
          const requiredHeaders = ['Email', 'First Name', 'Last Name', 'BrokerageForms'];
          const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

          if (missingHeaders.length > 0) {
            reject(new Error(`Missing required columns: ${missingHeaders.join(', ')}`));
            return;
          }

          for (let i = 1; i < lines.length; i++) {
            const values = parseCSVLine(lines[i]);
            const row = {};

            headers.forEach((header, index) => {
              row[header] = values[index] || '';
            });

            // Basic validation for each row
            if (!row['Email'] || !row['First Name'] || !row['Last Name']) {
              console.warn(`Row ${i + 1} missing required fields, skipping`);
              return; // Skip this row
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(row['Email'])) {
              console.warn(`Row ${i + 1} has invalid email format: ${row['Email']}`);
            }

            data.push(row);
          }

          if (data.length === 0) {
            reject(new Error('No valid data rows found in CSV file'));
            return;
          }

          resolve(data);
        } catch (error) {
          reject(new Error('Failed to parse CSV file: ' + error.message));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const transformCsvRowToUser = (row) => {
    const user = { ...DEFAULT_USER_VALUES };

    // Map basic fields (excluding special cases)
    Object.entries(CSV_COLUMN_MAPPING).forEach(([csvColumn, userField]) => {
      if (row[csvColumn] && !userField.startsWith('address') && !['mlsAccess', 'additionalFormsAccess', 'paymentsAmount', 'paymentsDateExpires'].includes(userField)) {
        user[userField] = row[csvColumn];
      }
    });

    // Handle address mapping - only address fields go into the address object
    if (row['Address Street'] || row['Address Unit'] || row['City'] || row['Address State'] || row['Zipcode']) {
      user.address = {
        street: row['Address Street'] || '',
        unit: row['Address Unit'] || '',
        city: row['City'] || '',
        state: row['Address State'] || '', // 2-letter abbreviation
        zipcode: row['Zipcode'] || '',
      };
    }

    // Handle MLS Access - support both semicolon and comma separated values
    if (row['MLS Access']) {
      const separator = row['MLS Access'].includes(';') ? ';' : ',';
      const mlsNames = row['MLS Access'].split(separator).map(name => name.trim().toUpperCase());
      user.mlsAccess = mlsNames
        .map(name => MLS_ACCESS_MAPPING[name])
        .filter(Boolean);
    }

    // Handle Additional Forms - support both semicolon and comma separated values
    if (row['Additional Forms']) {
      const separator = row['Additional Forms'].includes(';') ? ';' : ',';
      const formNames = row['Additional Forms'].split(separator).map(name => name.trim().toUpperCase());
      user.additionalFormsAccess = formNames
        .map(name => ADDITIONAL_FORMS_MAPPING[name])
        .filter(Boolean);
    }

    // Handle payments - create proper payment object
    if (row['Payments Amount'] && row['Payments Date Expires']) {
      try {
        const amount = row['Payments Amount'];
        const dateExpires = new Date(row['Payments Date Expires']);

        // Validate date
        if (isNaN(dateExpires.getTime())) {
          console.warn(`Invalid date format for ${row['Email']}: ${row['Payments Date Expires']}`);
        } else {
          user.payments = [{
            amount: amount,
            datePayment: new Date(), // This will be converted to Timestamp in the cloud function
            dateExpires: dateExpires, // This will be converted to Timestamp in the cloud function
          }];
        }
      } catch (error) {
        console.warn(`Error parsing payment data for ${row['Email']}:`, error);
      }
    }

    // Ensure required fields are present
    if (!user.email || !user.firstName || !user.lastName) {
      throw new Error(`Missing required fields for row: ${JSON.stringify(row)}`);
    }

    // Set brokerage forms as required field
    if (!user.brokerageForms) {
      throw new Error(`Missing required field 'BrokerageForms' for ${user.email}`);
    }

    return user;
  };

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error('Please upload a CSV file');
      return;
    }

    try {
      const data = await parseCsvFile(file);
      setCsvData(data);
      setResults([]);
      setErrors([]);
      toast.success(`CSV file loaded with ${data.length} rows`);
    } catch (error) {
      toast.error(error.message);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    multiple: false
  });

  const processUsers = async () => {
    if (csvData.length === 0) {
      toast.error('No CSV data to process');
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(10);
    setResults([]);
    setErrors([]);

    try {
      // Pre-validate and transform all CSV rows
      const usersData = [];
      const preValidationErrors = [];

      for (let index = 0; index < csvData.length; index++) {
        const row = csvData[index];
        try {
          // Validate required fields
          if (!row['Email'] || !row['First Name'] || !row['Last Name']) {
            throw new Error('Missing required fields: Email, First Name, or Last Name');
          }

          // Validate email format
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(row['Email'])) {
            throw new Error('Invalid email format');
          }

          // Validate brokerage forms
          if (!row['BrokerageForms']) {
            throw new Error('Missing required field: BrokerageForms');
          }

          const userData = transformCsvRowToUser(row);
          usersData.push(userData);
        } catch (error) {
          preValidationErrors.push({
            row: index + 1,
            email: row['Email'] || 'N/A',
            name: `${row['First Name'] || 'N/A'} ${row['Last Name'] || 'N/A'}`,
            error: `Validation error: ${error.message}`
          });
        }
      }

      setProcessingProgress(30);

      // If there are validation errors, show them and stop processing
      if (preValidationErrors.length > 0) {
        setErrors(preValidationErrors);
        toast.error(`${preValidationErrors.length} validation errors found. Please fix the CSV data and try again.`);
        return;
      }

      if (usersData.length === 0) {
        toast.error('No valid users to process after validation');
        return;
      }

      setProcessingProgress(50);

      // Call the bulk creation function
      const result = await functionCreateUsersFromCsv({ users: usersData });

      setProcessingProgress(90);

      // Transform results for display
      const newResults = result.results.map(r => ({
        row: r.index + 1,
        email: r.email,
        name: usersData[r.index] ? `${usersData[r.index].firstName} ${usersData[r.index].lastName}` : 'N/A',
        status: 'Success',
        uid: r.uid
      }));

      const newErrors = result.errors.map(e => ({
        row: e.index + 1,
        email: e.email,
        name: usersData[e.index] ? `${usersData[e.index].firstName || 'N/A'} ${usersData[e.index].lastName || 'N/A'}` : 'N/A',
        error: e.error
      }));

      setResults(newResults);
      setErrors(newErrors);
      setProcessingProgress(100);

      if (result.successCount > 0 && result.errorCount === 0) {
        toast.success(`All ${result.successCount} users created successfully!`);
      } else if (result.successCount > 0 && result.errorCount > 0) {
        toast.warning(`${result.successCount} users created, ${result.errorCount} errors occurred`);
      } else {
        toast.error(`Failed to create any users. ${result.errorCount} errors occurred`);
      }

    } catch (error) {
      console.error('Error processing users:', error);
      const errorMessage = error.message || 'Unknown error occurred';
      toast.error(`Error processing users: ${errorMessage}`);
      setErrors([{
        row: 'N/A',
        email: 'N/A',
        name: 'N/A',
        error: errorMessage
      }]);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Segment clearing>
      <Header size="huge" color="blue">
        Bulk User Upload (CSV)
      </Header>
      <Message info>
        <Message.Header>CSV Format Requirements</Message.Header>
        <p>Upload a CSV file to create multiple users at once. The CSV must contain these <strong>required columns</strong>:</p>
        <Message.List>
          <Message.Item><strong>Email</strong> - User's email address (must be unique)</Message.Item>
          <Message.Item><strong>First Name</strong> - User's first name</Message.Item>
          <Message.Item><strong>Last Name</strong> - User's last name</Message.Item>
          <Message.Item><strong>BrokerageForms</strong> - Brokerage forms identifier</Message.Item>
        </Message.List>
        <p><strong>Optional columns:</strong> BrokerageLogoRef, Payments Amount, Payments Date Expires, Middle Name, Phone, Brokerage Name, Brokerage License Number, Agent License Number, MLS Access (semicolon separated), Additional Forms (semicolon separated), Address Street, Address Unit, City, State (full name like Colorado), Address State (2-letter like CO), Zipcode</p>
        <p><strong>Default values:</strong> Role = agent, State = Colorado, Free Trial = 3 months</p>
      </Message>

      <Button
        icon="download"
        content="Download Sample CSV"
        onClick={downloadSampleCsv}
        style={{ marginBottom: '20px' }}
      />

      <Divider />

      {/* File Upload Area */}
      <div
        {...getRootProps()}
        style={{
          border: '2px dashed #ccc',
          borderRadius: '10px',
          padding: '40px',
          textAlign: 'center',
          cursor: 'pointer',
          backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa',
          marginBottom: '20px'
        }}
      >
        <input {...getInputProps()} />
        <Icon name="upload" size="huge" color="blue" />
        <Header as="h3">
          {isDragActive ? 'Drop the CSV file here' : 'Drag & drop a CSV file here, or click to select'}
        </Header>
        <p>Only CSV files are accepted</p>
      </div>

      {/* CSV Data Preview */}
      {csvData.length > 0 && (
        <>
          <Header as="h4" color="blue">
            CSV Data Preview ({csvData.length} rows)
          </Header>
          <div style={{ maxHeight: '300px', overflowY: 'auto', overflowX: 'auto', marginBottom: '20px' }}>
            <Table celled compact size="small">
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Row</Table.HeaderCell>
                  <Table.HeaderCell>Email</Table.HeaderCell>
                  <Table.HeaderCell>First Name</Table.HeaderCell>
                  <Table.HeaderCell>Last Name</Table.HeaderCell>
                  <Table.HeaderCell>Brokerage Forms</Table.HeaderCell>
                  <Table.HeaderCell>Brokerage Logo</Table.HeaderCell>
                  <Table.HeaderCell>MLS Access</Table.HeaderCell>
                  <Table.HeaderCell>City</Table.HeaderCell>
                  <Table.HeaderCell>State</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {csvData.slice(0, 10).map((row, index) => {
                  // Transform MLS Access for display
                  const mlsNames = row['MLS Access'] ?
                    row['MLS Access'].split(';').map(name => name.trim()).join(', ') :
                    'N/A';

                  return (
                    <Table.Row key={index}>
                      <Table.Cell>{index + 1}</Table.Cell>
                      <Table.Cell>{row['Email'] || 'N/A'}</Table.Cell>
                      <Table.Cell>{row['First Name'] || 'N/A'}</Table.Cell>
                      <Table.Cell>{row['Last Name'] || 'N/A'}</Table.Cell>
                      <Table.Cell>{row['BrokerageForms'] || 'N/A'}</Table.Cell>
                      <Table.Cell>
                        {row['BrokerageLogoRef'] ? (
                          <LogoPreviewCell
                            brokerageLogoRef={row['BrokerageLogoRef']}
                            isFullUrl={false}
                          />
                        ) : (
                          'No Logo'
                        )}
                      </Table.Cell>
                      <Table.Cell>{mlsNames}</Table.Cell>
                      <Table.Cell>{row['City'] || 'N/A'}</Table.Cell>
                      <Table.Cell>{row['State'] || 'N/A'}</Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
            {csvData.length > 10 && (
              <Message info>
                Showing first 10 rows. Total rows: {csvData.length}
              </Message>
            )}
          </div>

          <Button
            primary
            size="large"
            onClick={processUsers}
            disabled={isProcessing}
            loading={isProcessing}
          >
            Create {csvData.length} Users
          </Button>
        </>
      )}

      {/* Processing Progress */}
      {isProcessing && (
        <div style={{ marginTop: '20px' }}>
          <Header as="h4">Processing Users...</Header>
          <Progress percent={processingProgress} indicating />
        </div>
      )}

      {/* Results */}
      {(results.length > 0 || errors.length > 0) && !isProcessing && (
        <div style={{ marginTop: '20px' }}>
          <Header as="h4" color="green">
            Processing Results
          </Header>
          
          {results.length > 0 && (
            <Message positive>
              <Message.Header>Successfully Created Users ({results.length})</Message.Header>
              <Message.List>
                {results.slice(0, 5).map((result, index) => (
                  <Message.Item key={index}>
                    Row {result.row}: {result.name} ({result.email})
                  </Message.Item>
                ))}
                {results.length > 5 && (
                  <Message.Item>... and {results.length - 5} more</Message.Item>
                )}
              </Message.List>
            </Message>
          )}

          {errors.length > 0 && (
            <Message negative>
              <Message.Header>Errors ({errors.length})</Message.Header>
              <Message.List>
                {errors.slice(0, 5).map((error, index) => (
                  <Message.Item key={index}>
                    Row {error.row}: {error.name} ({error.email}) - {error.error}
                  </Message.Item>
                ))}
                {errors.length > 5 && (
                  <Message.Item>... and {errors.length - 5} more errors</Message.Item>
                )}
              </Message.List>
            </Message>
          )}
        </div>
      )}
    </Segment>
  );
}
