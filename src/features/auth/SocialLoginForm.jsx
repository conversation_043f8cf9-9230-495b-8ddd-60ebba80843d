import React from "react";
import { But<PERSON> } from "semantic-ui-react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { socialLogin } from "../../app/firestore/firebaseService";

export default function SocialLoginForm() {
  const dispatch = useDispatch();

  async function handleSocialLogin(provider) {
    try {
      const result = await socialLogin(provider);
      if (result) {
        // dispatch(closeModal());
        // updateLastSignIn(result.user.uid);
        // ReactGA.event({
        //   category: "User",
        //   action: "Login",
        // });
        // history.push("/activities");
      }
    } catch (error) {
      toast.error(error.message);
    }
  }

  return (
    <>
      <Button
        onClick={() => handleSocialLogin("facebook")}
        icon="facebook"
        fluid
        size="large"
        color="facebook"
        style={{ marginBottom: 10 }}
        content="Login with Facebook"
      />
      <Button
        onClick={() => handleSocialLogin("google")}
        icon="google"
        fluid
        size="large"
        color="google plus"
        content="Login with Google"
      />
    </>
  );
}
