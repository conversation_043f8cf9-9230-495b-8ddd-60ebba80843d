import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Icon,
  Message,
  Progress,
  Table,
  Segment,
  Divider,
  Modal,
  Form,
  Input,
  Popup,
} from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import { 
  getUserProfileByEmailFromDb,
  addTransactionToDb,
  addPartyToDb 
} from "../../app/firestore/firestoreService";

// CSV column mapping for transaction creation (for reference)
// const CSV_COLUMN_MAPPING = {
//   "agentEmail": "agentEmail",
//   "listingDate": "listingDate",
//   "mlsNumber": "mlsNumber",
//   "legalDescription": "legalDescription",
//   "address": "address",
//   "seller1FirstName": "seller1FirstName",
//   "seller1LastName": "seller1LastName",
//   "seller1Email": "seller1Email",
//   "seller1Phone": "seller1Phone",
//   "seller2FirstName": "seller2FirstName",
//   "seller2LastName": "seller2LastName",
//   "seller2Email": "seller2Email",
//   "seller2Phone": "seller2Phone",
//   "seller3FirstName": "seller3FirstName",
//   "seller3LastName": "seller3LastName",
//   "seller3Email": "seller3Email",
//   "seller3Phone": "seller3Phone",
//   "seller4FirstName": "seller4FirstName",
//   "seller4LastName": "seller4LastName",
//   "seller4Email": "seller4Email",
//   "seller4Phone": "seller4Phone",
// };

// Edit Transaction Form Component
function EditTransactionForm({ transaction, onSave, onCancel }) {
  const [formData, setFormData] = useState(transaction);

  const handleInputChange = (field, value, clientType = null) => {
    if (clientType) {
      setFormData(prev => ({
        ...prev,
        [clientType]: {
          ...prev[clientType],
          [field]: value
        }
      }));
    } else if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSave = () => {
    // Basic validation
    if (!formData.title || !formData.client.firstName || !formData.client.lastName) {
      toast.error('Title, client first name, and last name are required');
      return;
    }
    onSave(formData);
  };

  return (
    <Form>
      <Form.Group widths="equal">
        <Form.Field>
          <label>Transaction Title</label>
          <Input
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>MLS Number</label>
          <Input
            value={formData.mlsNumbers[0] || ''}
            onChange={(e) => {
              const newMlsNumbers = e.target.value ? [e.target.value] : [];
              handleInputChange('mlsNumbers', newMlsNumbers);
            }}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Street Address</label>
          <Input
            value={formData.address.street}
            onChange={(e) => handleInputChange('address.street', e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>City</label>
          <Input
            value={formData.address.city}
            onChange={(e) => handleInputChange('address.city', e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>State</label>
          <Input
            value={formData.address.state}
            onChange={(e) => handleInputChange('address.state', e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Zip Code</label>
          <Input
            value={formData.address.zipcode}
            onChange={(e) => handleInputChange('address.zipcode', e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      <Header as="h4">Primary Client</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>First Name</label>
          <Input
            value={formData.client.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value, 'client')}
          />
        </Form.Field>
        <Form.Field>
          <label>Last Name</label>
          <Input
            value={formData.client.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value, 'client')}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Email</label>
          <Input
            value={formData.client.email}
            onChange={(e) => handleInputChange('email', e.target.value, 'client')}
          />
        </Form.Field>
        <Form.Field>
          <label>Phone</label>
          <Input
            value={formData.client.phone}
            onChange={(e) => handleInputChange('phone', e.target.value, 'client')}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>
            <input
              type="checkbox"
              checked={formData.client.isTrust || false}
              onChange={(e) => handleInputChange('isTrust', e.target.checked, 'client')}
            />
            {' '}Is Entity/Trust
          </label>
        </Form.Field>
        <Form.Field>
          <label>Entity Name</label>
          <Input
            value={formData.client.entityName || ''}
            onChange={(e) => handleInputChange('entityName', e.target.value, 'client')}
            disabled={!formData.client.isTrust}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Authority Title</label>
          <Input
            value={formData.client.authorityTitle || ''}
            onChange={(e) => handleInputChange('authorityTitle', e.target.value, 'client')}
            disabled={!formData.client.isTrust}
          />
        </Form.Field>
        <Form.Field>
          <label>Signer Name</label>
          <Input
            value={formData.client.signerName || ''}
            onChange={(e) => handleInputChange('signerName', e.target.value, 'client')}
            // disabled={!formData.client.isTrust}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Legal Description</label>
          <Input
            value={formData.propertyDetails.legalDescription}
            onChange={(e) => handleInputChange('propertyDetails.legalDescription', e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>County</label>
          <Input
            value={formData.propertyDetails.county}
            onChange={(e) => handleInputChange('propertyDetails.county', e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      {formData.clientSecondary.exists && (
        <>
          <Header as="h4">Secondary Client</Header>
          <Form.Group widths="equal">
            <Form.Field>
              <label>First Name</label>
              <Input
                value={formData.clientSecondary.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value, 'clientSecondary')}
              />
            </Form.Field>
            <Form.Field>
              <label>Last Name</label>
              <Input
                value={formData.clientSecondary.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value, 'clientSecondary')}
              />
            </Form.Field>
          </Form.Group>

          <Form.Group widths="equal">
            <Form.Field>
              <label>Email</label>
              <Input
                value={formData.clientSecondary.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value, 'clientSecondary')}
              />
            </Form.Field>
            <Form.Field>
              <label>Phone</label>
              <Input
                value={formData.clientSecondary.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value, 'clientSecondary')}
              />
            </Form.Field>
          </Form.Group>

          <Form.Group widths="equal">
            <Form.Field>
              <label>
                <input
                  type="checkbox"
                  checked={formData.clientSecondary.isTrust || false}
                  onChange={(e) => handleInputChange('isTrust', e.target.checked, 'clientSecondary')}
                />
                {' '}Is Entity/Trust
              </label>
            </Form.Field>
            <Form.Field>
              <label>Entity Name</label>
              <Input
                value={formData.clientSecondary.entityName || ''}
                onChange={(e) => handleInputChange('entityName', e.target.value, 'clientSecondary')}
                disabled={!formData.clientSecondary.isTrust}
              />
            </Form.Field>
          </Form.Group>

          <Form.Group widths="equal">
            <Form.Field>
              <label>Authority Title</label>
              <Input
                value={formData.clientSecondary.authorityTitle || ''}
                onChange={(e) => handleInputChange('authorityTitle', e.target.value, 'clientSecondary')}
                disabled={!formData.clientSecondary.isTrust}
              />
            </Form.Field>
            <Form.Field>
              <label>Signer Name</label>
              <Input
                value={formData.clientSecondary.signerName || ''}
                onChange={(e) => handleInputChange('signerName', e.target.value, 'clientSecondary')}
                disabled={!formData.clientSecondary.isTrust}
              />
            </Form.Field>
          </Form.Group>
        </>
      )}

      <div style={{ marginTop: '20px' }}>
        <Button primary onClick={handleSave}>
          Save Changes
        </Button>
        <Button onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </Form>
  );
}

// Edit CSV Row Form Component
function EditCsvRowForm({ csvRow, onSave, onCancel }) {
  const [formData, setFormData] = useState(csvRow);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Basic validation
    if (!formData.agentEmail || !formData['address.street'] || !formData['seller1.firstName'] || !formData['seller1.lastName']) {
      toast.error('Agent email, street address, and primary seller name are required');
      return;
    }
    onSave(formData);
  };

  return (
    <Form>
      <Header as="h4">Agent Information</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>Agent Email</label>
          <Input
            value={formData.agentEmail}
            onChange={(e) => handleInputChange("agentEmail", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Agent Represents</label>
          <Input
            value={formData.agentRepresents || ""}
            onChange={(e) =>
              handleInputChange("agentRepresents", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Header as="h4">Property Information</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>Street Address</label>
          <Input
            value={formData["address.street"]}
            onChange={(e) =>
              handleInputChange("address.street", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>City</label>
          <Input
            value={formData["address.city"] || ""}
            onChange={(e) => handleInputChange("address.city", e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>State</label>
          <Input
            value={formData["address.state"] || ""}
            onChange={(e) => handleInputChange("address.state", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Zip Code</label>
          <Input
            value={formData["address.zipcode"] || ""}
            onChange={(e) =>
              handleInputChange("address.zipcode", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>County</label>
          <Input
            value={formData.county || ""}
            onChange={(e) => handleInputChange("county", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>MLS Number</label>
          <Input
            value={formData["mls.number"] || ""}
            onChange={(e) => handleInputChange("mls.number", e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Listing Date</label>
          <Input
            value={formData.listingDate || ""}
            onChange={(e) => handleInputChange("listingDate", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Legal Description</label>
          <Input
            value={formData.legalDescription || ""}
            onChange={(e) =>
              handleInputChange("legalDescription", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Header as="h4">Primary Seller</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>First Name</label>
          <Input
            value={formData["seller1.firstName"]}
            onChange={(e) =>
              handleInputChange("seller1.firstName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Middle Name</label>
          <Input
            value={formData["seller1.middleName"] || ""}
            onChange={(e) =>
              handleInputChange("seller1.middleName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Last Name</label>
          <Input
            value={formData["seller1.lastName"]}
            onChange={(e) =>
              handleInputChange("seller1.lastName", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Email</label>
          <Input
            value={formData["seller1.email"] || ""}
            onChange={(e) => handleInputChange("seller1.email", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Phone</label>
          <Input
            value={formData["seller1.phone"] || ""}
            onChange={(e) => handleInputChange("seller1.phone", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Is Entity</label>
          <Input
            value={formData["seller1.isEntity"] || "false"}
            onChange={(e) =>
              handleInputChange("seller1.isEntity", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      {formData["seller1.isEntity"] === "true" && (
        <Form.Group widths="equal">
          <Form.Field>
            <label>Entity Name</label>
            <Input
              value={formData["seller1.entityName"] || ""}
              onChange={(e) =>
                handleInputChange("seller1.entityName", e.target.value)
              }
            />
          </Form.Field>
          <Form.Field>
            <label>Authority Title</label>
            <Input
              value={formData["seller1.authorityTitle"] || ""}
              onChange={(e) =>
                handleInputChange("seller1.authorityTitle", e.target.value)
              }
            />
          </Form.Field>
        </Form.Group>
      )}

      <Header as="h4">Secondary Seller (Optional)</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>First Name</label>
          <Input
            value={formData["seller2.firstName"] || ""}
            onChange={(e) =>
              handleInputChange("seller2.firstName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Middle Name</label>
          <Input
            value={formData["seller2.middleName"] || ""}
            onChange={(e) =>
              handleInputChange("seller2.middleName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Last Name</label>
          <Input
            value={formData["seller2.lastName"] || ""}
            onChange={(e) =>
              handleInputChange("seller2.lastName", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Header as="h4">Third Seller (Optional)</Header>
      <Form.Group widths="equal">
        <Form.Field>
          <label>First Name</label>
          <Input
            value={formData["seller3.firstName"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.firstName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Middle Name</label>
          <Input
            value={formData["seller3.middleName"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.middleName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Last Name</label>
          <Input
            value={formData["seller3.lastName"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.lastName", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Email</label>
          <Input
            value={formData["seller3.email"] || ""}
            onChange={(e) => handleInputChange("seller3.email", e.target.value)}
          />
        </Form.Field>
        <Form.Field>
          <label>Phone</label>
          <Input
            value={formData["seller3.phone"] || ""}
            onChange={(e) => handleInputChange("seller3.phone", e.target.value)}
          />
        </Form.Field>
      </Form.Group>

      <Form.Group widths="equal">
        <Form.Field>
          <label>Is Entity</label>
          <Input
            value={formData["seller3.isEntity"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.isEntity", e.target.value)
            }
            placeholder="true or false"
          />
        </Form.Field>
        <Form.Field>
          <label>Entity Name</label>
          <Input
            value={formData["seller3.entityName"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.entityName", e.target.value)
            }
          />
        </Form.Field>
        <Form.Field>
          <label>Authority Title</label>
          <Input
            value={formData["seller3.authorityTitle"] || ""}
            onChange={(e) =>
              handleInputChange("seller3.authorityTitle", e.target.value)
            }
          />
        </Form.Field>
      </Form.Group>

      <div style={{ marginTop: "20px" }}>
        <Button primary onClick={handleSave}>
          Save Changes
        </Button>
        <Button onClick={onCancel}>Cancel</Button>
      </div>
    </Form>
  );
}

// Function to validate a single CSV row
const validateCsvRow = (row, rowNumber) => {
  const errors = [];
  let hasErrors = false;

  // Basic validation for each row - collect errors instead of skipping
  if (!row['agentEmail']) {
    errors.push('Agent email is required');
    hasErrors = true;
  }
  if (!row['address.street']) {
    errors.push('Street address is required');
    hasErrors = true;
  }
  if (!row['seller1.firstName']) {
    errors.push('Primary seller first name is required');
    hasErrors = true;
  }
  if (!row['seller1.lastName']) {
    errors.push('Primary seller last name is required');
    hasErrors = true;
  }

  // Validate email format
  if (row['agentEmail']) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(row['agentEmail'])) {
      errors.push('Invalid email format');
      hasErrors = true;
    }
  }

  return {
    ...row,
    _rowNumber: rowNumber,
    _hasErrors: hasErrors,
    _errors: errors
  };
};

export default function TransactionSpreadsheetUpload() {
  const [csvData, setCsvData] = useState([]);
  const [processedData, setProcessedData] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [results, setResults] = useState([]);
  const [errors, setErrors] = useState([]);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState(null);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [csvEditModalOpen, setCsvEditModalOpen] = useState(false);
  const [editingCsvRow, setEditingCsvRow] = useState(null);
  const [editingCsvIndex, setEditingCsvIndex] = useState(-1);

  const { currentUserProfile } = useSelector((state) => state.profile);

  const downloadSampleCsv = () => {
    const sampleData = [
      [
        "agentEmail",
        "transactionFolder",
        "listingPdf",
        "agentRepresents",
        "listingDate",
        "county",
        "legalDescription",
        "address.street",
        "address.city",
        "address.state",
        "address.zipcode",
        "listingAgent.firstName",
        "listingAgent.middleName",
        "listingAgent.lastName",
        "coAgent.firstName",
        "coAgent.middleName",
        "coAgent.lastName",
        "seller1.firstName",
        "seller1.middleName",
        "seller1.lastName",
        "seller1.isEntity",
        "seller1.entityName",
        "seller1.authorityTitle",
        "seller1.phone",
        "seller1.email",
        "seller2.firstName",
        "seller2.middleName",
        "seller2.lastName",
        "seller2.isEntity",
        "seller2.entityName",
        "seller2.authorityTitle",
        "seller2.phone",
        "seller2.email",
        "compensation.type",
        "compensation.value",
        "mls.number",
      ],
      [
        "<EMAIL>",
        "folder1",
        "listing1.pdf",
        "Seller",
        "2024-01-15",
        "Denver",
        "Lot 1 Block 2 Example Subdivision",
        "123 Main St",
        "Denver",
        "CO",
        "80202",
        "John",
        "A",
        "Agent",
        "",
        "",
        "",
        "John",
        "Q",
        "Smith",
        "false",
        "",
        "",
        "555-1234",
        "<EMAIL>",
        "Jane",
        "R",
        "Smith",
        "false",
        "",
        "",
        "555-5678",
        "<EMAIL>",
        "Percentage",
        "3.0",
        "12345",
      ],
      [
        "<EMAIL>",
        "folder2",
        "listing2.pdf",
        "Seller",
        "2024-01-20",
        "Boulder",
        "Lot 5 Block 3 Another Subdivision",
        "456 Oak Ave",
        "Boulder",
        "CO",
        "80301",
        "Sarah",
        "B",
        "Broker",
        "Mike",
        "C",
        "CoAgent",
        "Bob",
        "",
        "Johnson",
        "true",
        "Johnson LLC",
        "Manager",
        "555-9999",
        "<EMAIL>",
        "",
        "",
        "",
        "false",
        "",
        "",
        "",
        "",
        "Fixed",
        "5000",
        "67890",
      ],
    ];

    const csvContent = sampleData
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", "sample-transactions.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  console.log("userProf", currentUserProfile);

  const parseCsvFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target.result;
          const lines = text.split("\n").filter((line) => line.trim());

          if (lines.length < 2) {
            reject(
              new Error(
                "CSV file must contain at least a header row and one data row"
              )
            );
            return;
          }

          // Parse CSV with better handling of quoted values
          const parseCSVLine = (line) => {
            const result = [];
            let current = "";
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
              const char = line[i];

              if (char === '"') {
                inQuotes = !inQuotes;
              } else if (char === "," && !inQuotes) {
                result.push(current.trim());
                current = "";
              } else {
                current += char;
              }
            }
            result.push(current.trim());
            return result;
          };

          const headers = parseCSVLine(lines[0]);
          const data = [];

          // Validate required headers
          const requiredHeaders = [
            "agentEmail",
            "address.street",
            "seller1.firstName",
            "seller1.lastName",
          ];
          const missingHeaders = requiredHeaders.filter(
            (header) => !headers.includes(header)
          );

          if (missingHeaders.length > 0) {
            reject(
              new Error(
                `Missing required columns: ${missingHeaders.join(", ")}`
              )
            );
            return;
          }

          for (let i = 1; i < lines.length; i++) {
            const values = parseCSVLine(lines[i]);
            const row = {};

            headers.forEach((header, index) => {
              row[header] = values[index] || "";
            });

            // Validate the row using the shared validation function
            const validatedRow = validateCsvRow(row, i + 1);

            // Always add the row, even if it has errors
            data.push(validatedRow);
          }

          if (data.length === 0) {
            reject(new Error("No valid data rows found in CSV file"));
            return;
          }

          resolve(data);
        } catch (error) {
          reject(new Error("Failed to parse CSV file: " + error.message));
        }
      };
      reader.onerror = () => reject(new Error("Failed to read file"));
      reader.readAsText(file);
    });
  };

  const validateAgentEmail = async (email) => {
    try {
      const normalizedEmail = email.toLowerCase().trim();
      const userProfiles = await getUserProfileByEmailFromDb(normalizedEmail);
      if (!userProfiles || userProfiles.length === 0) {
        return { valid: false, error: "Agent email not found in database" };
      }

      const userProfile = userProfiles[0];
      if (userProfile.type !== "user" || userProfile.role !== "agent") {
        return {
          valid: false,
          error: "Email does not belong to an agent user",
        };
      }

      return { valid: true, userProfile };
    } catch (error) {
      return {
        valid: false,
        error: "Error validating agent email: " + error.message,
      };
    }
  };

  const parseAddress = (row) => {
    // Parse address from separate columns
    return {
      street: row["address.street"] || "",
      city: row["address.city"] || "",
      state: row["address.state"] || "",
      zipcode: row["address.zipcode"] || "",
      unit: "",
    };
  };

  const transformCsvRowToTransaction = async (row, index) => {
    // Validate agent email first
    const agentValidation = await validateAgentEmail(row.agentEmail);
    if (!agentValidation.valid) {
      throw new Error(`Row ${index + 1}: ${agentValidation.error}`);
    }

    const agentProfile = agentValidation.userProfile;
    const address = parseAddress(row);

    // Create transaction title: seller1.lastName - address.street
    const title = `${row["seller1.lastName"]} - ${address.street}`;

    // Parse listing date for createdAt
    let createdAt = new Date();
    if (row.listingDate) {
      const parsedDate = new Date(row.listingDate);
      if (!isNaN(parsedDate.getTime())) {
        createdAt = parsedDate;
      }
    }

    // Helper function to create client object
    const createClient = (prefix) => {
      const firstName = row[`${prefix}.firstName`];
      const lastName = row[`${prefix}.lastName`];

      if (!firstName || !lastName) {
        return { exists: false };
      }

      const isEntity = row[`${prefix}.isEntity`] === "true";
      const entityName = row[`${prefix}.entityName`] || "";
      const isTrust = isEntity || (entityName && entityName.trim() !== "");

      return {
        firstName: firstName,
        lastName: lastName,
        middleName: row[`${prefix}.middleName`] || "",
        email: row[`${prefix}.email`]
          ? row[`${prefix}.email`].toLowerCase().trim()
          : "",
        phone: row[`${prefix}.phone`] || "",
        isTrust: isTrust,
        entityName: entityName,
        authorityTitle: row[`${prefix}.authorityTitle`] || "",
        signerName: isTrust ? `${firstName} ${lastName}` : "",
        exists: true,
      };
    };

    const transaction = {
      active: true,
      createdBy: "upload",
      agentRepresents: row.agentRepresents || "Seller",
      userId: agentProfile.userId,
      agentProfile: agentProfile,
      title: title,
      address: address,
      propertyDetails: {
        legalDescription: row.legalDescription || "",
        county: row.county || "",
        inclusions: "",
        exclusions: "",
        yearBuilt: "",
      },
      mlsNumbers: row["mls.number"] ? [row["mls.number"]] : [],
      timeZone: "America/Denver",
      state: "No Offer",
      createdAt: createdAt,
      // Client info
      client: createClient("seller1"),
      clientSecondary: row["seller2.lastName"]
        ? createClient("seller2")
        : { exists: false },
      clientSecondaryExists: row["seller2.lastName"] ? true : false,
      clientThird: row["seller3.lastName"]
        ? createClient("seller3")
        : { exists: false },
      clientThirdExists: row["seller3.lastName"] ? true : false,
      // Store additional data for potential use
      compensation: {
        type: row["compensation.type"] || "",
        value: row["compensation.value"] || "",
      },
      listingAgent: {
        firstName: row["listingAgent.firstName"] || "",
        middleName: row["listingAgent.middleName"] || "",
        lastName: row["listingAgent.lastName"] || "",
      },
      coAgent: {
        firstName: row["coAgent.firstName"] || "",
        middleName: row["coAgent.middleName"] || "",
        lastName: row["coAgent.lastName"] || "",
      },
    };

    return transaction;
  };

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith(".csv")) {
      toast.error("Please upload a CSV file");
      return;
    }

    try {
      const data = await parseCsvFile(file);
      setCsvData(data);
      setProcessedData([]);
      setResults([]);
      setErrors([]);
      toast.success(`CSV file loaded with ${data.length} rows`);
    } catch (error) {
      toast.error(error.message);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "text/csv": [".csv"],
      "application/vnd.ms-excel": [".csv"],
    },
    multiple: false,
  });

  const processTransactionData = async () => {
    if (csvData.length === 0) {
      toast.error("No CSV data to process");
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(10);
    setProcessedData([]);
    setResults([]);
    setErrors([]);

    try {
      const transactions = [];
      const processingErrors = [];

      for (let index = 0; index < csvData.length; index++) {
        const row = csvData[index];
        setProcessingProgress(10 + (index / csvData.length) * 80);

        // Skip rows that already have CSV parsing errors
        if (row._hasErrors) {
          processingErrors.push({
            row: row._rowNumber || index + 1,
            error: row._errors.join("; "),
          });
          continue;
        }

        try {
          const transaction = await transformCsvRowToTransaction(row, index);
          transactions.push(transaction);
        } catch (error) {
          processingErrors.push({
            row: row._rowNumber || index + 1,
            error: error.message,
          });
        }
      }

      setProcessedData(transactions);
      setErrors(processingErrors);
      setProcessingProgress(100);

      if (transactions.length > 0) {
        toast.success(
          `${transactions.length} transactions processed successfully`
        );
      }
      if (processingErrors.length > 0) {
        toast.warning(`${processingErrors.length} rows had errors`);
      }
    } catch (error) {
      console.error("Error processing transaction data:", error);
      toast.error(`Error processing data: ${error.message}`);
      setErrors([
        {
          row: "N/A",
          error: error.message,
        },
      ]);
    } finally {
      setIsProcessing(false);
    }
  };

  const openEditModal = (transaction, index) => {
    setEditingTransaction({ ...transaction });
    setEditingIndex(index);
    setEditModalOpen(true);
  };

  const saveEditedTransaction = (editedTransaction) => {
    const newProcessedData = [...processedData];
    newProcessedData[editingIndex] = editedTransaction;
    setProcessedData(newProcessedData);
    setEditModalOpen(false);
    toast.success("Transaction updated");
  };

  const deleteTransaction = (index) => {
    const newProcessedData = processedData.filter((_, i) => i !== index);
    setProcessedData(newProcessedData);
    toast.success("Transaction removed");
  };

  const openEditCsvRowModal = (row, index) => {
    setEditingCsvRow({ ...row });
    setEditingCsvIndex(index);
    setCsvEditModalOpen(true);
  };

  const saveEditedCsvRow = (editedRow) => {
    // Re-validate the edited row
    const validatedRow = validateCsvRow(
      editedRow,
      editedRow._rowNumber || editingCsvIndex + 1
    );

    const newCsvData = [...csvData];
    newCsvData[editingCsvIndex] = validatedRow;
    setCsvData(newCsvData);
    setCsvEditModalOpen(false);

    if (validatedRow._hasErrors) {
      toast.warning(
        `Row updated but still has ${validatedRow._errors.length} error(s)`
      );
    } else {
      toast.success("CSV row updated and validated successfully");
    }
  };

  const deleteCsvRow = (index) => {
    const newCsvData = csvData.filter((_, i) => i !== index);
    setCsvData(newCsvData);
    toast.success("CSV row removed");
  };

  const createTransactions = async () => {
    if (processedData.length === 0) {
      toast.error("No transactions to create");
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(10);
    setResults([]);
    setErrors([]);

    try {
      const creationResults = [];
      const creationErrors = [];

      for (let index = 0; index < processedData.length; index++) {
        const transaction = processedData[index];
        try {
          setProcessingProgress(
            10 + Math.floor((index / processedData.length) * 80)
          );
          console.log(
            "heading out to addTransactionToDb, currentUserProfile",
            currentUserProfile
          );
          // Create the transaction
          delete transaction.canUploadCreateTransactions;
          if (transaction.agentProfile?.mlsAccess.length === 1) {
            transaction.mlsInfo = [
              {
                mlsIdCode: transaction.agentProfile.mlsAccess[0].mlsIdCode,
                mlsName: transaction.agentProfile.mlsAccess[0].mlsName,
                mlsNumber: transaction.mlsNumbers[0] || "",
              },
            ];
          }
          const transactionId = await addTransactionToDb(
            transaction,
            [], // people
            [], // forms
            transaction.agentProfile, // currentUserProfile,
            null, // mlsData
            [], // agentsForAssistant
            null, // formTemplates
            [], // taskTemplates
            transaction.agentProfile // agentUserProfile - the agent this transaction is for
          );
          console.log("transactionId", transactionId);

          // Add additional parties if seller4 exists
          if (
            transaction.additionalParties &&
            transaction.additionalParties.length > 0
          ) {
            for (const party of transaction.additionalParties) {
              await addPartyToDb(
                party,
                { id: transactionId, userId: transaction.userId },
                []
              );
            }
          }

          creationResults.push({
            row: index + 1,
            title: transaction.title,
            agentEmail: transaction.agentProfile.email,
            transactionId: transactionId,
          });
        } catch (error) {
          console.error(`Error creating transaction ${index + 1}:`, error);
          creationErrors.push({
            row: index + 1,
            error: error.message,
          });
        }
      }

      setResults(creationResults);
      setErrors(creationErrors);
      setProcessingProgress(100);

      if (creationResults.length > 0 && creationErrors.length === 0) {
        toast.success(
          `All ${creationResults.length} transactions created successfully!`
        );
      } else if (creationResults.length > 0 && creationErrors.length > 0) {
        toast.warning(
          `${creationResults.length} transactions created, ${creationErrors.length} errors occurred`
        );
      } else {
        toast.error(
          `Failed to create any transactions. ${creationErrors.length} errors occurred`
        );
      }
    } catch (error) {
      console.error("Error creating transactions:", error);
      toast.error(`Error creating transactions: ${error.message}`);
      setErrors([
        {
          row: "N/A",
          error: error.message,
        },
      ]);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Segment clearing>
      <Header size="huge" color="blue">
        Bulk Transaction Upload (CSV)
      </Header>
      <Message info>
        <Message.Header>CSV Format Requirements</Message.Header>
        <p>
          Upload a CSV file to create multiple transactions at once. The CSV
          must contain these <strong>required columns</strong>:
        </p>
        <Message.List>
          <Message.Item>
            <strong>agentEmail</strong> - Agent's email (must exist as type=user
            & role=agent)
          </Message.Item>
          <Message.Item>
            <strong>address.street</strong> - Property street address
          </Message.Item>
          <Message.Item>
            <strong>seller1.firstName</strong> - Primary seller first name
          </Message.Item>
          <Message.Item>
            <strong>seller1.lastName</strong> - Primary seller last name
          </Message.Item>
        </Message.List>
        <p>
          <strong>Optional columns:</strong> transactionFolder, listingPdf,
          agentRepresents, listingDate, county, legalDescription, address.city,
          address.state, address.zipcode, listingAgent.firstName,
          listingAgent.middleName, listingAgent.lastName, coAgent.firstName,
          coAgent.middleName, coAgent.lastName, seller1.middleName,
          seller1.isEntity, seller1.entityName, seller1.authorityTitle,
          seller1.phone, seller1.email, seller2.firstName, seller2.middleName,
          seller2.lastName, seller2.isEntity, seller2.entityName,
          seller2.authorityTitle, seller2.phone, seller2.email,
          compensation.type, compensation.value, mls.number
        </p>
        <p>
          <strong>Notes:</strong> Columns transactionFolder and listingPdf are
          ignored. listingDate sets transaction.createdAt.
          transaction.createdBy="upload" and active="true". Use periods in
          column names as shown above.
        </p>
      </Message>

      <Button
        icon="download"
        content="Download Sample CSV"
        onClick={downloadSampleCsv}
        style={{ marginBottom: "20px" }}
      />

      <Divider />

      {/* File Upload Area */}
      <div
        {...getRootProps()}
        style={{
          border: "2px dashed #ccc",
          borderRadius: "10px",
          padding: "40px",
          textAlign: "center",
          cursor: "pointer",
          backgroundColor: isDragActive ? "#f0f8ff" : "#fafafa",
          marginBottom: "20px",
        }}
      >
        <input {...getInputProps()} />
        <Icon name="upload" size="huge" color="blue" />
        <Header as="h3">
          {isDragActive
            ? "Drop the CSV file here"
            : "Drag & drop a CSV file here, or click to select"}
        </Header>
        <p>Only CSV files are accepted</p>
      </div>

      {/* CSV Data Preview and Processing */}
      {csvData.length > 0 && (
        <>
          <Header as="h4" color="blue">
            CSV Data Preview ({csvData.length} rows)
          </Header>

          <div
            style={{
              maxHeight: "400px",
              overflowY: "auto",
              overflowX: "auto",
              marginBottom: "20px",
            }}
          >
            <Table celled compact size="small">
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Row</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Agent Email</Table.HeaderCell>
                  <Table.HeaderCell>Address</Table.HeaderCell>
                  <Table.HeaderCell>Primary Seller</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>Secondary Seller</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>Third Seller</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>MLS #</Table.HeaderCell>
                  <Table.HeaderCell>Listing Date</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {csvData.map((row, index) => (
                  <Table.Row key={index} negative={row._hasErrors}>
                    <Table.Cell>{row._rowNumber || index + 1}</Table.Cell>
                    <Table.Cell>
                      {row._hasErrors ? (
                        <Popup
                          trigger={
                            <Icon name="exclamation triangle" color="red" />
                          }
                          content={
                            <div>
                              <strong>Errors:</strong>
                              <ul
                                style={{ margin: "5px 0", paddingLeft: "20px" }}
                              >
                                {row._errors.map((error, i) => (
                                  <li key={i}>{error}</li>
                                ))}
                              </ul>
                            </div>
                          }
                          position="right center"
                        />
                      ) : (
                        <Icon name="checkmark" color="green" />
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {row.agentEmail || (
                        <em style={{ color: "#999" }}>Missing</em>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {row["address.street"] ? (
                        `${row["address.street"]}, ${row["address.city"]}, ${row["address.state"]} ${row["address.zipcode"]}`
                      ) : (
                        <em style={{ color: "#999" }}>Missing address</em>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller1.firstName"] && row["seller1.lastName"] ? (
                        `${row["seller1.firstName"]} ${row["seller1.lastName"]}`
                      ) : (
                        <em style={{ color: "#999" }}>Missing name</em>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller1.isEntity"] === "true" ||
                      row["seller1.entityName"]
                        ? "Yes"
                        : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller1.entityName"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller1.authorityTitle"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller2.firstName"] && row["seller2.lastName"]
                        ? `${row["seller2.firstName"]} ${row["seller2.lastName"]}`
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller2.isEntity"] === "true" ||
                      row["seller2.entityName"]
                        ? "Yes"
                        : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller2.entityName"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller2.authorityTitle"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller3.firstName"] && row["seller3.lastName"]
                        ? `${row["seller3.firstName"]} ${row["seller3.lastName"]}`
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller3.isEntity"] === "true" ||
                      row["seller3.entityName"]
                        ? "Yes"
                        : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller3.entityName"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {row["seller3.authorityTitle"] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>{row["mls.number"] || "N/A"}</Table.Cell>
                    <Table.Cell>{row.listingDate || "N/A"}</Table.Cell>
                    <Table.Cell>
                      <Button
                        size="mini"
                        icon="edit"
                        onClick={() => openEditCsvRowModal(row, index)}
                        title="Edit"
                      />
                      <Button
                        size="mini"
                        icon="trash"
                        color="red"
                        onClick={() => deleteCsvRow(index)}
                        title="Delete"
                      />
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          <Button
            primary
            size="large"
            onClick={processTransactionData}
            disabled={isProcessing}
            loading={isProcessing}
            style={{ marginBottom: "20px" }}
          >
            Process Transaction Data to get ready...
          </Button>
        </>
      )}

      {/* Processing Progress */}
      {isProcessing && (
        <div style={{ marginTop: "20px" }}>
          <Header as="h4">Processing Transaction Data...</Header>
          <Progress percent={processingProgress} indicating />
        </div>
      )}

      {/* Processed Data Preview Table */}
      {processedData.length > 0 && !isProcessing && (
        <>
          <Header as="h4" color="green">
            Transaction Preview ({processedData.length} transactions)
          </Header>
          <Message info>
            <p>
              <strong>Processing Summary:</strong> {processedData.length} of{" "}
              {csvData.length} rows processed successfully
              {errors.length > 0 && (
                <span style={{ color: "#d01919" }}>
                  {" "}
                  • {errors.length} rows failed (see errors below)
                </span>
              )}
            </p>
            <p>
              Review the transactions below. You can edit or delete individual
              entries before creating them.
            </p>
          </Message>

          <div
            style={{
              maxHeight: "400px",
              overflowY: "auto",
              overflowX: "auto",
              marginBottom: "20px",
            }}
          >
            <Table celled compact size="small">
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Row</Table.HeaderCell>
                  <Table.HeaderCell>Agent Email</Table.HeaderCell>
                  <Table.HeaderCell>Title</Table.HeaderCell>
                  <Table.HeaderCell>Address</Table.HeaderCell>
                  <Table.HeaderCell>Primary Client</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>Secondary Client</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>Third Client</Table.HeaderCell>
                  <Table.HeaderCell>Entity?</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Authority Title</Table.HeaderCell>
                  <Table.HeaderCell>MLS #</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {processedData.map((transaction, index) => (
                  <Table.Row key={index}>
                    <Table.Cell>{index + 1}</Table.Cell>
                    <Table.Cell>
                      {transaction.agentProfile?.email || "N/A"}
                    </Table.Cell>
                    <Table.Cell>{transaction.title}</Table.Cell>
                    <Table.Cell>{`${transaction.address.street}, ${transaction.address.city}, ${transaction.address.state} ${transaction.address.zipcode}`}</Table.Cell>
                    <Table.Cell>{`${transaction.client.firstName} ${transaction.client.lastName}`}</Table.Cell>
                    <Table.Cell>
                      {transaction.client.isTrust ? "Yes" : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.client.entityName || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.client.authorityTitle || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientSecondary.exists
                        ? `${transaction.clientSecondary.firstName} ${transaction.clientSecondary.lastName}`
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientSecondary.exists &&
                      transaction.clientSecondary.isTrust
                        ? "Yes"
                        : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientSecondary.exists
                        ? transaction.clientSecondary.entityName || "N/A"
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientSecondary.exists
                        ? transaction.clientSecondary.authorityTitle || "N/A"
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientThird.exists
                        ? `${transaction.clientThird.firstName} ${transaction.clientThird.lastName}`
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientThird.exists &&
                      transaction.clientThird.isTrust
                        ? "Yes"
                        : "No"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientThird.exists
                        ? transaction.clientThird.entityName || "N/A"
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.clientThird.exists
                        ? transaction.clientThird.authorityTitle || "N/A"
                        : "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.mlsNumbers[0] || "N/A"}
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        size="mini"
                        icon="edit"
                        onClick={() => openEditModal(transaction, index)}
                        title="Edit"
                      />
                      <Button
                        size="mini"
                        icon="trash"
                        color="red"
                        onClick={() => deleteTransaction(index)}
                        title="Delete"
                      />
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          {/* Processing Errors */}
          {errors.length > 0 && (
            <div style={{ marginBottom: "20px" }}>
              <Message negative>
                <Message.Header>
                  Processing Errors ({errors.length} rows failed)
                </Message.Header>
                <Message.Content>
                  <p>
                    The following rows could not be processed. Please fix the
                    issues in your CSV file and re-upload:
                  </p>
                </Message.Content>
                <Message.List>
                  {errors.map((error, index) => (
                    <Message.Item key={index}>
                      <strong>Row {error.row}:</strong> {error.error}
                    </Message.Item>
                  ))}
                </Message.List>
              </Message>
            </div>
          )}

          <Button
            primary
            size="large"
            onClick={createTransactions}
            disabled={isProcessing}
            loading={isProcessing}
          >
            Create {processedData.length} Transactions
          </Button>
        </>
      )}

      {/* Results */}
      {(results.length > 0 || errors.length > 0) && !isProcessing && (
        <div style={{ marginTop: "20px" }}>
          <Header as="h4" color="green">
            Creation Results
          </Header>

          {results.length > 0 && (
            <Message positive>
              <Message.Header>
                Successfully Created Transactions ({results.length})
              </Message.Header>
              <Message.List>
                {results.slice(0, 5).map((result, index) => (
                  <Message.Item key={index}>
                    Row {result.row}: {result.title} for {result.agentEmail}
                  </Message.Item>
                ))}
                {results.length > 5 && (
                  <Message.Item>... and {results.length - 5} more</Message.Item>
                )}
              </Message.List>
            </Message>
          )}

          {errors.length > 0 && (
            <Message negative>
              <Message.Header>Errors ({errors.length})</Message.Header>
              <Message.List>
                {errors.slice(0, 5).map((error, index) => (
                  <Message.Item key={index}>
                    Row {error.row}: {error.error}
                  </Message.Item>
                ))}
                {errors.length > 5 && (
                  <Message.Item>
                    ... and {errors.length - 5} more errors
                  </Message.Item>
                )}
              </Message.List>
            </Message>
          )}
        </div>
      )}

      {/* Edit Transaction Modal */}
      <Modal
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        size="large"
      >
        <Modal.Header>Edit Transaction</Modal.Header>
        <Modal.Content>
          {editingTransaction && (
            <EditTransactionForm
              transaction={editingTransaction}
              onSave={saveEditedTransaction}
              onCancel={() => setEditModalOpen(false)}
            />
          )}
        </Modal.Content>
      </Modal>

      {/* Edit CSV Row Modal */}
      <Modal
        open={csvEditModalOpen}
        onClose={() => setCsvEditModalOpen(false)}
        size="large"
      >
        <Modal.Header>Edit CSV Row</Modal.Header>
        <Modal.Content>
          {editingCsvRow && (
            <EditCsvRowForm
              csvRow={editingCsvRow}
              onSave={saveEditedCsvRow}
              onCancel={() => setCsvEditModalOpen(false)}
            />
          )}
        </Modal.Content>
      </Modal>
    </Segment>
  );
}
