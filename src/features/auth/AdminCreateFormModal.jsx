import React, { useCallback, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>con,
  Message,
  Segment,
  Divider,
} from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import { useDispatch } from "react-redux";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import ModalWrapper from "../../app/common/modals/modalWrapper";
import { closeModal } from "../../app/common/modals/modalSlice";
import { uploadBlobToStorage } from "../../app/firestore/firebaseService";
import MyTextInput from "../../app/common/form/MyTextInput";
import MyTextArea from "../../app/common/form/MyTextArea";
import MyCheckbox from "../../app/common/form/MyCheckbox";
import MySelectInput from "../../app/common/form/MySelectInput";
import {
  getFirestore,
  collection,
  addDoc,
  doc,
  updateDoc,
} from "firebase/firestore";
import { app } from "../../app/config/firebase";
import { propertyTypeOptions } from "../../app/common/categoryData/categoryOptions";

// Role options for form sharing settings
const formRoleOptions = [
  { key: "buyer", text: "Buyer", value: "Buyer" },
  { key: "buyer2", text: "Buyer 2", value: "Buyer 2" },
  { key: "buyer3", text: "Buyer 3", value: "Buyer 3" },
  { key: "seller", text: "Seller", value: "Seller" },
  { key: "seller2", text: "Seller 2", value: "Seller 2" },
  { key: "seller3", text: "Seller 3", value: "Seller 3" },
  { key: "listingAgent", text: "Listing Agent", value: "Listing Agent" },
  { key: "buyerAgent", text: "Buyer Agent", value: "Buyer Agent" },
  { key: "title", text: "Title", value: "Title" },
  { key: "lender", text: "Lender", value: "Lender" },
];

// Transaction status options for suggested forms
const formTransactionStatusOptions = [
  { key: "activeListing", text: "Active Listing", value: "Active Listing" },
  { key: "activeBuyer", text: "Active Buyer", value: "Active Buyer" },
  { key: "underContract", text: "Under Contract", value: "Under Contract" },
  { key: "complete", text: "Complete", value: "Complete" },
  { key: "archived", text: "Archived", value: "Archived" },
];

const dropzoneStyles = {
  textAlign: "center",
  cursor: "pointer",
  paddingTop: "36px",
  paddingBottom: "36px",
  border: "2px dashed #ccc",
  borderRadius: "8px",
  marginBottom: "20px",
};

const dropzoneActive = {
  border: "dashed 3px #2185d0",
  backgroundColor: "#f0f8ff",
};

export default function AdminCreateFormModal({
  selectedState,
  existingForm = null,
  isEditMode = false,
}) {
  const dispatch = useDispatch();
  const [uploadedFile, setUploadedFile] = useState(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [formMessage, setFormMessage] = useState("");
  const [formMessageType, setFormMessageType] = useState("");

  const onDrop = useCallback(
    async (acceptedFiles) => {
      const firstFile = acceptedFiles?.[0];
      if (!firstFile) return;

      if (firstFile.type !== "application/pdf") {
        setUploadError("Please upload a PDF file");
        return;
      }

      setUploadLoading(true);
      setUploadError(null);

      try {
        // Extract filename without extension for title/name
        const filenameWithoutExt = firstFile.name.replace(/\.pdf$/i, "");
        
        // Upload to storage/forms{State}/
        const storagePath = `forms${selectedState}/${firstFile.name}`;
        const blob = await fetch(URL.createObjectURL(firstFile)).then((r) =>
          r.blob()
        );
        await uploadBlobToStorage(storagePath, blob);

        setUploadedFile({
          name: firstFile.name,
          title: filenameWithoutExt,
          storagePath: storagePath,
        });
        setUploadSuccess(true);
        setUploadLoading(false);
      } catch (error) {
        console.error("Error uploading file:", error);
        setUploadError(error.message);
        setUploadLoading(false);
      }
    },
    [selectedState]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { "application/pdf": [".pdf"] },
    multiple: false,
  });

  // Get initial values - from existing form for edit mode, or from uploaded file for create mode
  const getInitialValues = () => {
    if (isEditMode && existingForm) {
      return {
        title: existingForm.title || "",
        name: existingForm.name || "",
        brokerageReplacesParentFormTitle: existingForm.brokerageReplacesParentFormTitle || "",
        showBuyerAgent: existingForm.showBuyerAgent || false,
        showSellerAgent: existingForm.showSellerAgent || false,
        canBeTemplate: existingForm.canBeTemplate || false,
        isStateForm: existingForm.isStateForm || false,
        isFillableByClient: existingForm.isFillableByClient || false,
        canRemoveOtherSideSigsIfTransCancelled: existingForm.canRemoveOtherSideSigsIfTransCancelled || false,
        autoAddToNewTransaction: existingForm.autoAddToNewTransaction || false,
        isManagerOnly: existingForm.isManagerOnly || false,
        dontShowFormInAddDocumentModal: existingForm.dontShowFormInAddDocumentModal || false,
        hasFieldConversions: existingForm.hasFieldConversions || false,
        additionalFormsGroupId: existingForm.additionalFormsGroupId || "",
        canCustomizeEmailOnShare: existingForm.canCustomizeEmailOnShare || [],
        emailSharingDefaultsSubject: existingForm.emailSharingDefaults?.subjectPlusAddress || "",
        emailSharingDefaultsMessage: existingForm.emailSharingDefaults?.message || "",
        isSuggestedTitleInAddDocModalSeller: existingForm.isSuggestedTitleInAddDocModal?.Seller || [],
        isSuggestedTitleInAddDocModalBuyer: existingForm.isSuggestedTitleInAddDocModal?.Buyer || [],
        doNotShareWithRole: existingForm.doNotShareWithRole || [],
        isBrokerageForm: existingForm.isBrokerageForm || false,
        brokerage: existingForm.brokerage || "",
      };
    }
    return {
      title: uploadedFile?.title || "",
      name: uploadedFile?.title || "",
      brokerageReplacesParentFormTitle: "",
      showBuyerAgent: false,
      showSellerAgent: false,
      canBeTemplate: false,
      isStateForm: false,
      isFillableByClient: false,
      canRemoveOtherSideSigsIfTransCancelled: false,
      autoAddToNewTransaction: false,
      isManagerOnly: false,
      dontShowFormInAddDocumentModal: false,
      hasFieldConversions: false,
      additionalFormsGroupId: "",
      canCustomizeEmailOnShare: [],
      emailSharingDefaultsSubject: "",
      emailSharingDefaultsMessage: "",
      isSuggestedTitleInAddDocModalSeller: [],
      isSuggestedTitleInAddDocModalBuyer: [],
      doNotShareWithRole: [],
      isBrokerageForm: false,
      brokerage: "",
    };
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    setFormMessage("");
    try {
      const db = getFirestore(app);
      const collectionName = `forms${selectedState}`;

      const formData = {
        title: values.title,
        name: values.name,
        brokerageReplacesParentFormTitle: values.brokerageReplacesParentFormTitle || "",
        showBuyerAgent: values.showBuyerAgent,
        showSellerAgent: values.showSellerAgent,
        canBeTemplate: values.canBeTemplate,
        isStateForm: values.isStateForm,
        isFillableByClient: values.isFillableByClient,
        canRemoveOtherSideSigsIfTransCancelled: values.canRemoveOtherSideSigsIfTransCancelled,
        autoAddToNewTransaction: values.autoAddToNewTransaction,
        isManagerOnly: values.isManagerOnly,
        dontShowFormInAddDocumentModal: values.dontShowFormInAddDocumentModal,
        hasFieldConversions: values.hasFieldConversions,
        additionalFormsGroupId: values.additionalFormsGroupId || "",
        canCustomizeEmailOnShare: values.canCustomizeEmailOnShare || [],
        emailSharingDefaults: {
          subjectPlusAddress: values.emailSharingDefaultsSubject || "",
          message: values.emailSharingDefaultsMessage || "",
        },
        isSuggestedTitleInAddDocModal: {
          Seller: values.isSuggestedTitleInAddDocModalSeller || [],
          Buyer: values.isSuggestedTitleInAddDocModalBuyer || [],
        },
        doNotShareWithRole: values.doNotShareWithRole || [],
        isBrokerageForm: values.isBrokerageForm || false,
        brokerage: values.brokerage || "",
      };

      if (isEditMode && existingForm?.id) {
        // Update existing form
        const docRef = doc(db, collectionName, existingForm.id);
        await updateDoc(docRef, formData);
        setFormMessage(`Form "${values.title}" updated successfully`);
      } else {
        // Create new form
        formData.createdAt = new Date();
        await addDoc(collection(db, collectionName), formData);
        setFormMessage(`Form "${values.title}" created successfully in ${collectionName}`);
      }
      setFormMessageType("success");

      setTimeout(() => {
        dispatch(closeModal({ modalType: "AdminCreateFormModal" }));
      }, 1500);
    } catch (error) {
      console.error("Error saving form:", error);
      setFormMessage(`Error saving form: ${error.message}`);
      setFormMessageType("error");
    } finally {
      setSubmitting(false);
    }
  };

  // For create mode, show upload UI first if no file uploaded yet
  if (!isEditMode && !uploadSuccess) {
    return (
      <ModalWrapper size="small" header={`Create New Form - ${selectedState}`}>
        <Segment>
          <div
            {...getRootProps()}
            style={isDragActive ? { ...dropzoneStyles, ...dropzoneActive } : dropzoneStyles}
          >
            <input {...getInputProps()} />
            <Icon name="file pdf outline" size="huge" color="grey" />
            <Header color="grey" style={{ marginTop: "6px" }}>
              Drag and drop a PDF form here
              <br />
              or click to select a file
            </Header>
            <p style={{ color: "#888" }}>
              The PDF will be uploaded to storage/forms{selectedState}/
            </p>
          </div>
          {uploadLoading && (
            <Message info>
              <Icon name="spinner" loading />
              Uploading...
            </Message>
          )}
          {uploadError && (
            <Message negative>
              <Message.Header>Upload Error</Message.Header>
              <p>{uploadError}</p>
            </Message>
          )}
          <Button
            onClick={() => dispatch(closeModal({ modalType: "AdminCreateFormModal" }))}
          >
            Cancel
          </Button>
        </Segment>
      </ModalWrapper>
    );
  }

  // Show the form (either after upload for create, or immediately for edit)
  return (
    <ModalWrapper size="large" header={isEditMode ? `Edit Form - ${selectedState}` : `Create New Form - ${selectedState}`}>
      <Formik
        enableReinitialize
        initialValues={getInitialValues()}
        validationSchema={Yup.object({
          title: Yup.string().required("Unique name is required"),
          name: Yup.string()
            .matches(
              /^[a-zA-Z0-9 _-]*$/,
              "Only letters, numbers, underscore, dash, and spaces are allowed."
            )
            .required("Display name is required"),
        })}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, dirty }) => (
          <Form className="ui form">
            <Segment>
              {uploadedFile && (
                <Message positive>
                  <Message.Header>File Uploaded Successfully</Message.Header>
                  <p>Uploaded: {uploadedFile.name}</p>
                </Message>
              )}
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="title"
                      label="Unique Name of File"
                      placeholder="Include brokerage abbreviation (e.g., CB_ExclusiveRightToSell)"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="name"
                      label="Display Name"
                      placeholder="Name shown in transaction (e.g., Exclusive Right to Sell)"
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="category"
                      label="Searchable field"
                    />
                  </Grid.Column>
                </Grid.Row>
                
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyCheckbox name="showBuyerAgent" label="Show Buyer Agent" />
                    <MyCheckbox name="showSellerAgent" label="Show Seller Agent" />
                    <MyCheckbox name="canBeTemplate" label="Can be a Form Template" />
                    <MyCheckbox name="isStateForm" label="Is State Form" />
                    <MyCheckbox name="isFillableByClient" label="Can Client Fill Out Form" />
                    <MyCheckbox name="canRemoveOtherSideSigsIfTransCancelled" label="Can Remove Other Side Sigs If Trans Cancelled" />

                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyCheckbox name="autoAddToNewTransaction" label="Automatically Add to New Transaction" />
                    <MyCheckbox name="isManagerOnly" label="Manager Only" />
                    <MyCheckbox name="dontShowFormInAddDocumentModal" label="Don't Show in Add Document Modal" />
                    <MyCheckbox name="hasFieldConversions" label="Has Field Conversions" />
                    <MyCheckbox name="canApplyDeadlinesTemplate" label="Can Apply Deadlines Template" />
                    <MyCheckbox name="canImportFromListing" label="Can Import from Listing" />
                    <MySelectInput
                      name="propertyType"
                      label="Property Type"
                      options={propertyTypeOptions}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Divider />
                <Grid.Row>
                  <Grid.Column mobile={16} computer={4}>
                    <MyCheckbox name="isBrokerageForm" label="Is Brokerage Form?" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={4}>
                    <MyTextInput name="brokerage" label="Brokerage" />
                  </Grid.Column>
                   <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageReplacesParentFormTitle"
                      label="State Form to Override (optional)"
                      placeholder="Enter the name of the state form this replaces"
                    />
                  </Grid.Column>
                  </Grid.Row>
                  <Divider />
               
                <Grid.Row>
                  <Grid.Column mobile={16} computer={4}>
                    <MyTextInput
                      name="additionalFormsGroupId"
                      label="Additional Forms Group ID"
                      placeholder="Group ID"
                    />
                  </Grid.Column>
                </Grid.Row>
                <Divider />
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Header size="small" color="blue">Email Sharing Options</Header>
                    <MySelectInput
                      name="canCustomizeEmailOnShare"
                      label="Can Customize Email on Share (select roles)"
                      options={formRoleOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="emailSharingDefaultsSubject"
                      label="Default Email Subject"
                      placeholder="Default subject line for sharing"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextArea
                      name="emailSharingDefaultsMessage"
                      label="Default Email Message"
                      placeholder="Default email body for sharing"
                      rows={3}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Divider />
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Header size="small" color="blue">Suggested Forms Options</Header>
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MySelectInput
                      name="isSuggestedTitleInAddDocModalSeller"
                      label="Suggest for Seller Transactions"
                      options={formTransactionStatusOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MySelectInput
                      name="isSuggestedTitleInAddDocModalBuyer"
                      label="Suggest for Buyer Transactions"
                      options={formTransactionStatusOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Divider />
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Header size="small" color="blue">Sharing Restrictions</Header>
                    <MySelectInput
                      name="doNotShareWithRole"
                      label="Do NOT Share With These Roles"
                      options={formRoleOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Button
                      loading={isSubmitting}
                      disabled={!dirty || isSubmitting}
                      type="submit"
                      size="large"
                      color="blue"
                      content={isEditMode ? "Update Form" : "Create Form"}
                    />
                    <Button
                      type="button"
                      size="large"
                      onClick={() => dispatch(closeModal({ modalType: "AdminCreateFormModal" }))}
                    >
                      Cancel
                    </Button>
                    {formMessage && (
                      <Message
                        positive={formMessageType === "success"}
                        negative={formMessageType === "error"}
                        style={{ marginTop: "10px" }}
                      >
                        {formMessage}
                      </Message>
                    )}
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Segment>
          </Form>
        )}
      </Formik>
    </ModalWrapper>
  );
}
