import React from "react";
import { <PERSON><PERSON>, Card, Icon, Divider } from "semantic-ui-react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import {
  convertAddressFull,
  convertFullName,
  convertPhoneToLink,
} from "../../../app/common/util/util";
import PartyCard from "./PartyCard";

export default function PartyContacts() {
  const { allParties } = useSelector((state) => state.transaction);
  const { transaction } = useSelector((state) => state.transaction);
  const { party } = useSelector((state) => state.party);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  if (!allParties || !transaction || !party) {
    return <LoadingComponent content="Loading contacts..." />;
  }

  // backwards compatible
  const agentFullName = transaction?.agentProfile?.firstName
    ? convertFullName(transaction.agentProfile)
    : transaction?.agentName || "";
  const agentMain = {
    fullName: agentFullName,
    email: transaction?.agentProfile?.email || transaction?.agentEmail || "",
    phone: transaction?.agentProfile?.phone || transaction?.agentPhone || "",
  };

  let userPartyRole = "";
  if (party && !userPartyRole) {
    userPartyRole = party.role;
  }
  const buyerAgent = allParties.filter(
    (partyPerson) => partyPerson.role === "Buyer Agent"
  );
  const sellerAgent = allParties.filter(
    (partyPerson) => partyPerson.role === "Listing Agent"
  );

  const coAgentMine = allParties.filter(
    (partyPerson) => partyPerson.role === "CoAgent (Mine)"
  );
  const tcMine = allParties.filter(
    (partyPerson) => partyPerson.role === "TC (Mine)"
  );

  const orderedPartiesBuyerSide = [
    allParties.filter((partyPerson) => partyPerson.role === "Buyer")
      ? allParties.filter((partyPerson) => partyPerson.role === "Buyer")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Buyer 2")
      ? allParties.filter((partyPerson) => partyPerson.role === "Buyer 2")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Buyer 3")
      ? allParties.filter((partyPerson) => partyPerson.role === "Buyer 3")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Buyer Lender")
      ? allParties.filter(
          (partyPerson) => partyPerson.role === "Buyer Lender"
        )[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Buyer Attorney")
      ? allParties.filter(
          (partyPerson) => partyPerson.role === "Buyer Attorney"
        )[0]
      : {},
  ];
  const orderedPartiesSellerSide = [
    // allParties.filter((partyPerson) => partyPerson.role === "Listing Agent")
    //   ? allParties.filter(
    //       (partyPerson) => partyPerson.role === "Listing Agent"
    //     )[0]
    //   : {},
    allParties.filter((partyPerson) => partyPerson.role === "Seller")
      ? allParties.filter((partyPerson) => partyPerson.role === "Seller")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Seller 2")
      ? allParties.filter((partyPerson) => partyPerson.role === "Seller 2")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Seller 3")
      ? allParties.filter((partyPerson) => partyPerson.role === "Seller 3")[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Seller Attorney")
      ? allParties.filter(
          (partyPerson) => partyPerson.role === "Seller Attorney"
        )[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Title Company")
      ? allParties.filter(
          (partyPerson) => partyPerson.role === "Title Company"
        )[0]
      : {},
    allParties.filter((partyPerson) => partyPerson.role === "Escrow Officer")
      ? allParties.filter(
          (partyPerson) => partyPerson.role === "Escrow Officer"
        )[0]
      : {},
  ];
  let showContactsOrder = [];
  if (transaction?.agentRepresents === "Buyer") {
    showContactsOrder.push(orderedPartiesBuyerSide);
    showContactsOrder.push(orderedPartiesSellerSide);
  } else if (transaction?.agentRepresents === "Seller") {
    showContactsOrder.push(orderedPartiesSellerSide);
    showContactsOrder.push(orderedPartiesBuyerSide);
  }

  return (
    <Grid className={isMobile ? "small padding" : "big padding"}>
      <Grid.Column width={16}>
        <div>
          <h3 key="ContactAddr">Contacts: {transaction?.address?.street}</h3>
          <Divider className="mini top margin mini bottom margin" />
          <h3 key="PrimaryAgentSide">{transaction?.agentRepresents} Side</h3>
          <Card.Group
            stackable
            className="medium top margin"
            key="PrimaryAgentGroupSide"
          >
            {agentMain?.fullName && (
              <Card
                color="blue"
                className="tiny top margin background-blue"
                key={transaction?.id}
              >
                <Card.Content extra>
                  <Card.Header>{agentMain?.fullName}</Card.Header>
                  <p>{transaction?.agentRepresents || ""} Agent</p>
                  <Card.Meta>
                    {transaction?.agentProfile?.address &&
                      transaction.agentProfile.address.street && (
                        <p>
                          Address:{" "}
                          {convertAddressFull(transaction.agentProfile.address)}
                        </p>
                      )}
                  </Card.Meta>
                  {agentMain.email && (
                    <Card.Meta>
                      <div className="tiny bottom margin small top margin">
                        <Icon name="mail" />
                        <span className="tiny left margin">
                          <a href={`mailto:${agentMain.email}`}>
                            {agentMain?.email}
                          </a>
                        </span>
                        <br />
                      </div>
                    </Card.Meta>
                  )}
                  {agentMain?.phone && (
                    <Card.Meta>
                      <div className="tiny bottom margin">
                        <Icon name="phone" />
                        <span className="tiny left margin">
                          <a
                            href={`tel:${convertPhoneToLink(agentMain.phone)}`}
                          >
                            {agentMain.phone}
                          </a>
                        </span>
                        <br />
                      </div>
                    </Card.Meta>
                  )}
                </Card.Content>

                {transaction?.assistant?.lastName && (
                  <>
                    <Card.Content extra>
                      <h4 className="tiny bottom margin">Assistant</h4>
                      <p>{convertFullName(transaction.assistant)}</p>
                      {transaction.assistant?.email && (
                        <div className="tiny bottom margin">
                          <Icon name="mail" />
                          <span className="tiny left margin">
                            <a href={`mailto:${transaction.assistant?.email}`}>
                              {transaction.assistant?.email}
                            </a>
                          </span>
                          <br />
                        </div>
                      )}
                      {transaction.assistant?.phone && (
                        <div className="tiny bottom margin">
                          <Icon name="phone" />
                          <span className="tiny left margin">
                            <a
                              href={`tel:${convertPhoneToLink(
                                transaction.assistant?.phone
                              )}`}
                            >
                              {transaction.assistant?.phone}
                            </a>
                          </span>
                          <br />
                        </div>
                      )}
                    </Card.Content>
                  </>
                )}
              </Card>
            )}

            {coAgentMine && coAgentMine[0] && (
              <PartyCard
                party={coAgentMine[0]}
                userViewerRole={userPartyRole}
                key="CoAgentMine"
              />
            )}

            {tcMine && tcMine[0] && (
              <PartyCard
                party={tcMine[0]}
                userViewerRole={userPartyRole}
                key="TcMine"
              />
            )}

            {transaction?.agentRepresents === "Buyer" &&
              orderedPartiesBuyerSide &&
              orderedPartiesBuyerSide.map((partyItem) =>
                partyItem ? (
                  <PartyCard
                    party={partyItem}
                    userViewerRole={userPartyRole}
                    key={partyItem.role}
                  />
                ) : (
                  ""
                )
              )}

            {transaction?.agentRepresents === "Seller" &&
              orderedPartiesSellerSide &&
              orderedPartiesSellerSide.map((partyItem) =>
                partyItem ? (
                  <PartyCard
                    party={partyItem}
                    userViewerRole={userPartyRole}
                    key={partyItem.role}
                  />
                ) : (
                  ""
                )
              )}
          </Card.Group>
          <Divider className="mini top margin mini bottom margin" />

          {transaction?.agentRepresents === "Buyer" && (
            <div key="SellerSidePeeps">
              <h3>Seller Side</h3>
              <Card.Group
                stackable
                className="medium top margin"
                key="SellerSideGroupies"
              >
                {sellerAgent && sellerAgent[0]?.lastName && (
                  <PartyCard
                    party={sellerAgent[0]}
                    userViewerRole={userPartyRole}
                    key={sellerAgent[0].role}
                  />
                )}
                {orderedPartiesSellerSide &&
                  orderedPartiesSellerSide.map((partyItem) =>
                    partyItem ? (
                      <PartyCard
                        party={partyItem}
                        userViewerRole={userPartyRole}
                        key={partyItem.role}
                      />
                    ) : (
                      ""
                    )
                  )}
              </Card.Group>
            </div>
          )}

          {transaction?.agentRepresents === "Seller" &&
            orderedPartiesBuyerSide?.length > 0 && (
              <div key="BuyerSidePeeps">
                <h3>Buyer Side</h3>
                <Card.Group
                  stackable
                  className="medium top margin"
                  key="BuyerSideGroupies"
                >
                  {buyerAgent && buyerAgent[0]?.lastName && (
                    <PartyCard
                      party={buyerAgent[0]}
                      userViewerRole={userPartyRole}
                      key={buyerAgent[0].role}
                    />
                  )}
                  {orderedPartiesBuyerSide &&
                    orderedPartiesBuyerSide.map((partyItem) =>
                      partyItem ? (
                        <PartyCard
                          party={partyItem}
                          userViewerRole={userPartyRole}
                          key={partyItem.role}
                        />
                      ) : (
                        ""
                      )
                    )}
                </Card.Group>
              </div>
            )}
        </div>
      </Grid.Column>
    </Grid>
  );
}
