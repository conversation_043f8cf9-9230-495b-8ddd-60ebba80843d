import React from "react";
import { Table, Button, Image, Icon } from "semantic-ui-react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { addHistoryToDb } from "../../../app/firestore/firestoreService";

export default function PartyAllListItem({ party, showClosingDate = false }) {
  const navigate = useNavigate();

  function handleViewDocuments() {
    addHistoryToDb(party.transactionId, party, "logged in to transaction");
    navigate(`/party/${party.id}/documents`);
  }

  // Format date for display
  const formatDate = (date) => {
    if (!date) return "";
    // Handle Firestore timestamp
    if (date.seconds) {
      return format(new Date(date.seconds * 1000), "MM/dd/yyyy");
    }
    // Handle regular Date object
    if (date instanceof Date) {
      return format(date, "MM/dd/yyyy");
    }
    return "";
  };

  return (
    <Table.Row key={party.id}>
      <Table.Cell style={{ padding: "0px" }}>
        {party.transactionPic ? (
          <Image style={{ width: "90px" }} src={party.transactionPic} />
        ) : (
          <Image
            src="/assets/placeholder-house.png"
            style={{ width: "90px" }}
            rounded
          />
        )}
      </Table.Cell>
      <Table.Cell>{party.address?.street}</Table.Cell>
      <Table.Cell>{party.agentRepresents}</Table.Cell>
      <Table.Cell>
        {party.agentFirstName || party.agentProfile?.firstName}
      </Table.Cell>
      <Table.Cell>
        {party.agentLastName || party.agentProfile?.lastName}
      </Table.Cell>
      <Table.Cell>{formatDate(party.updatedAt)}</Table.Cell>
      {showClosingDate && (
        <Table.Cell>{formatDate(party.closingDateTime)}</Table.Cell>
      )}
      <Table.Cell textAlign="center">
        {party.needsSignatures && (
          <Icon name="warning sign" color="orange" size="large" />
        )}
      </Table.Cell>
      <Table.Cell>
        <Button
          primary
          floated="right"
          onClick={() => handleViewDocuments()}
          content="View Documents"
        />
      </Table.Cell>
    </Table.Row>
  );
}
