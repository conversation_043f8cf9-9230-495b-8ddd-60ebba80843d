import React from "react";
import { useNavigate } from "react-router-dom";
import { Dropdown } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";

export default function PartyAllActionButtons({ party }) {
  const navigate = useNavigate();
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleGoToParty() {
    navigate(`/party/${party.id}/overview`);
  }

  return (
    <div className={isMobile ? null : "text align right"}>
      <Dropdown
        button
        icon="chevron down"
        text={isMobile ? "Actions " : null}
        className={isMobile ? "button icon" : "button mini icon"}
        direction={isMobile ? "right" : "left"}
      >
        <Dropdown.Menu>
          <Dropdown.Item
            onClick={() => handleGoToParty()}
          >
            View Documents
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    </div>
  );
}