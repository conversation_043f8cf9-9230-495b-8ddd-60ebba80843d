# Enhanced HelpPage Features

The HelpPage has been improved with several new features to provide a better user experience when searching for help content.

## New Features

### 1. Multiple Search Results
- When multiple help articles match a user's query, the system now displays all relevant results
- Each result is shown in a card format with the question as the header
- Users can see multiple potential answers instead of just the single best match

### 2. Expandable Content with "Show More" Button
- Long help content is now truncated to show only the first 3 lines by default
- A "Show More" button appears for content that exceeds the preview length
- Users can expand to see the full content and collapse it back with "Show Less"
- Content is considered expandable if it:
  - Has more than 3 lines
  - Has more than 200 characters
  - Contains images or videos

### 3. Image Support
- Help content can now include images using Markdown syntax
- Syntax: `![Alt text](image_url)`
- Images are automatically styled with:
  - Maximum width of 100%
  - Automatic height scaling
  - 10px margin top/bottom
  - 4px border radius for a polished look

### 4. YouTube Video Embedding
- Help content can embed YouTube videos directly
- Syntax: `[youtube](video_id)` or `[youtube](full_youtube_url)`
- Videos are embedded as responsive iframes with:
  - 100% width (max 560px)
  - 315px height
  - 4px border radius
  - Full YouTube player functionality

## Usage Examples

### Adding Images to Help Content
```
To upload a document, click the upload button shown below:

![Upload Button](https://example.com/upload-button.png)

Then select your file from the dialog.
```

### Adding YouTube Videos to Help Content
```
Watch this tutorial on how to sign documents:

[youtube](dQw4w9WgXcQ)

You can also use full YouTube URLs:

[youtube](https://www.youtube.com/watch?v=dQw4w9WgXcQ)
```

### Creating Expandable Content
Any help content that is longer than 3 lines or 200 characters will automatically become expandable:

```
This is a short answer that will display fully.

This is a much longer answer that explains the process in detail.
It has multiple paragraphs and provides comprehensive information.
Users will see a preview of this content with a "Show More" button.
When they click it, they'll see the full content including this text.
```

## Technical Implementation

### Components
- `ExpandableAnswer`: Handles content expansion/collapse and media parsing
- `SearchResults`: Displays multiple search results in card format
- Enhanced search algorithm that returns multiple matches instead of just one

### Content Processing
- Image markdown is converted to HTML `<img>` tags with responsive styling
- YouTube links are converted to embedded `<iframe>` elements
- Content expansion is determined by length, line count, and media presence

### Search Improvements
- Returns up to 5 matching results instead of just 1
- Prioritizes exact matches and keyword matches
- Sorts results by relevance score

## Testing
The functionality is covered by unit tests that verify:
- Image markdown processing
- YouTube embed processing
- Content expansion logic
- Search matching algorithm
- Database integration

Run tests with:
```bash
npm test -- --testPathPattern=HelpPage.test.js
```

## Future Enhancements
- Support for other video platforms (Vimeo, etc.)
- Image galleries with multiple images
- Interactive content elements
- Rich text formatting (bold, italic, lists)
- Search result highlighting
- Bookmarking favorite help articles
