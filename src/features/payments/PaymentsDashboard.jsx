import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import { useSelector } from "react-redux";
import { Table } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";

export default function PaymentsDashboard() {
  const { currentUserProfile } = useSelector((state) => state.profile);

  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  return (
    <div className="main-page-wrapper">
      <Grid centered stackable className="small bottom margin">
        <Grid.Column width={9}>
          <Header color="blue" as="h2">
            Payments
          </Header>
          <p color="grey">Subscription Payments for TransActioner software.</p>

          <>
          {currentUserProfile?.paymentsDiscount === "VIPx5" && (
          <>
          <h2>VIP Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As a VIP, you have the option to set up recurring annual payments for a VIP discount.
                <br/>
                You can cancel your recurring payments at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/9B628q4WG26geqe6cB5kk0i"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay $195 for 1 year and sets up automatic recurring payments on an annual basis - guaranteed price until 2030!
              </a>{" "}
            </p>
            </>
            )}
          {currentUserProfile?.paymentsDiscount === "MULTIAGENT5" && (
          <>
          <h2>VIP Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As a VIP, you have the option to set up recurring annual payments for a VIP discount.
                <br/>
                You can cancel your recurring payments at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/3cIbJ0ah04eo6XM0Sh5kk0g"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay $175 for 1 year and sets up automatic recurring payments on an annual basis.
              </a>{" "}
            </p>
            </>
            )}
          {currentUserProfile?.brokerageForms === "SHERPA" && currentUserProfile.paymentsDiscount === "SHERPA" && (
          <>
          <h2>Sherpa Brokerage Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As an agent with Sherpa Real Estate, you have the option to set up recurring annual payments for a discount.
                <br/>
                You can cancel your recurring payment on Stripe at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/4gMbJ04WG26ggymcAZ5kk0k"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Subscribe for $165 per year and sets up automatic recurring payments on an annual basis.
              </a>{" "}
            </p>
            </>
            )}
            <h2>One-Time Payment Annual Plan</h2>

            <p color="grey">
              <i>
                For the regular annual plan, you can pay a one-time amount and then will need to pay again each year.
                <br />
                <i> 
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/7sI3cB7ZUg3L6TC4gh"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay $199 for 1 year one-time payment.
              </a>{" "}
            </p>
            <h2>Monthly Plan</h2>
            <p color="grey">
              
              <a
                href="https://buy.stripe.com/3cs7sR2FAbNv3Hq3ce"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay monthly instead at $19/month.
              </a>
              <br /> <br />
            </p>
          </>
          <Segment clearing>
            <Grid.Column width={4}>
              <div>
                <Header color="blue" as="h2">
                  Payment History
                </Header>
                {!currentUserProfile?.payments &&
                  currentUserProfile.role === "tc" && (
                    <>
                      TransActioner is currently free for Transaction
                      Coordinators.
                    </>
                  )}
                {!currentUserProfile?.payments &&
                  currentUserProfile.role !== "tc" && (
                    <>
                      Your History of Payments is Coming Soon....
                      <br />
                    </>
                  )}
                <Table compact>
                  <Table.Header className="mobile hidden">
                    <Table.Row
                      key="tableHeadingsForPayments"
                      className="small-header"
                    >
                      <Table.HeaderCell>Amount</Table.HeaderCell>
                      <Table.HeaderCell> Date Paid</Table.HeaderCell>
                      <Table.HeaderCell>Date Expires</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {currentUserProfile?.payments?.map((payMap) => (
                      <Table.Row key={payMap.dateExpires.seconds}>
                        <Table.Cell>${payMap.amount} </Table.Cell>
                        <Table.Cell>
                          <>
                            {isMobile ? <>Date Paid: </> : ""}
                            {payMap.datePayment?.toDate()?.toDateString()}
                          </>
                        </Table.Cell>
                        <Table.Cell>
                          {isMobile ? <>Date Expires: </> : ""}
                          {payMap.dateExpires?.toDate()?.toDateString() || ""}
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </Grid.Column>
            <Divider hidden />
          </Segment>
        </Grid.Column>
      </Grid>
    </div>
  );
}
