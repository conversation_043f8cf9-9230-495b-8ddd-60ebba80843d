import React from "react";
import { useSelector } from "react-redux";
import { NavLink, useLocation, useNavigate, Link } from "react-router-dom";
import { Image, Menu, Dropdown } from "semantic-ui-react";
import { signOutFirebase } from "../../app/firestore/firebaseService";

export default function NavBarPartyAll() {
  const location = useLocation();
  const navigate = useNavigate();
  const { authenticated } = useSelector((state) => state.auth);
  const { currentUserProfile } = useSelector((state) => state.profile);

  function handleSignOut() {
    signOutFirebase();
    navigate(`/partyLogin`);
  }

  return (
    <>
      <Menu secondary stackable className="main-menu">
        <Menu.Item header style={{ marginLeft: "0px", paddingLeft: "0px" }}>
          <Image src="/assets/logo-color-with-text-small.png" />
        </Menu.Item>
        {authenticated && (
          <>
            <Menu.Item
              as={NavLink}
              active={location.pathname.includes("overview")}
              to={`/overviewParty`}
              name="All Transactions"
            />
            <Menu.Item position="right">
              <Dropdown
                pointing="top left"
                direction="left"
                text={currentUserProfile?.lastName ? (
                  currentUserProfile?.firstName +
                  " " +
                  currentUserProfile?.lastName
                ) : "Account"
                }
              >
                <Dropdown.Menu>
                  <Dropdown.Item
                    as={Link}
                    to="/partyAccount"
                    text="My account"
                    icon="sun outline"
                  />
                  <Dropdown.Item
                    onClick={handleSignOut}
                    text="Sign Out"
                    icon="power"
                  />
                </Dropdown.Menu>
              </Dropdown>
            </Menu.Item>
          </>
        )}
      </Menu>
    </>
  );
}