import React from "react";
import { Table } from "semantic-ui-react";
import _ from "lodash";
import { startOfDay, subDays } from "date-fns";
import ManagerUpcomingClosingsListItem from "./ManagerUpcomingClosingsListItem";

export default function ManagerUpcomingClosingsList({ transactions }) {
  const closingTransactions = transactions.filter(
    (transaction) =>
      transaction.closingDateTime &&
      transaction.closingDateTime > startOfDay(subDays(new Date(), 2))
  );
  const closingTransactionsSorted = _.orderBy(
    closingTransactions,
    "closingDateTime",
    "asc"
  );

  if (!closingTransactions || closingTransactions.length === 0) return null;

  return (
    <Table compact>
      <Table.Header className="mobile hidden">
        <Table.Row className="small-header">
          <Table.HeaderCell></Table.HeaderCell>
          <Table.HeaderCell>Closing Date</Table.HeaderCell>
          <Table.HeaderCell>Agent</Table.HeaderCell>
          <Table.HeaderCell>Representing</Table.HeaderCell>
          <Table.HeaderCell>Address</Table.HeaderCell>
          <Table.HeaderCell>CDA Complete</Table.HeaderCell>
          <Table.HeaderCell></Table.HeaderCell>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {closingTransactionsSorted.map((transaction) => (
          <ManagerUpcomingClosingsListItem
            transaction={transaction}
            key={transaction.id}
          />
        ))}
      </Table.Body>
    </Table>
  );
}
