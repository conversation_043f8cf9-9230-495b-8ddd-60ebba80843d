import React from "react";
import { useDispatch } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Segment,
  Grid,
} from "semantic-ui-react";

import { useMediaQuery } from "react-responsive";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import MyTextInput from "../../../../app/common/form/MyTextInput";
// import MyCheckbox from "../../../app/common/form/MyCheckbox";
import MyTextArea from "../../../../app/common/form/MyTextArea";
import MySelectInput from "../../../../app/common/form/MySelectInput";
import { updateProfileFieldsInDb } from "../../../../app/firestore/firestoreService";
import { toast } from "../../../../../node_modules/react-toastify/dist/index";


export default function ManagerAgentFinancialsEditForm({
  agent,
  brokerageFees,
}) {
  const dispatch = useDispatch();
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  // let initialValues = agent;
  let initialValues = {
    fees: {
      agentPayToName: "",
      feeBrokerageFeeAmount: "",
      feeBrokerageFeeType: "",
      feeFranchiseFeeAmount: "",
      feeFranchiseFeeType: "",
      feeManagerFeeAmount: "",
      feeManagerFeeType: "",
      feeMentorFeeAmount: "",
      feeMentorFeeType: "",
      feeTransactionFeeAmount: "",
      feeTransactionWhoPays: "",
      feeTeamName: "",
      feeTeamSplitAmount: "",
      feeTeamSplitType: "",
      feeDeskAmount: "",
      feeDeskFreq: "",
      notesForManager: "",
      ...agent.fees,
    },
  };
  const validationSchema = Yup.object({
    // lastName: Yup.string().required("You must provide a last name"),
  });

  let amountType = [
    {
      key: "",
      value: "",
      text: "",
    },
    {
      key: "dollars",
      value: "dollars",
      text: "$",
    },
    {
      key: "percent",
      value: "percent",
      text: "%",
    },
  ];

  return (
    <ModalWrapper>
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Header size="large" color="blue">
            Agent Financials : {agent?.firstName} {agent?.lastName}
          </Header>
          &nbsp; {agent.email} &nbsp; {agent.phone}
          <Grid centered stackable className="small bottom margin">
            <Grid.Column width={14}>
              <Segment clearing>
                <Formik
                  enableReinitialize
                  initialValues={initialValues}
                  validationSchema={validationSchema}
                  validateOnChange={false}
                  validateOnBlur={false}
                  onSubmit={async (values, { setSubmitting }) => {
                    try {
                      await updateProfileFieldsInDb(agent.id, values);
                      setSubmitting(false);
                      toast.success("Agent successfully updated");
                    } catch (error) {
                      toast.error(error.message);
                      setSubmitting(false);
                    }
                  }}
                >
                  {({ isSubmitting, dirty, isValid }) => (
                    <Form className="ui form medium margin bottom">
                      <Grid>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={8}>
                            <MyTextInput
                              name={"fees.agentPayToName"}
                              label="Agent's Payable To"
                              // defaultValue={agent?.fees?.agentPayToName || ""}
                            />
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      <Header color="blue">Brokerage Fees</Header>
                      <Grid>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={5}>
                            <MyTextInput
                              name={"fees.feeBrokerageFeeAmount"}
                              label="Brokerage Fee Amount"
                              // value={agent?.fees?.feeBrokerageFeeAmount ?? ""}
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.feeBrokerageFeeType"}
                              options={amountType}
                              label="Brokerage Fee % or $"
                            ></MySelectInput>
                          </Grid.Column>
                        </Grid.Row>
                        {brokerageFees?.hasFranchiseFee && (
                          <>
                            <Grid.Row>
                              <Grid.Column mobile={16} computer={5}>
                                <MyTextInput
                                  name={"fees.feeFranchiseFeeAmount"}
                                  label="Franchise Fee Amount"
                                  // value={agent?.fees?.feeFranchiseFeeAmount ?? ""}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={5}>
                                <MySelectInput
                                  name={"fees.feeFranchiseFeeType"}
                                  options={amountType}
                                  label="Franchise Fee % or $"
                                ></MySelectInput>
                              </Grid.Column>
                            </Grid.Row>
                          </>
                        )}
                        {brokerageFees?.hasManagerFee && (
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={5}>
                              <MyTextInput
                                name={"fees.feeManagerFeeAmount"}
                                label="Manager Fee Amount"
                                // value={agent?.fees?.feeManagerFeeAmount ?? ""}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={5}>
                              <MySelectInput
                                name={"fees.feeManagerFeeType"}
                                options={amountType}
                                label="Manager Fee % or $"
                              ></MySelectInput>
                            </Grid.Column>
                          </Grid.Row>
                        )}
                        {brokerageFees?.hasMentorFee && (
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={5}>
                              <MyTextInput
                                name={"fees.feeMentorFeeAmount"}
                                label="Mentor Fee Amount"
                                // value={agent?.fees?.feeMentorFeeAmount ?? ""}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={5}>
                              <MySelectInput
                                name={"fees.feeMentorFeeType"}
                                options={amountType}
                                label="Mentor Fee % or $"
                              ></MySelectInput>
                            </Grid.Column>
                          </Grid.Row>
                        )}
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={5}>
                            <MyTextInput
                              name={"fees.feeTransactionFeeAmount"}
                              label="Transaction Fee Amount"
                              // value={
                              //   agent?.fees?.feeTransactionFeeAmount ?? ""
                              // }
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.feeTransactionFeeType"}
                              options={amountType}
                              label="Transaction Fee % or $"
                            ></MySelectInput>
                          </Grid.Column>

                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.feeTransactionWhoPays"}
                              label="Who Pays Transaction Fee"
                              options={[
                                {
                                  key: "",
                                  text: "",
                                  value: "",
                                },
                                {
                                  key: "agent",
                                  text: "agent",
                                  value: "agent",
                                },
                                {
                                  key: "client",
                                  text: "client",
                                  value: "client",
                                },

                                {
                                  key: "other",
                                  text: "other",
                                  value: "other",
                                },
                              ]}
                            ></MySelectInput>
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      <Divider />
                      <Header color="blue">Team Fees</Header>
                      <Grid stackable>
                        <Grid.Row>
                          <Grid.Column width={5}>
                            <MyTextInput
                              name="fees.feeTeamName"
                              label="Team Name"
                              // value={agent?.fees?.feeTeamName ?? ""}
                            />
                          </Grid.Column>
                        </Grid.Row>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={5}>
                            <MyTextInput
                              name={"fees.feeTeamSplitAmount"}
                              label="Team Split Amount"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.feeTeamSplitType"}
                              options={amountType}
                              label="Team Split % or $"
                            ></MySelectInput>
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      <br />
                      <Divider />
                      <Header color="blue">Misc. Fees</Header>
                      <Grid stackable>
                        {brokerageFees?.hasDonationFee && (
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={4}>
                              <MyTextInput
                                name={"fees.feeDonationFeeAmount"}
                                label="Donation Fee Amount"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={5}>
                              <MySelectInput
                                name={"fees.feeDonationFeeType"}
                                options={amountType}
                                label="Donation Fee % or $"
                              ></MySelectInput>
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={6}>
                              <MyTextInput
                                name={"fees.feeDonationPayName"}
                                label="Donation Pay To"
                              />
                            </Grid.Column>
                          </Grid.Row>
                        )}
                        {brokerageFees?.hasDeskFee && (
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={5}>
                              <MyTextInput
                                name={"fees.feeDeskAmount"}
                                label="Desk/Office Fee Amount"
                                // value={agent?.fees?.feeDeskAmount ?? ""}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={5}>
                              <MySelectInput
                                name={"fees.feeDeskFreq"}
                                options={[
                                  {
                                    key: "",
                                    text: "",
                                    value: "",
                                  },
                                  {
                                    key: "monthly",
                                    text: "monthly",
                                    value: "monthly",
                                  },
                                  {
                                    key: "annually",
                                    text: "annually",
                                    value: "annually",
                                  },
                                ]}
                                label="Desk/Office Fee Frequency"
                              ></MySelectInput>
                            </Grid.Column>
                          </Grid.Row>
                        )}
                      </Grid>
                      <Divider />

                      <Divider />
                      <Header color="blue">Notes</Header>
                      <Grid stackable>
                        <Grid.Row>
                          <Grid.Column width={12}>
                            <MyTextArea
                              name="fees.notesForManager"
                              label="Manager Notes"
                              // defaultValue={agent?.fees?.notesForManager ?? ""}
                            />
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      <br />
                      <br />
                      <Button
                        loading={isSubmitting}
                        disabled={!dirty || isSubmitting}
                        type="submit"
                        floated={isMobile ? "left" : "right"}
                        primary
                        content="Submit"
                        className={isMobile ? "fluid large" : "large"}
                      />
                    </Form>
                  )}
                </Formik>
              </Segment>
            </Grid.Column>
          </Grid>
        </div>
        <Divider hidden />
        <br />
        <Button
          onClick={() =>
            dispatch(
              closeModal({
                modalType: "ManagerAgentFinancialsEditForm",
              })
            )
          }
          type="button"
          floated={isMobile ? null : "right"}
          content="Close"
          className={isMobile ? "fluid medium bottom margin" : null}
        />
      </Segment>
    </ModalWrapper>
  );
}
