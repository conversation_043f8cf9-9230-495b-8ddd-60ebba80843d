import React from "react";
import { Table } from "semantic-ui-react";
import ManagerAgentFinancialsActionButtons from "./ManagerAgentFinancialsActionButtons";
import { Icon, Popup } from "semantic-ui-react";

export default function ManagerAgentsFinancialsListItem({
  agent,
  brokerageFees,
}) {
  function convertAmountToIncludeType(amount, type) {
    if (!amount || !type) return "";
    if (type === "percent") {
      return amount + "%";
    } else if (type === "dollars") {
      return "$" + amount;
    }
    return "";
  }
  function convertAmountToFreq(amount, freq) {
    if (!amount || !freq) return "";
    return "$" + amount + (freq === "monthly" ? "/mo" : "/yr");
  }
  return (
    <Table.Row key={agent.id}>
      <Table.Cell>
        <>{agent.firstName}</>
      </Table.Cell>
      <Table.Cell>
        <>
          {agent.lastName} &nbsp;
          {agent.fees?.notesForManager && (
            <>
              <Popup
                flowing
                size="large"
                trigger={
                  <Icon
                    name="info"
                    color="blue"
                    circular
                    inverted
                    size="tiny"
                    style={{ marginLeft: "3px", marginBottom: "4px" }}
                  />
                }
              >
                <p className="text-dark text-normal small vertical margin">
                  {agent.fees?.notesForManager}
                </p>
              </Popup>{" "}
            </>
          )}
        </>
      </Table.Cell>
      <Table.Cell>
        {convertAmountToIncludeType(
          agent.fees?.feeBrokerageFeeAmount,
          agent.fees?.feeBrokerageFeeType
        )}
      </Table.Cell>
      {brokerageFees?.hasFranchiseFee && (
        <Table.Cell>
          {convertAmountToIncludeType(
            agent.fees?.feeFranchiseFeeAmount,
            agent.fees?.feeFranchiseFeeType
          )}
        </Table.Cell>
      )}
      {brokerageFees?.hasManagerFee && (
        <Table.Cell>
          {convertAmountToIncludeType(
            agent.fees?.feeManagerFeeAmount,
            agent.fees?.feeManagerFeeType
          )}
        </Table.Cell>
      )}
      {brokerageFees?.hasMentorFee && (
        <Table.Cell>
          {convertAmountToIncludeType(
            agent.fees?.feeMentorFeeAmount,
            agent.fees?.feeMentorFeeType
          )}
        </Table.Cell>
      )}
      {brokerageFees?.hasTransactionFee && (
        <Table.Cell>
          {convertAmountToIncludeType(
            agent.fees?.feeTransactionFeeAmount,
            agent.fees?.feeTransactionFeeType
          )}
        </Table.Cell>
      )}

      {brokerageFees?.hasTeamFee && (
        <>
          <Table.Cell>
            {convertAmountToIncludeType(
              agent.fees?.feeTeamSplitAmount,
              agent.fees?.feeTeamSplitType
            )}
          </Table.Cell>
          <Table.Cell>
            {convertAmountToFreq(
              agent.fees?.feeDeskAmount,
              agent.fees?.feeDeskFreq
            )}
          </Table.Cell>
        </>
      )}
      {brokerageFees?.hasDeskFee && (
        <>
          <Table.Cell>
            $110/mo
            {/* {convertAmountToFreq(
              agent.fees?.feeDeskFeeAmount,
              agent.fees?.feeDeskFeeFreq
            )} */}
          </Table.Cell>
        </>
      )}

      <Table.Cell>
        <ManagerAgentFinancialsActionButtons agent={agent} />
      </Table.Cell>
    </Table.Row>
  );
}