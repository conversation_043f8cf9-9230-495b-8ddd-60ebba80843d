import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Grid, Table, Input, Button } from "semantic-ui-react";
import { searchFilter } from "../../../../app/common/util/util";
// import { Link } from "react-router-dom";
import { sortAgentsForManagerActive } from "../../../profiles/profileSlice";
import ManagerAgentsFinancialsListItem from "./ManagerAgentsFinancialsListItem";
import { Link } from "../../../../../node_modules/react-router-dom/dist/index";

export default function ManagerAgentsFinancialsList() {
  const { currentUserProfile } = useSelector((state) => state.profile);

  const dispatch = useDispatch();
  const { agentsForManagerActive } = useSelector((state) => state.profile);
  const [searchTerms, setSearchTerms] = useState("");
  const agents = searchFilter(agentsForManagerActive?.agents, searchTerms);

  function handleSort(column) {
    dispatch(sortAgentsForManagerActive(column));
  }

  const column = agentsForManagerActive?.column;
  const direction = agentsForManagerActive?.direction;

  return (
    // <div className="main-page-wrapper">
    <>
      {currentUserProfile?.managerAccess?.hasAccessToFinancialsDashboard ? (
        <>
          <Grid stackable className="small bottom margin">
            <Grid.Row>
              <Grid.Column computer={5}>
                <Input
                  type="text"
                  fluid
                  placeholder="Search by name"
                  value={searchTerms}
                  onChange={(e) => setSearchTerms(e.target.value)}
                ></Input>
              </Grid.Column>

              <Grid.Column computer={3} tablet={4}>
                <Button.Group fluid size="small">
                  <Button as={Link} to={`/financialsManagerUpcoming/`}>
                    Upcoming Closings
                  </Button>
                  <Button active as={Link} to="">
                    Agent List
                  </Button>
                </Button.Group>
              </Grid.Column>
            </Grid.Row>
          </Grid>
          <Grid>
            <Grid.Row>
              <Grid.Column computer={16} className="small bottom margin">
                <h3 className="zero bottom margin">Agent List</h3>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column>
                <p>
                  <i>Show or hide fee fields in your Profile page.</i>
                </p>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column computer={16} className="small bottom margin">
                <Table compact sortable className="mini top margin">
                  <Table.Header className="mobile hidden">
                    <Table.Row className="small-header">
                      <Table.HeaderCell
                        sorted={column === "title" ? direction : null}
                        onClick={() => handleSort("First Name")}
                      >
                        First Name
                      </Table.HeaderCell>
                      <Table.HeaderCell style={{ cursor: "default" }}>
                        Last Name
                      </Table.HeaderCell>
                      <Table.HeaderCell
                        sorted={
                          column === "fees.feeBrokerageFeeAmount"
                            ? direction
                            : null
                        }
                        onClick={() => handleSort("fees.feeBrokerageFeeAmount")}
                      >
                        Brokerage Fee
                      </Table.HeaderCell>
                      {currentUserProfile.brokerage?.hasFranchiseFee && (
                        <>
                          <Table.HeaderCell
                            sorted={
                              column === "fees.feeFranchiseFeeAmount"
                                ? direction
                                : null
                            }
                            onClick={() =>
                              handleSort("fees.feeFranchiseFeeAmount")
                            }
                          >
                            Franchise Fee
                          </Table.HeaderCell>
                        </>
                      )}
                      {currentUserProfile.brokerage?.hasManagerFee && (
                        <>
                          <Table.HeaderCell
                            sorted={
                              column === "fees.feeManagerFeeAmount"
                                ? direction
                                : null
                            }
                            onClick={() =>
                              handleSort("fees.feeManagerFeeAmount")
                            }
                          >
                            Manager Fee
                          </Table.HeaderCell>
                        </>
                      )}
                      {currentUserProfile.brokerage?.hasMentorFee && (
                        <>
                          <Table.HeaderCell>Mentor Fee</Table.HeaderCell>
                        </>
                      )}
                      {currentUserProfile.brokerage?.hasTransactionFee && (
                        <Table.HeaderCell>Transaction Fee</Table.HeaderCell>
                      )}
                      {currentUserProfile.brokerage?.hasTeamFee && (
                        <Table.HeaderCell>Team Split</Table.HeaderCell>
                      )}
                      {currentUserProfile.brokerage?.hasDeskFee && (
                        <Table.HeaderCell>Desk Fees</Table.HeaderCell>
                      )}
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {agents &&
                      agents.length !== 0 &&
                      agents.map((agent) => (
                        <ManagerAgentsFinancialsListItem
                          agent={agent}
                          brokerageFees={currentUserProfile.brokerage}
                          key={agent.id}
                        />
                      ))}
                  </Table.Body>
                </Table>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        </>
      ) : (
        <>
          <p>Contact TransActioner to subscribe to Manager Financials.</p>
        </>
      )}
    </>
    // </div>
  );
}