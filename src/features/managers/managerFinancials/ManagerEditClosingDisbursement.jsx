import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import {
  <PERSON><PERSON>,
  Divider,
  Header,
  Segment,
  Grid,
  Icon,
  Popup,
} from "semantic-ui-react";

import { useMediaQuery } from "react-responsive";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { Form, Formik, useFormikContext } from "formik";
import * as Yup from "yup";
import MyTextInput from "../../../app/common/form/MyTextInput";
import MyTextArea from "../../../app/common/form/MyTextArea";
import MySelectInput from "../../../app/common/form/MySelectInput";
import { toast } from "../../../../node_modules/react-toastify/dist/index";
import { updateTransFieldsInDb } from "../../../app/firestore/firestoreService";
import { convertFullName } from "../../../app/common/util/util";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import { addFormsToTransaction } from "../../../app/firestore/firebaseService";

// import { values } from "lodash";

export default function ManagerEditClosingDisbursement({
  transaction,
  agent,
  brokerageDetails,
}) {
  const dispatch = useDispatch();
  const { forms } = useSelector((state) => state.doc);

  // const [formFieldData, setFormFieldData] = useState();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { agentsForManagerActive } = useSelector((state) => state.profile);
    const navigate = useNavigate();


  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  if (!agentsForManagerActive) {
    return <LoadingComponent />;
  }

  const agentProf = agentsForManagerActive?.agents?.filter(
    (ag) => ag.id === agent.id
  );
  const agentProfile = agentProf ? agentProf[0] : null;
  let coAgentProfile = transaction?.coAgent?.userId
    ? agentsForManagerActive?.agents?.filter(
        (ag) => ag.id === transaction.coAgent.userId
      )[0]
    : null;
  const isCoAgentOneOfMyAgents =
    coAgentProfile?.managerId === currentUserProfile.id;

    if (!coAgentProfile && transaction?.coAgent) {
      coAgentProfile = transaction?.coAgent;
    }

  function getTableHeader() {
    return (
      <Grid.Row>
        <Grid.Column mobile={16} computer={colSize.one}>
          <b> Description</b>
        </Grid.Column>
        <Grid.Column mobile={16} computer={colSize.two}>
          <b>Payable To</b>
        </Grid.Column>
        <Grid.Column mobile={16} computer={colSize.three}>
          <>
            <b>% Amount</b>{" "}
            <Popup
              flowing
              size="small"
              trigger={
                <Icon
                  name="info"
                  color="blue"
                  circular
                  inverted
                  size="tiny"
                  style={{
                    marginLeft: "3px",
                    marginBottom: "3px",
                  }}
                />
              }
            >
              <p className="bold text blue mini bottom margin">
                Fill out either the % (the system will auto-calculate the
                amount) <br />
                or fill out the $ amount (and ensure the % is blank).
                <br />
                <i>Make sure the "Payable To" is also filled out.</i>
              </p>

              {/* <p className="text-dark text-normal mini bottom margin">
                                  You can define the order in which the money is
                                  allocated inside your Profile.
                                </p> */}
            </Popup>
          </>
        </Grid.Column>
        <Grid.Column mobile={16} computer={colSize.four}>
          <b>$ Amount</b>
        </Grid.Column>
        <Grid.Column mobile={16} computer={colSize.five}>
          <>
            <b>Balance</b>

            <Popup
              flowing
              size="small"
              trigger={
                <Icon
                  name="info"
                  color="blue"
                  circular
                  inverted
                  size="tiny"
                  style={{
                    marginLeft: "3px",
                    marginBottom: "3px",
                  }}
                />
              }
            >
              <p className="bold text blue mini bottom margin">
                Balance shows the amount of compensation remaining
                <br />
                after the line item is applied.
              </p>

              {/* <p className="text-dark text-normal mini bottom margin">
                                  You can define the order in which the money is
                                  allocated inside your Profile.
                                </p> */}
            </Popup>
          </>
        </Grid.Column>
      </Grid.Row>
    );
  }
  function getPayToCoAgent(values) {
    return (
      (values?.financials?.CoAgentPayToPayAmount || 0) -
      (brokerageDetails?.hasManagerFee
        ? values?.financials?.coAgentManagerPayAmount || 0
        : 0) -
      (brokerageDetails?.hasDonationFee
        ? values?.financials?.coAgentDonationPayAmount || 0
        : 0)
    );
  }
  function getAsNumber(amount) {
    return amount && !isNaN(amount) ? Number(amount) : 0;
  }
  function getPayToAgent(values) {
    const credit =
      values.fees?.creditGoesTo === "agent"
        ? getAsNumber(values?.fees?.creditAmount)
        : values?.fees?.creditPaidBy === "agent"
        ? -getAsNumber(values.fees?.creditAmount)
        : 0;
    const transactionFee =
      brokerageDetails?.hasTransactionFee &&
      values?.fees?.feeTransactionWhoPays === "agent"
        ? getAsNumber(values?.fees?.feeTransactionFeeAmount)
        : 0;

    return (
      getAsNumber(values?.financials?.AgentPayAmount) -
      transactionFee -
      (brokerageDetails?.hasManagerFee
        ? getAsNumber(values?.financials?.ManagerPayAmount)
        : 0) -
      (brokerageDetails?.hasDonationFee
        ? getAsNumber(values?.financials?.DonationPayAmount)
        : 0) -
      (transaction?.tcId ? getAsNumber(values?.financials?.TcPayAmount) : 0) +
      credit -
      getAsNumber(values?.fees?.debitAmount)
    );
  }
  function renderCDAListOfPayouts() {
    if (!cda) return "";
    const listItems = [];
    for (let i = 1; i <= 10 && cda["text" + i]?.length > 4; i++) {
      listItems.push(<li key={i}>{cda["text" + i]}</li>);
    }
    return listItems;
  }

  function parseCurrency(currencyString) {
    const cleanedString = currencyString.replace(/[^\d.-]/g, "");
    const number = parseFloat(cleanedString);
    if (isNaN(number)) {
      return null;
    }
    return number;
  }

  const currencyFormatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  });

  const colSize = { one: 3, two: 4, three: 3, four: 3, five: 3 };
  // const feeDescription = [
  //   {
  //     key: "",
  //     value: "",
  //     text: "",
  //   },
  //   {
  //     key: "Referral",
  //     value: "Referral",
  //     text: "Referral",
  //   },
  //   {
  //     key: "Brokerage",
  //     value: "Brokerage",
  //     text: "Brokerage",
  //   },
  //   {
  //     key: "Franchise",
  //     value: "Franchise",
  //     text: "Franchise",
  //   },
  //   {
  //     key: "Donation",
  //     value: "Donation",
  //     text: "Donation",
  //   },
  //   {
  //     key: "Credit",
  //     value: "Credit",
  //     text: "Credit",
  //   },
  // ];

  const amountType = [
    {
      key: "",
      value: "",
      text: "",
    },
    {
      key: "dollars",
      value: "dollars",
      text: "$",
    },
    {
      key: "percent",
      value: "percent",
      text: "%",
    },
  ];

  function resetCdaText() {
    for (let i = 1; i <= 10; i++) {
      cda["text" + i] = "";
    }
  }
  function resetBalance(setFieldValue) {
    for (var key in balance) {
      balance[key] = 0;
      setFieldValue(balance[key], "");
    }
  }

  let cda = {
    text1: "",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
    compensationPayAmount: "",
    specialInstructions: brokerageDetails?.cdaDefaultSpecialInstructions || "",
  };

  let compensation =
    (parseCurrency(transaction?.salesPrice) || 0) &&
    transaction?.compensation?.compensationAmount &&
    transaction.copensation?.compensationType === "percent"
      ? (parseCurrency(transaction.salesPrice) *
          transaction.compensationAmount) /
        100
      : getAsNumber(transaction?.compensation?.compensationAmount);

  let balance = {
    compensation: 0,
    Referral: 0,
    Other: 0,
    CoAgent: 0,
    Brokerage: 0,
    Manager: 0,
    Transaction: 0,
    Donation: 0,
    Tc: 0,
    Agent: 0,
  };

  let initialValues = {
    balance: {
      compensation: "",
      OtherSide: "",
      Referral: "",
      CoAgent: "",
      Brokerage: "",
      Manager: "",
      Transaction: "",
      Donation: "",
      Tc: "",
      Agent: "",
    },
    cda: {
      text1: "",
      text2: "",
      text3: "",
      text4: "",
      text5: "",
      text6: "",
      text7: "",
      text8: "",
      text9: "",
      text10: "",
      compensationPayAmount: "",
      specialInstructions:
        brokerageDetails?.cdaDefaultSpecialInstructions || "",
    },
    financials: {
      salesPrice: transaction?.salesPrice || "",
      compensationAmount: transaction?.compensation?.compensationAmount || "",
      compensationType: transaction?.compensation?.compensationType || "",
      compensationPayAmount: compensation,
      // otherPayName: "",
      // OtherPayAmount: "",
      referralPayName: "",
      ReferralPayAmount: "",

      agentPayToName:
        agentProfile?.fees?.agentPayToName ||
        convertFullName(agentProfile) ||
        "",
      AgentPayAmount: "",
      coAgentPayToName: coAgentProfile ? convertFullName(coAgentProfile) : "",
      CoAgentPayAmount: "",
      coAgentPayToBrokerageName: "",
      CoAgentBrokeragePayAmount: "",
      coAgentManagerPayName: brokerageDetails?.hasManagerFee
        ? convertFullName(currentUserProfile)
        : "",
      coAgentManagerPayAmount: brokerageDetails?.hasManagerFee
        ? coAgentProfile?.fees?.feeManagerFeeAmount
        : "",
      coAgentDonationPayName: brokerageDetails?.hasDonationFee
        ? coAgentProfile?.fees?.feeDonationPayName
        : "",
      coAgentDonationPayAmount:
        brokerageDetails?.hasDonationFee &&
        coAgentProfile?.fees?.feeDonationFeeType === "dollars"
          ? coAgentProfile?.fees?.feeDonationFeeAmount
          : "",

      brokeragePayName: agentProfile?.brokerageName || "",

      BrokeragePayAmount:
        agentProfile?.fees?.feeBrokerageFeeType === "dollars"
          ? agentProfile?.fees?.feeBrokerageFeeAmount
          : "",
      ManagerPayAmount:
        brokerageDetails?.hasManagerFee &&
        agentProfile?.fees?.feeManagerFeeType === "dollars"
          ? agentProfile?.fees?.feeManagerFeeAmount
          : "",
      managerPayName: brokerageDetails?.hasManagerFee
        ? convertFullName(currentUserProfile)
        : "",
      TransactionPayAmount: "",
      DonationPayAmount:
        agentProfile?.fees?.feeDonationFeeType === "dollars"
          ? agentProfile?.fees?.feeDonationFeeAmount
          : "",
      donationPayName: brokerageDetails?.hasDonationFee
        ? agentProfile?.fees?.feeDonationPayName
        : "",
      tcPayName: "",
      TcPayAmount: "",
    },
    fees: {
      agentFullName: agentProfile ? convertFullName(agentProfile) : "",
      agentPayAmount: "",
      feeAgentFeePercent:
        agentProfile?.fees?.feeBrokerageFeeType === "percent"
          ? 100 - getAsNumber(agentProfile?.fees?.feeBrokerageFeeAmount)
          : "",

      coAgentFeeAmount: "",
      feeCoAgentBrokeragePayToFeePercent: "",
      feeCoAgentFeePercent: "",
      coAgentFeeManagerFeePercent:
        brokerageDetails?.hasManagerFee &&
        coAgentProfile?.fees?.feeManagerFeeType === "percent"
          ? coAgentProfile?.fees?.feeManagerFeeAmount
          : "",
      coAgentFeeDonationFeePercent:
        brokerageDetails?.hasDonationFee &&
        coAgentProfile?.fees?.feeDonationFeeType === "percent"
          ? coAgentProfile.fees.feeDonationFeeAmount
          : "",
      feeOtherSideFeePercent: "",
      feeReferralFeeAmount: transaction?.fees?.feeReferralFeeAmount || "",
      feeBrokerageFeeAmount: agentProfile?.fees?.feeBrokerageFeeAmount || "",
      feeBrokerageFeePercent:
        agentProfile?.fees?.feeBrokerageFeeType === "percent"
          ? agentProfile?.fees?.feeBrokerageFeeAmount
          : "",
      feeBrokerageFeeType: agentProfile?.fees?.feeBrokerageFeeType || "",
      feeFranchisePayName: "",
      feeFranchiseFeePercent:
        brokerageDetails?.hasFranchiseFee &&
        agentProfile?.fees?.feeFranchiseFeeType === "percent"
          ? agentProfile?.fees?.feeFranchiseFeeAmount
          : "",
      feeFranchiseFeeAmount: brokerageDetails?.hasFranchiseFee
        ? agentProfile?.fees?.feeFranchiseFeeAmount || ""
        : "",
      feeFranchiseFeeType: agentProfile?.fees?.feeFranchiseFeeType || "",
      feeManagerFeeAmount: brokerageDetails?.hasManagerFee
        ? agentProfile?.fees?.feeManagerFeeAmount
        : "",
      feeManagerFeePercent:
        brokerageDetails?.hasManagerFee &&
        agentProfile?.fees?.feeManagerFeeType === "percent"
          ? agentProfile?.fees?.feeManagerFeeAmount
          : "",
      feeManagerFeeType: brokerageDetails?.hasManagerFee
        ? agentProfile?.fees?.feeManagerFeeType
        : "",
      feeManagerPayName: brokerageDetails?.hasManagerFee
        ? agentProfile?.fees?.feeManagerPayName
        : "",
      feeMentorFeeAmount: brokerageDetails?.hasMentorFee
        ? agentProfile?.fees?.feeMentorFeeAmount || ""
        : "",
      feeMentorFeeType: brokerageDetails?.hasMentorFee
        ? agentProfile?.fees?.feeMentorFeeType || ""
        : "",
      feeTransactionFeeAmount: brokerageDetails?.hasTransactionFee
        ? agentProfile?.fees?.feeTransactionFeeAmount || ""
        : "",
      feeTransactionWhoPays: agentProfile?.fees?.feeTransactionWhoPays || "",
      feeDonationFeeAmount: brokerageDetails?.hasDonationFee
        ? agentProfile?.fees?.feeDonationFeeAmount || ""
        : "",
      feeDonationFeePercent:
        brokerageDetails?.hasDonationFee &&
        agentProfile?.fees?.feeDonationFeeType === "percent"
          ? agentProfile?.fees?.feeDonationFeeAmount
          : "",
      feeDonationPayName: brokerageDetails?.hasDonationFee
        ? agentProfile?.fees.feeDonationPayName || ""
        : "",
      feeTeamName: "",
      feeTeamFeeAmount: "",
      feeTeamFeeType: "",
      feeDeskAmount: "",
      feeDeskFreq: "",
      creditAmount: "",
      creditGoesTo: "",
      debitReason: "",
      debitAmount: "",
      // ...agent.fees,
    },
    coAgent: transaction?.coAgent || null,
  };
  //   const agentFeeValues = agent ? [...initialValues, ...agent] : initialValues;

  const validationSchema = Yup.object({
    // lastName: Yup.string().required("You must provide a last name"),
  });

  const fieldKey = "financials.";
  function handleChangeForFee(
    setFieldValue,
    feeName,
    currentCompensation,
    percentAmount,
    dollarAmount,
    payTo,
    position,
    setPayLineInCDA
  ) {
    if (payTo?.length < 1) {
      setFieldValue(fieldKey + feeName + "PayAmount", "");
      setFieldValue("balance." + feeName, "");
      return currentCompensation;
    }
    let payAmount = 0;
    if (percentAmount && !isNaN(percentAmount)) {
      payAmount = (currentCompensation * getAsNumber(percentAmount)) / 100;

      setFieldValue(fieldKey + feeName + "PayAmount", payAmount);
    } else {
      payAmount = getAsNumber(dollarAmount);
    }
    balance[feeName] = currentCompensation - payAmount;

    setFieldValue(
      "balance." + feeName,
      currencyFormatter.format(balance[feeName])
    );

    if (setPayLineInCDA) {
      cda["text" + position.lineNum++] =
        payTo + " " + currencyFormatter.format(payAmount);
    }

    return currentCompensation - payAmount;
  }

  // function setCurrencyValueInForm(setFieldValue, namedInput, newValue) {
  //   setFieldValue(namedInput, currencyFormatter.format(newValue));
  // }
  function Watcher() {
    const { values, setFieldValue } = useFormikContext();
    useEffect(() => {
      resetCdaText();
      resetBalance(setFieldValue);
      if (
        values?.financials?.salesPrice &&
        values?.financials?.compensationAmount &&
        values?.financials?.compensationType
      ) {
        let compAmt = 0;

        if (values.financials.compensationType === "dollars") {
          compAmt = parseCurrency(values.financials.compensationAmount) || 0;
        } else if (values.financials.compensationType === "percent") {
          compAmt =
            ((parseCurrency(values.financials.salesPrice) || 0) *
              (Number(values.financials.compensationAmount) || 0)) /
            100;
        } else {
          compAmt = 0;
        }
        setFieldValue(
          fieldKey + "compensationPayAmount",
          currencyFormatter.format(compAmt)
        );
        setFieldValue(
          "cda.compensationPayAmount",
          currencyFormatter.format(compAmt)
        );

        balance.compensation = compAmt;

        if (compAmt === 0) return;

        let position = { lineNum: 1 };

        // if (values?.financials?.referralPayName?.length > 0) {
        compAmt = handleChangeForFee(
          setFieldValue,
          "Referral",
          compAmt,
          values?.fees?.feeReferralFeePercent,
          values.financials.ReferralPayAmount,
          values.financials.referralPayName,
          position,
          true
        );

        // if (
        //   values?.financials?.coAgentPayToName ||
        //   values?.financials?.coAgentPayToBrokerage
        // )
        if (transaction?.coAgent?.firstName?.length > 0) {
          compAmt = handleChangeForFee(
            setFieldValue,
            "CoAgent",
            compAmt,
            values?.fees?.feeCoAgentFeePercent,
            values?.financials?.CoAgentPayAmount,
            values?.financials?.coAgentPayToName,
            position,
            false
          );
          const totalCoAgentSideDollarSplitAmount =
            Number(values?.financials?.CoAgentPayAmount) || 0;
          const coAgentDollarAmountSplit =
            (Number(values?.fees?.feeCoAgentFeePercent) / 100) *
              totalCoAgentSideDollarSplitAmount || 0;
          // Number(values?.financials?.CoAgentPayAmount) || 0;
          const payToCoAgentPercent =
            Number(values?.fees?.feeCoAgentPayToFeePercent) || 0;
          let payToCoAgentBrokeragePercent =
            Number(values?.fees?.feeCoAgentBrokeragePayToFeePercent) || 0;
          // let payToCoAgent =
          //   Number(values?.financials?.CoAgentPayToPayAmount) || 0;
          // let payToCoAgentBrokerage =
          //   Number(values?.fees?.feeCoAgentBrokeragePayAmount) || 0;
          let coAgentPayToName = values?.financials?.coAgentPayToName || "";
          // let coAgentPayToBrokerage =
          //   values?.financials?.coAgentPayToBrokerage || "";
          // let coAgentBrokeragePayToName =
          //   values?.financials?.coAgentPayToBrokerageName || "";

          if (
            // dollar amount calculated, and entered % for agent
            payToCoAgentPercent &&
            payToCoAgentPercent > 0 &&
            coAgentDollarAmountSplit > 0
          ) {
            setFieldValue(
              "financials.CoAgentPayToPayAmount",
              coAgentDollarAmountSplit.toFixed(2)
            );

            payToCoAgentBrokeragePercent = 100 - payToCoAgentPercent;
            setFieldValue(
              "fees.feeCoAgentBrokeragePayToFeePercent",
              payToCoAgentBrokeragePercent
            );

            setFieldValue(
              "financials.CoAgentBrokeragePayAmount",
              totalCoAgentSideDollarSplitAmount - coAgentDollarAmountSplit
              // coAgentDollarAmountSplit - payToCoAgentBrokeragePercent / 100
            );
          }
          if (isCoAgentOneOfMyAgents && brokerageDetails?.hasDonationFee) {
            const coAgentDonationPayAmount = values?.fees
              ?.coAgentFeeDonationFeePercent
              ? (values.fees.coAgentFeeDonationFeePercent / 100) *
                coAgentDollarAmountSplit
              : values?.fees?.coAgentFeeDonationFeeAmount || 0;
            setFieldValue(
              "financials.coAgentDonationPayAmount",
              coAgentDonationPayAmount
            );
            cda["text" + position.lineNum++] =
              values.financials.coAgentDonationPayName +
              " " +
              currencyFormatter.format(coAgentDonationPayAmount);
          }
          cda["text" + position.lineNum++] =
            coAgentPayToName +
            "  " +
            currencyFormatter.format(getPayToCoAgent(values));
          // if (
          //   coAgentBrokeragePayToName === values?.financials?.brokeragePayName
          // ) {
          // }
        }
        // setFieldValue(
        //   "financials.AgentPayAmount",
        //   currencyFormatter.format(payToAgent)
        // );
        // setFieldValue(
        //   "balance.Agent",
        //   currencyFormatter.format(compensationRemaining - payToAgent)
        // );
        // agent percent
        // const agentSplitPercent = values?.fees?.feeBrokerageFeePercent
        //   ? 100 - getAsNumber(values?.fees?.feeBrokerageFeePercent)
        //   : "";
        handleChangeForFee(
          setFieldValue,
          "Agent",
          compAmt,
          values?.fees?.feeAgentFeePercent,
          values.financials.AgentPayAmount,
          values.financials.agentPayToName,
          position,
          false
        );

        if (values?.financials?.brokeragePayName) {
          handleChangeForFee(
            setFieldValue,
            "Brokerage",
            compAmt,
            values?.fees?.feeBrokerageFeePercent,
            values.financials.BrokeragePayAmount,
            values.financials.brokeragePayName,
            position,
            false
          );
          // brokerage balance not correct

          compAmt =
            compAmt -
            getAsNumber(values?.financials?.AgentPayAmount) -
            getAsNumber(values?.financials?.BrokeragePayAmount);
          // setCurrencyValueInForm(setFieldValue, "balance.Brokerage", compAmt);
          // setFieldValue("balance.Brokerage", currencyFormatter.format(compAmt));
          // do stuff for brokerage
          let payToBrokerage = values.financials.BrokeragePayAmount;
          if (
            values?.fees?.creditPaidBy === "brokerage" &&
            Number(values?.fees?.creditAmount)
          ) {
            payToBrokerage -= Number(values?.fees?.creditAmount);
          }
          if (Number(values?.fees?.debitAmount)) {
            payToBrokerage += Number(values?.fees?.debitAmount);
          }
          if (
            values?.financials?.coAgentPayToBrokerageName ===
              values?.financials?.brokeragePayName &&
            Number(values?.financials?.CoAgentBrokeragePayAmount)
          ) {
            payToBrokerage += Number(
              values?.financials?.CoAgentBrokeragePayAmount
            );
          }
          let extraBrokerageInfo = "";
          if (Number(values?.fees?.feeTransactionFeeAmount)) {
            payToBrokerage += getAsNumber(
              values?.fees?.feeTransactionFeeAmount
            );
            extraBrokerageInfo =
              " [includes Transaction Fee Amount of " +
              currencyFormatter.format(values?.fees?.feeTransactionFeeAmount) +
              " paid by " +
              values?.fees?.feeTransactionWhoPays +
              " ]";
          }

          cda["text" + position.lineNum++] =
            values.financials.brokeragePayName +
            "  " +
            currencyFormatter.format(payToBrokerage) +
            extraBrokerageInfo;
        }
        if (
          currentUserProfile?.brokerage?.hasManagerFee &&
          values?.financials?.managerPayName
        ) {
          compAmt = handleChangeForFee(
            setFieldValue,
            "Manager",
            compAmt,
            values?.fees?.feeManagerFeePercent,
            values.financials.ManagerPayAmount,
            values.financials.managerPayName,
            position,
            true
          );
        }
        if (
          currentUserProfile?.brokerage?.hasTransactionFee &&
          values?.fees?.feeTransactionWhoPays
        ) {
          handleChangeForFee(
            setFieldValue,
            "Transaction",
            compAmt,
            values?.fees?.feeTransactionFeePercent,
            values.financials.TransactionPayAmount,
            values.fees?.feeTransactionWhoPays,
            position,
            false
          );
        }
        if (
          currentUserProfile?.brokerage?.hasDonationFee &&
          values?.financials?.donationPayName
        ) {
          handleChangeForFee(
            setFieldValue,
            "Donation",
            compAmt,
            values?.fees?.feeDonationFeePercent,
            values.financials.DonationPayAmount,
            values.financials.donationPayName,
            position,
            true
          );
        }
        if (
          transaction.tcId?.length > 0 &&
          values?.financials?.tcPayName?.length
        ) {
          handleChangeForFee(
            setFieldValue,
            "Tc",
            compAmt,
            "",
            values.financials.TcPayAmount,
            values.financials.tcPayName,
            position,
            true
          );
        }
        // let payToAgent = compensationRemaining;
        // if (
        //   values.fees?.creditPaidBy === "agent" &&
        //   !isNaN(values.fees?.creditAmount)
        // ) {
        //   payToAgent -= Number(values.fees?.creditAmount);
        // }
        // if (!isNaN(values.fees?.debitAmount)) {
        //   payToAgent -= Number(values.fees?.debitAmount);
        // }

        // if (values?.fees?.feeBrokerageFeePercent) {
        //   setFieldValue(
        //     "fees.feeAgentFeePercent",
        //     100 - values.fees.feeBrokerageFeePercent
        //   );
        // }
        // setFieldValue(
        //   "financials.AgentPayAmount",
        //   currencyFormatter.format(payToAgent)
        // );
        // setFieldValue(
        //   "balance.Agent",
        //   currencyFormatter.format(compensationRemaining - payToAgent)
        // );

        cda["text" + position.lineNum++] =
          values.financials.agentPayToName +
          " " +
          currencyFormatter.format(getPayToAgent(values));
      }
    }, [values, setFieldValue]);
    return null;
  }

  return (
    <ModalWrapper>
      <Segment clearing id="manager-financials-edit-form">
        <div className="large horizontal margin small top margin">
          <Header size="large" color="blue">
            Transaction Financials : {transaction.address.street}{" "}
            {transaction.city}
          </Header>
          <Grid centered stackable className="zero bottom margin">
            <Grid.Column width={16}>
              <Segment clearing>
                <Formik
                  enableReinitialize
                  initialValues={initialValues}
                  validationSchema={validationSchema}
                  validateOnChange={false}
                  validateOnBlur={false}
                  onSubmit={async (values, { setSubmitting }) => {
                    try {
                      const finan = {
                        cda: {
                          ...cda,
                          specialInstructions: values.cda.specialInstructions,
                          compensationPayAmount:
                            values.cda.compensationPayAmount,
                        },
                      };
                      await updateTransFieldsInDb(transaction.id, finan);
                      setSubmitting(false);
                      toast.success("CDA details successfully updated");
                      // dispatch(
                      //   closeModal({
                      //     modalType: "ManagerAgentFinancialsEditForm",
                      //   })
                      // );
                      //  THIS ISN'T WORKING ON PAGE REFRESH BECAUSE NO FORMS!!!
                      const formsAdded = [];
                      const formToAdd = forms.find(
                        (form) =>
                          form.title === "Commission Disbursement Authorization"
                      );
                      formsAdded.push(formToAdd);
                      if (formsAdded) {
                        await addFormsToTransaction(
                          transaction,
                          formsAdded,
                          currentUserProfile
                        );
                      }
                      dispatch(
                        closeModal({
                          modalType: "ManagerAgentFinancialsEditForm",
                        })
                      );
                      navigate(`/transactions/${transaction.id}/documents`);
                    } catch (error) {
                      toast.error(error.message);
                      setSubmitting(false);
                    }
                  }}
                >
                  {({ isSubmitting, dirty, isValid, values }) => (
                    <Form className="ui form medium margin bottom">
                      <Watcher />
                      <Header color="blue">Transaction Details</Header>
                      <Grid>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={4}>
                            <MyTextInput
                              name={"financials.salesPrice"}
                              label="Transaction Sales Price"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={4}>
                            <MyTextInput
                              name={"financials.compensationAmount"}
                              label="Compensation Amount"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={4}>
                            <MySelectInput
                              name={"financials.compensationType"}
                              options={amountType}
                              label="Compensation Type"
                              style={{ maxWidth: "6em" }}
                            ></MySelectInput>
                          </Grid.Column>

                          <Grid.Column mobile={16} computer={4}>
                            <MyTextInput
                              disabled
                              name={"financials.compensationPayAmount"}
                              label="Total Compensation"
                            />
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>

                      <Header color="blue">Referral </Header>
                      <Grid stackable>{getTableHeader()}</Grid>
                      <Grid stackable>
                        <Grid.Row className="zero top padding">
                          <Grid.Column mobile={16} computer={colSize.one}>
                            <MyTextInput
                              disabled
                              name={"REFERRAL"}
                              value="Referral"
                            ></MyTextInput>
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.two}>
                            <MyTextInput name={"financials.referralPayName"} />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.three}>
                            <MyTextInput name={"fees.feeReferralFeePercent"} />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.four}>
                            <MyTextInput
                              name={"financials.ReferralPayAmount"}
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.five}>
                            <MyTextInput disabled name={"balance.Referral"} />
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      {/* {brokerageDetails?.hasOtherTop && (
                        <>
                          <Header color="blue">Payout To Other </Header>
                          <Grid stackable>{getTableHeader()}</Grid>
                          <Grid stackable>
                            <Grid.Row className="zero top padding">
                              <Grid.Column mobile={16} computer={colSize.one}>
                                <MyTextInput
                                  disabled
                                  name={"OTHERTOP"}
                                  value="Other"
                                ></MyTextInput>
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.two}>
                                <MyTextInput name={"financials.otherPayName"} />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.three}>
                                <MyTextInput
                                  name={"fees.feeOtherSideFeePercent"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.four}>
                                <MyTextInput
                                  name={"financials.OtherSidePayAmount"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.five}>
                                <MyTextInput
                                  disabled
                                  name={"balance.OtherSide"}
                                />
                              </Grid.Column>
                            </Grid.Row>
                          </Grid>
                        </>
                      )} */}

                      {transaction?.coAgent && (
                        <>
                          <Divider />
                          <Header color="blue">Co-Agent</Header>
                          <Grid>
                            {getTableHeader()}
                            <Grid.Row className="zero top padding">
                              <Grid.Column mobile={16} computer={colSize.one}>
                                <MyTextInput
                                  disabled
                                  name={"COAGENT"}
                                  value="Co-Agent"
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.two}>
                                {/* <MyTextInput
                                  name={"financials.coAgentPayToName"}
                                /> */}
                                Total Split amount:
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.three}>
                                <MyTextInput
                                  name={"fees.feeCoAgentFeePercent"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.four}>
                                <MyTextInput
                                  name={"financials.CoAgentPayAmount"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.five}>
                                <MyTextInput
                                  disabled
                                  name={"balance.CoAgent"}
                                />
                              </Grid.Column>
                            </Grid.Row>
                            <Grid.Row className="zero top padding">
                              <Grid.Column mobile={16} computer={colSize.one}>
                                Co-Agent Split
                                <br />
                                {transaction?.coAgent?.firstName}{" "}
                                {transaction?.coAgent?.lastName}
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.two}>
                                <MyTextInput
                                  name={"financials.coAgentPayToName"}
                                  placeholder="Co-Agent Name"
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.three}>
                                <MyTextInput
                                  name={"fees.feeCoAgentPayToFeePercent"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.four}>
                                <MyTextInput
                                  name={"financials.CoAgentPayToPayAmount"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.five}>
                                {/* <MyTextInput
                                  disabled
                                  name={"balance.CoAgent"}
                                /> */}
                              </Grid.Column>
                            </Grid.Row>
                            <Grid.Row className="zero top padding">
                              <Grid.Column mobile={16} computer={colSize.one}>
                                <MyTextInput
                                  disabled
                                  name={"BROKERAGECOAGENT"}
                                  value="Brokerage"
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.two}>
                                <MyTextInput
                                  name={"financials.coAgentPayToBrokerageName"}
                                  placeholder="Co-Agent Brokerage"
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.three}>
                                <MyTextInput
                                  disabled
                                  name={
                                    "fees.feeCoAgentBrokeragePayToFeePercent"
                                  }
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.four}>
                                <MyTextInput
                                  name={"financials.CoAgentBrokeragePayAmount"}
                                />
                              </Grid.Column>
                              <Grid.Column mobile={16} computer={colSize.five}>
                                {/* <MyTextInput
                                  disabled
                                  name={"balance.CoAgent"}
                                /> */}
                              </Grid.Column>
                            </Grid.Row>

                            {isCoAgentOneOfMyAgents &&
                              brokerageDetails?.hasManagerFee && (
                                <Grid.Row className="zero top padding">
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.one}
                                  >
                                    <MyTextInput
                                      disabled
                                      name={"MANAGERCOAGENT"}
                                      value="Manager"
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.two}
                                  >
                                    <MyTextInput
                                      name={"financials.coAgentManagerPayName"}
                                      placeholder="CoAgent Manager"
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.three}
                                  >
                                    <MyTextInput
                                      name={"fees.coAgentFeeManagerFeePercent"}
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.four}
                                  >
                                    <MyTextInput
                                      name={
                                        "financials.coAgentManagerPayAmount"
                                      }
                                    ></MyTextInput>
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.five}
                                  ></Grid.Column>
                                </Grid.Row>
                              )}
                            {isCoAgentOneOfMyAgents &&
                              currentUserProfile?.brokerage?.hasDonationFee && (
                                <Grid.Row className="zero top padding">
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.one}
                                  >
                                    <MyTextInput
                                      disabled
                                      name={"DONATIONCOAGENT"}
                                      value="Donation"
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.two}
                                  >
                                    <MyTextInput
                                      name={"financials.coAgentDonationPayName"}
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.three}
                                  >
                                    <MyTextInput
                                      name={"fees.coAgentFeeDonationFeePercent"}
                                    />
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.four}
                                  >
                                    <MyTextInput
                                      name={
                                        "financials.coAgentDonationPayAmount"
                                      }
                                    ></MyTextInput>
                                  </Grid.Column>
                                  <Grid.Column
                                    mobile={16}
                                    computer={colSize.five}
                                  >
                                    {/* <MyTextInput name={"balance.Donation"} /> */}
                                  </Grid.Column>
                                </Grid.Row>
                              )}
                            {isCoAgentOneOfMyAgents && (
                              <Grid.Row>
                                <Grid.Column
                                  mobile={16}
                                  computer={6}
                                ></Grid.Column>

                                <Grid.Column mobile={16} computer={5}>
                                  Total payable to Co-Agent:
                                </Grid.Column>

                                <Grid.Column mobile={16} computer={3}>
                                  {currencyFormatter.format(
                                    getPayToCoAgent(values)
                                  )}
                                </Grid.Column>
                              </Grid.Row>
                            )}
                          </Grid>
                        </>
                      )}

                      <Divider />
                      <Header color="blue">Agent</Header>

                      <Grid stackable>
                        {getTableHeader()}

                        <Grid.Row className="zero top padding">
                          <Grid.Column mobile={16} computer={colSize.one}>
                            Agent Split
                            <br />
                            {values?.fees?.agentFullName}
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.two}>
                            <MyTextInput
                              name={"financials.agentPayToName"}
                              placeholder="Agent Pay To Name"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.three}>
                            <MyTextInput name={"fees.feeAgentFeePercent"} />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.four}>
                            <MyTextInput name={"financials.AgentPayAmount"} />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.five}>
                            <MyTextInput disabled name={"balance.Agent"} />
                          </Grid.Column>
                        </Grid.Row>
                        <Grid.Row className="zero top padding">
                          <Grid.Column mobile={16} computer={colSize.one}>
                            <MyTextInput
                              disabled
                              name={"BROKERAGE"}
                              value={"Brokerage"}
                            ></MyTextInput>
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.two}>
                            <MyTextInput name={"financials.brokeragePayName"} />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.three}>
                            <MyTextInput
                              disabled
                              name={"fees.feeBrokerageFeePercent"}
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.four}>
                            <MyTextInput
                              name={"financials.BrokeragePayAmount"}
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={colSize.five}>
                            <MyTextInput
                              disabled
                              hidden
                              name={"balance.Brokerage"}
                            />
                          </Grid.Column>
                        </Grid.Row>

                        {/* {brokerageDetails?.hasFranchiseFee && (
                          <Grid.Row className="zero top padding">
                            <Grid.Column mobile={16} computer={colSize.one}>
                              <MyTextInput
                                disabled
                                name={"FRANCHISE"}
                                value="Franchise"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.two}>
                              <MyTextInput
                                name={"fees.feeFranchisePayName"}
                              />
                          
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.three}>
                              <MyTextInput
                                name={"fees.feeFranchiseFeePercent"}
                              />
                            </Grid.Column>
                          </Grid.Row>
                        )} */}
                        {transaction?.tcId && (
                          <Grid.Row className="zero top padding">
                            <Grid.Column mobile={16} computer={colSize.one}>
                              <MyTextInput disabled name={"TC"} value="TC" />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.two}>
                              <MyTextInput name={"financials.tcPayName"} />
                            </Grid.Column>
                            <Grid.Column
                              mobile={16}
                              computer={colSize.three}
                            ></Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.four}>
                              <MyTextInput
                                name={"financials.TcPayAmount"}
                              ></MyTextInput>
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.five}>
                              <MyTextInput
                                disabled
                                hidden
                                name={"balance.Tc"}
                              />
                            </Grid.Column>
                          </Grid.Row>
                        )}

                        {brokerageDetails?.hasManagerFee && (
                          <Grid.Row className="zero top padding">
                            <Grid.Column mobile={16} computer={colSize.one}>
                              <MyTextInput
                                disabled
                                name={"MANAGER"}
                                value="Manager"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.two}>
                              <MyTextInput name={"financials.managerPayName"} />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.three}>
                              <MyTextInput name={"fees.feeManagerFeePercent"} />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.four}>
                              <MyTextInput
                                name={"financials.ManagerPayAmount"}
                              ></MyTextInput>
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.five}>
                              <MyTextInput
                                disabled
                                hidden
                                name={"balance.Manager"}
                              />
                            </Grid.Column>
                          </Grid.Row>
                        )}
                        {brokerageDetails?.hasDonationFee && (
                          <Grid.Row className="zero top padding">
                            <Grid.Column mobile={16} computer={colSize.one}>
                              <MyTextInput
                                disabled
                                name={"DONATION"}
                                value="Donation"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.two}>
                              <MyTextInput
                                name={"financials.donationPayName"}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.three}>
                              <MyTextInput
                                name={"fees.feeDonationFeePercent"}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.four}>
                              <MyTextInput
                                name={"financials.DonationPayAmount"}
                              ></MyTextInput>
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.five}>
                              <MyTextInput
                                disabled
                                hidden
                                name={"balance.Donation"}
                              />
                            </Grid.Column>
                          </Grid.Row>
                        )}

                        {brokerageDetails?.hasTransactionFee && (
                          <Grid.Row className="zero top padding">
                            <Grid.Column mobile={16} computer={colSize.one}>
                              <MyTextInput
                                disabled
                                name={"TRANSACTION"}
                                value="Transaction"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.two}>
                              <MySelectInput
                                name={"fees.feeTransactionWhoPays"}
                                placeholder="Who pays?"
                                options={[
                                  {
                                    key: "",
                                    text: "",
                                    value: "",
                                  },
                                  {
                                    key: "agent",
                                    text: "agent",
                                    value: "agent",
                                  },
                                  {
                                    key: "client",
                                    text: "client",
                                    value: "client",
                                  },

                                  {
                                    key: "other",
                                    text: "other",
                                    value: "other",
                                  },
                                ]}
                              ></MySelectInput>
                            </Grid.Column>
                            <Grid.Column
                              mobile={16}
                              computer={colSize.three}
                            ></Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.four}>
                              <MyTextInput
                                name={"fees.feeTransactionFeeAmount"}
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={colSize.five}>
                              {/* <MyTextInput
                                name={"financials.TransactionPayAmount"}
                              ></MyTextInput> */}
                            </Grid.Column>
                          </Grid.Row>
                        )}
                      </Grid>
                      <Divider />
                      <Grid stackable>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={3}>
                            <MyTextInput
                              disabled
                              name={"CREDIT"}
                              value="Credit"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.creditGoesTo"}
                              placeholder="Who gets credit?"
                              label="Credit To"
                              options={[
                                {
                                  key: "",
                                  text: "",
                                  value: "",
                                },
                                {
                                  key: "agent",
                                  text: "agent",
                                  value: "agent",
                                },
                                {
                                  key: "client",
                                  text: "client",
                                  value: "client",
                                },
                              ]}
                            ></MySelectInput>
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={5}>
                            <MySelectInput
                              name={"fees.creditPaidBy"}
                              placeholder="Who pays credit?"
                              label="Paid By"
                              options={[
                                {
                                  key: "",
                                  text: "",
                                  value: "",
                                },
                                {
                                  key: "agent",
                                  text: "agent",
                                  value: "agent",
                                },
                                {
                                  key: "brokerage",
                                  text: "brokerage",
                                  value: "brokerage",
                                },

                                {
                                  key: "client",
                                  text: "client",
                                  value: "client",
                                },
                              ]}
                            ></MySelectInput>
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={3}>
                            <MyTextInput
                              name={"fees.creditAmount"}
                              label="$ Credit Amount"
                            />
                          </Grid.Column>
                        </Grid.Row>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={colSize.one}>
                            <MyTextInput
                              disabled
                              name={"DEBIT"}
                              value="Debit"
                            />
                          </Grid.Column>
                          <Grid.Column mobile={16} computer={10}>
                            <MyTextInput
                              name={"fees.debitReason"}
                              label="Debit Reason"
                            ></MyTextInput>{" "}
                          </Grid.Column>

                          <Grid.Column mobile={16} computer={colSize.five}>
                            <MyTextInput
                              name={"fees.debitAmount"}
                              label="$ Debit Amount"
                            ></MyTextInput>
                          </Grid.Column>
                        </Grid.Row>
                        <Grid.Row>
                          <Grid.Column mobile={16} computer={6}></Grid.Column>

                          <Grid.Column mobile={16} computer={5}>
                            Total payable to Agent:
                          </Grid.Column>

                          <Grid.Column mobile={16} computer={3}>
                            {currencyFormatter.format(getPayToAgent(values))}
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>

                      <Divider />
                      <Header color="blue">Distribution Authorization</Header>
                      <Grid stackable>
                        <Grid.Row>
                          <Grid.Column width={14}>
                            <>
                              Quick view of payouts:
                              <Popup
                                flowing
                                size="small"
                                trigger={
                                  <Icon
                                    name="info"
                                    color="blue"
                                    circular
                                    inverted
                                    size="small"
                                    style={{
                                      marginLeft: "3px",
                                      marginBottom: "3px",
                                    }}
                                  />
                                }
                              >
                                <p className="bold text blue mini bottom margin">
                                  Below are the payout instructions that will
                                  auto-populate in the CDA form.
                                  <br />
                                  You can still edit the CDA form inside the
                                  transaction.
                                  <br />
                                  <i>
                                    Note:Only Managers can add the CDA form to
                                    the transaction.
                                  </i>
                                </p>

                                <ol>{renderCDAListOfPayouts()}</ol>
                                {cda["text1"]?.length < 4 && (
                                  <p>
                                    Be sure the Total Compensation is filled out
                                    above,
                                    <br />
                                    and fill out at least one payout amount
                                    <br />
                                    to see the payout lines that will appear in
                                    the CDA.
                                  </p>
                                )}
                                <p></p>
                              </Popup>
                            </>
                          </Grid.Column>
                          <Grid.Column width={14}>
                            <MyTextArea
                              name="cda.specialInstructions"
                              label="Special Instructions to print on Distribution Authorization"
                            />
                          </Grid.Column>
                        </Grid.Row>
                      </Grid>
                      <br />
                      <br />
                      <Button
                        loading={isSubmitting}
                        disabled={!dirty || isSubmitting}
                        type="submit"
                        floated={isMobile ? "left" : "right"}
                        primary
                        content="Create CDA"
                        className={isMobile ? "fluid large" : "large"}
                      />
                      <Button
                        onClick={() =>
                          dispatch(
                            closeModal({
                              modalType: "ManagerAgentFinancialsEditForm",
                            })
                          )
                        }
                        type="button"
                        floated={isMobile ? null : "right"}
                        content="Close"
                        className={isMobile ? "fluid large" : "large"}
                      />
                    </Form>
                  )}
                </Formik>
              </Segment>
            </Grid.Column>
          </Grid>
        </div>
        <Divider hidden />
        <br />
      </Segment>
    </ModalWrapper>
  );
}
