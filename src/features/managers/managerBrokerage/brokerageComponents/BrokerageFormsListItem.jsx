import React from "react";
import { useDispatch } from "react-redux";
import { Button, Icon, Table } from "semantic-ui-react";
import { openModal } from "../../../../app/common/modals/modalSlice";

export default function BrokerageFormsListItem({ brokerageForm }) {
  const dispatch = useDispatch();

  const handleEdit = () => {
    dispatch(
      openModal({
        modalType: "BrokerageFormCustomizationModal",
        modalProps: { brokerageForm },
      })
    );
  };

  return (
    <>
      <Table.Row>
        <Table.Cell>
          <strong>{brokerageForm.title}</strong>
        </Table.Cell>
        <Table.Cell>
          <Icon
            name={brokerageForm.showBuyerAgent ? "check" : "times"}
            color={brokerageForm.showBuyerAgent ? "green" : "grey"}
          />
        </Table.Cell>
        <Table.Cell>
          <Icon
            name={brokerageForm.showSellerAgent ? "check" : "times"}
            color={brokerageForm.showSellerAgent ? "green" : "grey"}
          />
        </Table.Cell>
        <Table.Cell>
          <Icon
            name={brokerageForm.autoAddToNewTransaction ? "check" : "times"}
            color={brokerageForm.autoAddToNewTransaction ? "green" : "grey"}
          />
        </Table.Cell>
        <Table.Cell>
          <Icon
            name={brokerageForm.canBeTemplate ? "check" : "times"}
            color={brokerageForm.canBeTemplate ? "green" : "grey"}
          />
        </Table.Cell>
        <Table.Cell>
          <Icon
            name={brokerageForm.isFillableByClient ? "check" : "times"}
            color={brokerageForm.isFillableByClient ? "green" : "grey"}
          />
        </Table.Cell>
        {/* <Table.Cell>
          <Icon
            name={brokerageForm.isManagerOnly ? "check" : "times"}
            color={brokerageForm.isManagerOnly ? "green" : "grey"}
          />
        </Table.Cell> */}
        {/* {brokerageForm.updatedAt &&
            format(brokerageForm.updatedAt?.toDate(), "MMM d, yyyy")} */}
        <Table.Cell textAlign="right">
          <Button
            size="mini"
            color="blue"
            icon="edit"
            onClick={handleEdit}
            title="Edit"
          />
        </Table.Cell>
      </Table.Row>
    </>
  );
}
