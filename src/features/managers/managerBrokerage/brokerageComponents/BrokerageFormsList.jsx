import React from "react";
import { Table } from "semantic-ui-react";
import BrokerageFormsListItem from "./BrokerageFormsListItem";

export default function BrokerageFormsList({
  brokerageForms,
  column,
  direction,
}) {
  return (
    <>
      <Table compact sortable className="mini top margin">
        <Table.Header className="mobile hidden">
          <Table.Row className="small-header">
            <Table.HeaderCell sorted={column === "title" ? direction : null}>
              File Name
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "showBuyerAgent" ? direction : null}
            >
              Show Buyer
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "showSellerAgent" ? direction : null}
            >
              Show Seller
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "autoAddToNewTransaction" ? direction : null}
            >
              Auto Add
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "canBeTemplate" ? direction : null}
            >
              Template
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "isFillableByClient" ? direction : null}
            >
              Client Fill Out
            </Table.HeaderCell>
            {/* <Table.HeaderCell
              sorted={column === "updatedAt" ? direction : null}
            >
              Manager Only
            </Table.HeaderCell> */}
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {brokerageForms &&
            brokerageForms.length !== 0 &&
            brokerageForms.map((brokerageForm) => (
              <BrokerageFormsListItem
                brokerageForm={brokerageForm}
                key={brokerageForm.id}
              />
            ))}
        </Table.Body>
      </Table>
    </>
  );
}
