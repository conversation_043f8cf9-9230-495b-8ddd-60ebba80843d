import { Formik, Form } from "formik";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, Segment, Popup, Icon } from "semantic-ui-react";
import * as Yup from "yup";
import MyTextInput from "../../../../app/common/form/MyTextInput";
import MyTextArea from "../../../../app/common/form/MyTextArea";
import MyCheckbox from "../../../../app/common/form/MyCheckbox";
import MySelectInput from "../../../../app/common/form/MySelectInput";
import {
  updateBrokerageFormInDb,
} from "../../../../app/firestore/firestoreService";
import { toast } from "react-toastify";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../../app/common/modals/modalSlice";

export default function BrokerageFormCustomizationModal({ brokerageForm }) {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);

  const roleOptions = [
    { key: "buyer", text: "Buyer", value: "Buyer" },
    { key: "buyer2", text: "Buyer 2", value: "Buyer 2" },
    { key: "buyer3", text: "Buyer 3", value: "Buyer 3" },
    { key: "seller", text: "Seller", value: "Seller" },
    { key: "seller2", text: "Seller2", value: "Seller2" },
    { key: "seller3", text: "Seller3", value: "Seller3" },
    { key: "listingAgent", text: "Listing Agent", value: "Listing Agent" },
    { key: "buyerAgent", text: "Buyer Agent", value: "Buyer Agent" },
    { key: "title", text: "Title", value: "Title" },
    { key: "lender", text: "Lender", value: "Lender" },
  ];

  const transactionStatusOptions = [
    { key: "activeListing", text: "Active Listing", value: "Active Listing" },
    { key: "activeBuyer", text: "Active Buyer", value: "Active Buyer" },
    { key: "underContract", text: "Under Contract", value: "Under Contract" },
    { key: "complete", text: "Complete", value: "Complete" },
    { key: "archived", text: "Archived", value: "Archived" },
  ];

  const initialValues = brokerageForm
    ? {
      id: brokerageForm.id,
      brokerage: brokerageForm.brokerage || currentUserProfile.brokerageForms || "",
        title: brokerageForm.title || "",
        name: brokerageForm.name || "",
        brokerageReplacesParentFormTitle: brokerageForm.brokerageReplacesParentFormTitle || "",
        showBuyerAgent: brokerageForm.showBuyerAgent || false,
        showSellerAgent: brokerageForm.showSellerAgent || false,
        canBeTemplate: brokerageForm.canBeTemplate || false,
        isFillableByClient: brokerageForm.isFillableByClient || false,
        autoAddToNewTransaction: brokerageForm.autoAddToNewTransaction || false,
        canCustomizeEmailOnShare: brokerageForm.canCustomizeEmailOnShare || [],
        emailSharingDefaultsSubject: brokerageForm.emailSharingDefaults?.subjectPlusAddress || "",
        emailSharingDefaultsMessage: brokerageForm.emailSharingDefaults?.message || "",
        isSuggestedTitleInAddDocModalSeller: brokerageForm.isSuggestedTitleInAddDocModal?.Seller || [],
        isSuggestedTitleInAddDocModalBuyer: brokerageForm.isSuggestedTitleInAddDocModal?.Buyer || [],
        isManagerOnly: brokerageForm.isManagerOnly || false,
        doNotShareWithRole: brokerageForm.doNotShareWithRole || [],
      }
    : {
        title: "",
        name: "",
        brokerageReplacesParentFormTitle: "",
        showBuyerAgent: false,
        showSellerAgent: false,
        canBeTemplate: false,
        isFillableByClient: false,
        autoAddToNewTransaction: false,
        canCustomizeEmailOnShare: [],
        emailSharingDefaultsSubject: "",
        emailSharingDefaultsMessage: "",
        isSuggestedTitleInAddDocModalSeller: [],
        isSuggestedTitleInAddDocModalBuyer: [],
        isManagerOnly: false,
        doNotShareWithRole: [],
      };

  const validationSchema = Yup.object({
    title: Yup.string().required("Unique name is required"),
    name: Yup.string()
      .matches(
        /^[a-zA-Z0-9 _-]*$/,
        "Only letters, numbers, underscore, dash, and spaces are allowed."
      )
      .required("This field is required."),
  });

  return (
    <ModalWrapper size="large">
      <Segment clearing>
        {currentUserProfile?.managerAccess?.hasAccessToBrokerageCustomizations ? (
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validationSchema={validationSchema}
          validateOnChange={false}
          validateOnBlur={false}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              const formData = {
                title: values.title,
                name: values.name,
                brokerageReplacesParentFormTitle:
                  values.brokerageReplacesParentFormTitle,
                showBuyerAgent: values.showBuyerAgent,
                showSellerAgent: values.showSellerAgent,
                canBeTemplate: values.canBeTemplate,
                isFillableByClient: values.isFillableByClient,
                autoAddToNewTransaction: values.autoAddToNewTransaction,
                canCustomizeEmailOnShare: values.canCustomizeEmailOnShare,
                emailSharingDefaults: {
                  subjectPlusAddress: values.emailSharingDefaultsSubject,
                  message: values.emailSharingDefaultsMessage,
                },
                isSuggestedTitleInAddDocModal: {
                  Seller: values.isSuggestedTitleInAddDocModalSeller,
                  Buyer: values.isSuggestedTitleInAddDocModalBuyer,
                },
                isManagerOnly:
                  values.isManagerOnly,
                doNotShareWithRole: values.doNotShareWithRole,
              };

              if (brokerageForm) {
                await updateBrokerageFormInDb(
                  currentUserProfile.state,
                  brokerageForm.id,
                  formData
                );
                toast.success("Brokerage form updated successfully");
              } else {
                toast.error("No form selected for editing");
              }

              setSubmitting(false);
              dispatch(closeModal());
            } catch (error) {
              toast.error(error.message);
              setSubmitting(false);
            }
          }}
        >
          {({ isSubmitting, dirty, values }) => (
            <Form className="ui form medium margin">
              <Header size="huge" color="blue">
                Edit Brokerage Form
              </Header>
              <Divider />

              <Grid>
                <Grid.Row>
                  <Grid.Column width={8}>
                    <MyTextInput
                      name="title"
                      label="Unique Name of File (changes require admin approval)"
                      disabled
                      placeholder="Include brokerage abbreviation (e.g., CB_ExclusiveRightToSell)"
                    />
                  </Grid.Column>
                  <Grid.Column width={8}>
                    <MyTextInput
                      name="name"
                      label="Display Name"
                      placeholder="Name shown in transaction (e.g., Exclusive Right to Sell)"
                    />
                  </Grid.Column>
                </Grid.Row>

                <Grid.Row>
                  <Grid.Column width={16}>
                    <MyTextInput
                      name="brokerageReplacesParentFormTitle"
                      label="State Form to Override (optional) - RARE USE CASE (changes require admin approval)"
                      disabled
                      placeholder="Enter the exact name in TransActioner of the state form this replaces."
                    />
                  </Grid.Column>
                </Grid.Row>

                <Grid.Row>
                  <Grid.Column width={8}>
                    <MyCheckbox
                      name="showBuyerAgent"
                      label="Show Buyer Agent"
                    />
                    <MyCheckbox
                      name="showSellerAgent"
                      label="Show Seller Agent"
                    />
                    <MyCheckbox
                      name="canBeTemplate"
                      label="Can be a Form Template"
                    />
                    <MyCheckbox
                      name="isFillableByClient"
                      label="Can Client Fill Out Form"
                    />
                  </Grid.Column>
                  <Grid.Column width={8}>
                    Auto-Add
                    <Popup
                      flowing
                      size="small"
                      trigger={
                        <Icon
                          name="info"
                          color="blue"
                          circular
                          inverted
                          size="tiny"
                          style={{
                            cursor: "help",
                            marginLeft: "3px",
                            marginBottom: "3px",
                          }}
                        />
                      }
                      position="top center"
                    >
                      <p className="bold text blue mini bottom margin">
                        Automatically Add to New Transaction
                      </p>
                      <p style={{ margin: "0 0 8px 0" }}>
                        This will automatically be added to new transactions if
                        <br />
                        the user does not have a template version of this form
                        (if it can be a template)
                        <br />
                        and if the form can be shown to Buyer/Seller transaction
                        <br />
                        (see checkboxes on left Show Buyer Agent and Show Seller
                        Agent).
                        <br />
                      </p>
                    </Popup>
                    <MyCheckbox
                      name="autoAddToNewTransaction"
                      label="Automatically Add to New Transaction"
                    />
                    {/* Managers Only
                    <Popup
                      flowing
                      size="small"
                      trigger={
                        <Icon
                          name="info"
                          color="blue"
                          circular
                          inverted
                          size="tiny"
                          style={{
                            cursor: "help",
                            marginLeft: "3px",
                            marginBottom: "3px",
                          }}
                        />
                      }
                      position="top center"
                    >
                      <p className="bold text blue mini bottom margin">
                        Managers Only
                      </p>
                      <p style={{ margin: "0 0 8px 0" }}>
                        This form will only be visible to Managers and their
                        assistants
                        <br />
                        when adding forms to a transaction.
                      </p>
                    </Popup>
                    <MyCheckbox
                      name="isManagerOnly"
                      label="Manager Only"
                    /> */}
                  </Grid.Column>
                </Grid.Row>
              </Grid>
              <Divider />
              <Header size="medium" color="blue">
                Email Sharing Options
              </Header>
              <p>
                Please use sparingly!
                <br />
                Custom email sharing should only be for forms that need
                attention right away, such as an offer or objection as otherwise
                you may be sending a lot of emails to them if you set it up on
                each document.
              </p>
              <Grid>
                <Grid.Row>
                  <Grid.Column width={16}>
                    <MySelectInput
                      name="canCustomizeEmailOnShare"
                      label="Can Customize Email on Share (select roles)"
                      options={roleOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  <Grid.Column width={8}>
                    <MyTextInput
                      name="emailSharingDefaultsSubject"
                      label="Default Email Subject"
                      placeholder="Default subject line for sharing"
                    />
                  </Grid.Column>
                  <Grid.Column width={8}>
                    <MyTextArea
                      name="emailSharingDefaultsMessage"
                      label="Default Email Message"
                      placeholder="Default email body for sharing"
                      rows={3}
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
              <Divider />
              <Header size="medium" color="blue">
                Suggested Forms Options
                </Header>
                <p>
                Select the transaction statuses where this form should be suggested in the Add Documents modal.
                </p>
              <Grid>
                <Grid.Row>
                  <Grid.Column width={8}>
                    <MySelectInput
                      name="isSuggestedTitleInAddDocModalSeller"
                      label="Suggest for Seller Transactions"
                      options={transactionStatusOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                  <Grid.Column width={8}>
                    <MySelectInput
                      name="isSuggestedTitleInAddDocModalBuyer"
                      label="Suggest for Buyer Transactions"
                      options={transactionStatusOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>

              <Header size="medium" color="blue">
                Sharing Restrictions
              </Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column width={16}>
                    <MySelectInput
                      name="doNotShareWithRole"
                      label="Do NOT Share With These Roles (e.g., Exclusive Agency do not share with the other agent)"
                      options={roleOptions}
                      multiple={true}
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>

              <Divider />
              <Button
                loading={isSubmitting}
                disabled={!dirty || isSubmitting}
                type="submit"
                size="large"
                color="blue"
                content="Update Form"
              />
              <Button
                disabled={isSubmitting}
                onClick={() => dispatch(closeModal())}
                type="button"
                size="large"
                content="Cancel"
              />
            </Form>
          )}
          </Formik>
           
        ) : (
          <div className="medium horizontal margin small top margin">
            <Header size="huge" color="blue">
              Brokerage Customizations
              </Header>
              <p>
                You do not have access to Brokerage Customizations.
                <br />
                Please <NAME_EMAIL> for more information.
                <br /> &nbsp; <br/>
                If you were set up with access, you would be able to edit the fields shown in the modal for each of your brokerage forms.
            </p>
            <p><img src="https://storage.googleapis.com/transact-staging.appspot.com/public/screenshots/Manager-Brokeragecustomizations.png" width="700px" alt="Example of BrokerageCustomizations screen"/></p>
          </div>
        )}
      </Segment>
    </ModalWrapper>
  );
}
