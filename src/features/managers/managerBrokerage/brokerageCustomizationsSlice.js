import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  brokerageForms: [],
  brokerageForm: {},
}

const brokerageFormsSlice = createSlice({
  name: 'brokerageForms',
  initialState,
  reducers: {
    fetchBrokerageForms(state, action) {
      state.brokerageForms = action.payload
    },
    fetchBrokerageForm(state, action) {
      state.brokerageForm = action.payload
    },
    clearBrokerageForm(state, action) {
      state.brokerageForm = {}
    },
  }
});

export const { 
  fetchBrokerageForms, 
  fetchBrokerageForm, 
  clearBrokerageForm 
} = brokerageFormsSlice.actions

export default brokerageFormsSlice.reducer
