import React, { useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, Head<PERSON>, Segment, Button, Icon } from "semantic-ui-react";
import BrokerageFormsList from "./brokerageComponents/BrokerageFormsList";
import { fieldConversionMappings } from "../../../app/common/util/formUtils";
import { deadlineTemplatesFormFieldMapping } from "../../deadlineTemplates/deadlineTemplatesComponents/DeadlineTemplatesFormFieldMapping";

export default function BrokerageManagerDashboard() {
	  const { brokerageForms } = useSelector((state) => state.brokerageForms);
	  const [showFormFieldInfo, setShowFormFieldInfo] = useState(false);

  return (
    <div className="main-page-wrapper">
      <Grid centered stackable className="small bottom margin">
        <Grid.Column width={14}>
          <Header color="blue" as="h2">
            Brokerage Customizations
          </Header>
          <p color="grey">
            Customize your brokerage forms by editing their options and settings.
            <br />
            Select a form from the table below to modify its configuration.
	            <br />
	          </p>
	          <Button
	            basic
	            color="blue"
	            size="small"
	            onClick={() => setShowFormFieldInfo((prev) => !prev)}
	          >
	            How to Set Up Form Fields for TransActioner
	            <Icon
	              name="info"
	              color="blue"
	              circular
	              inverted
	              size="small"
	              style={{ marginLeft: "3px", marginBottom: "3px" }}
	            />
	          </Button>
	          {showFormFieldInfo && (
	            <div className="top margin">
	              <p className="text-dark text-normal small vertical margin">
                  <h3>How to set up your forms for auto-population and deadline extraction:</h3>
                  This information is to help you set up your forms correctly for TransActioner.
                  <br/>We are working on enabling you to be able to update your forms yourself if you choose.
                  <br/>Email <NAME_EMAIL> for processing, then customize in this section.
	                <ol>
	                  <li>
	                    Convert radio buttons to checkboxes.
	                    <i>
	                      {" "}
	                      Why? Because clearing a radio button is confusing to users.
	                    </i>
	                  </li>
	                  <li>
	                    Ensure no form fields with the same name
	                    <i>
	                      {" "}
	                      (e.g., Adobe will show as #0 and that will not work)
	                    </i>
	                  </li>
	                  <li>
	                    Checkboxes must be checkboxes and not text fields. Ensure it
	                    uses a checkmark.
	                  </li>
	                  <li>
	                    To enable the calendar pop-up to select dates, the form field
	                    must begin with "Date".
	                  </li>
                    <li>Use fonts: Times New Roman or Helvetica (ensures font is embedded or available on the user's computer)</li>
                    <li>Ensure all fields use the color of choice for text. For Blue, use #0000CC</li>
                    <li>Font text size should probably be set to "Auto" or 9.</li>
                    <li>Ensure there is enough space for the text to be entered.</li>
                    <li>For multi-line fields, ensure the option for multi-line is checked for that field.</li>
	                  <li>
	                    For auto-populate to work, the field name must match the list
	                    shown below. <i>Logo is automatically added, leave space on top of first page for it.</i>
	                  </li>
                    <li>Do not have any Adobe-Signature fields in place, as we handle signatures within TransActioner. Ensure all signature fields are text fields with the correct naming convention (begins with "Signature ").</li>
	                  <li>
	                    If a field for auto-population could be either Buyer or
	                    Seller side, if it is your client then use the Client form
	                    fields.
	                  </li>
                    <li>For deadline extraction, the field name must match the list shown below.</li>
	                </ol>
	              </p>
	              <p><Grid>
                  <Grid.Column width={7}>
	                <span className="bold text blue mini bottom margin">
	                  Form Field Names in the PDF to enable auto-population:</span>
	                <div style={{ maxHeight: "150px", maxWidth: "350px", overflowY: "auto" }}>
	                  {fieldConversionMappings.map((mapping) => (
	                    <React.Fragment key={mapping.pdfFieldName}>
	                      {mapping.pdfFieldName}
	                      <br />
	                    </React.Fragment>
	                  ))}
	                </div>
                  </Grid.Column>
                  <Grid.Column width={8}>
	                <span className="bold text blue mini bottom margin">
	                  Date and Deadline Extractions:</span>
	                <div style={{ maxHeight: "150px", maxWidth: "450px", overflowY: "auto" }}>
                    {Object.values(deadlineTemplatesFormFieldMapping).map((value, index) => (
                      <React.Fragment key={index}>
	                      {value}
	                      <br />
	                    </React.Fragment>
                    ))}
	                  
                    </div>
                    </Grid.Column>
                  </Grid>
	              </p>
	            </div>
	          )}
          <Segment clearing>
            {brokerageForms?.length > 0 ? (
              <BrokerageFormsList
                brokerageForms={brokerageForms}
                column={brokerageForms.column}
                direction={brokerageForms.direction}
              />
            ) : (
                <p>There are no brokerage forms configured.
                  <br /> <br />
                  If you have brokerage-specific forms set up inside TransActioner, <NAME_EMAIL> to get set up to access your forms here.
                  <br /> <br />
                  If you want your brokerage-specific forms set up inside TransActioner, please email your forms and brokerage <NAME_EMAIL>.
              </p>
            )}
          </Segment>
        </Grid.Column>
      </Grid>
    </div>
  );
}
