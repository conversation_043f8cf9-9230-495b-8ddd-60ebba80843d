import React, { useState } from "react";
import { Grid, Input, Button } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import {
  searchFilter,
  downloadEmailsAsPdf,
} from "../../../app/common/util/util";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import EmailsList from "./EmailList";
import useFirestoreCollectionNoAsync from "../../../app/hooks/useFirestoreCollectionNoAsync";
import { fetchEmailsFromDb } from "../../../app/firestore/firestoreService";
import { fetchEmails } from "../emailSlice";
import { toast } from "react-toastify";
import { useMediaQuery } from "react-responsive";

export default function EmailHistoryPage() {
  const dispatch = useDispatch();
  const { emailsAll } = useSelector((state) => state.email);
  const { transaction } = useSelector((state) => state.transaction);
  const [searchTerms, setSearchTerms] = useState("");
  const [downloadingEmails, setDownloadingEmails] = useState(false);
  const emails = searchFilter(emailsAll.emails, searchTerms);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  let { id } = useParams();

  async function handleDownloadEmails() {
    if (!emails || emails.length === 0) {
      toast.error("No email data available to download");
      return;
    }

    setDownloadingEmails(true);
    try {
      downloadEmailsAsPdf(emails, transaction);
      toast.success("Email history PDF downloaded successfully!");
    } catch (error) {
      console.error("Error downloading emails:", error);
      toast.error(error.message || "Failed to download email history");
    } finally {
      setDownloadingEmails(false);
    }
  }

  useFirestoreCollectionNoAsync({
    query: () => fetchEmailsFromDb(id),
    data: (emailsFromDb) => dispatch(fetchEmails(emailsFromDb)),
    deps: [dispatch],
  });

  if (!emailsAll) {
    return <LoadingComponent />;
  }

  return (
    <div className="secondary-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={6}>
          <Input
            type="text"
            fluid
            placeholder="Search by name, email, or text in a message"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={4}>
          <Button.Group fluid size="small">
            <Button as={Link} to={`/transactions/${id}/emails`}>
              Send Email
            </Button>
            <Button active as={Link} to="">
              Email History
            </Button>
          </Button.Group>
        </Grid.Column>
        <Grid.Column computer={6}>
          <div className={isMobile ? null : "float-right"}>
            <Button
              icon="download"
              size="small"
              className={isMobile ? "fluid" : null}
              onClick={handleDownloadEmails}
              content="Download Emails"
              color="teal"
              loading={downloadingEmails}
              disabled={downloadingEmails || !emails || emails.length === 0}
            />
          </div>
        </Grid.Column>
        <Grid.Column computer={16}>
          <h3>Email History</h3>
          {emails.length > 0 ? (
            <EmailsList
              emails={emails}
              column={emailsAll.column}
              direction={emailsAll.direction}
            />
          ) : (
            <p>There are no emails for this transaction</p>
          )}
        </Grid.Column>
      </Grid>
    </div>
  );
}
