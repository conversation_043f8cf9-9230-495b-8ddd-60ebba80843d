import React from "react";
import { Table } from "semantic-ui-react";
import TaskTemplatesListItem from "./TaskTemplatesListItem";

export default function TaskTemplatesList({
  taskTemplates,
  column,
  direction,
  actionButtonMode,
}) {
  return (
    <>
      <Table compact sortable className="mini top margin">
        <Table.Header className="mobile hidden">
          <Table.Row className="small-header">
            <Table.HeaderCell sorted={column === "title" ? direction : null}>
              Name
            </Table.HeaderCell>
            <Table.HeaderCell sorted={column === "propertyType" ? direction : null}>
              Property Type
            </Table.HeaderCell>
            <Table.HeaderCell sorted={column === "autoAddToBuyerTransactions" ? direction : null}>
              Auto-add to Buyer Transactions
            </Table.HeaderCell>
            <Table.HeaderCell sorted={column === "autoAddToSellerTransactions" ? direction : null}>
              Auto-add to Seller Transactions
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "updatedAt" ? direction : null}
            >
              Last Updated
            </Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {taskTemplates &&
            taskTemplates.length !== 0 &&
            taskTemplates.map((taskTemplate) => (
              <TaskTemplatesListItem
                taskTemplate={taskTemplate}
                key={taskTemplate.id}
                actionButtonMode={actionButtonMode}
              />
            ))}
        </Table.Body>
      </Table>
    </>
  );
}
