import { createSlice } from "@reduxjs/toolkit";
import _ from "lodash";

const initialState = {
  taskTemplates: [],
  taskTemplate: {},
};

const taskTemplatesSlice = createSlice({
  name: "taskTemplates",
  initialState,
  reducers: {
    fetchTaskTemplates(state, action) {
      state.taskTemplates = _.sortBy(action.payload, "title");
    },
    fetchTaskTemplate(state, action) {
      state.taskTemplate = action.payload;
    },
    updateTaskTemplateTitle(state, action) {
      state.taskTemplate.title = action.payload;
    },
    updateTaskTemplatePropertyType(state, action) {
      state.taskTemplate.propertyType = action.payload;
    },
    updateTaskTemplateAutoAdd(state, action) {
      state.taskTemplate.autoAddToBuyerTransactions = action.payload.autoAddToBuyerTransactions;
      state.taskTemplate.autoAddToSellerTransactions = action.payload.autoAddToSellerTransactions;
    },
    addItemToTaskTemplate(state, action) {
      state.taskTemplate.items = [...state.taskTemplate.items, action.payload];
    },
    updateItemInTaskTemplate(state, action) {
      state.taskTemplate.items.splice(
        action.payload.index,
        1,
        action.payload.item
      );
    },
    deleteItemInTaskTemplate(state, action) {
      state.taskTemplate.items.splice(action.payload, 1);
    },
    moveItemInTaskTemplate(state, action) {
      const { fromIndex, toIndex } = action.payload;
      const items = [...state.taskTemplate.items];
      const [movedItem] = items.splice(fromIndex, 1);
      items.splice(toIndex, 0, movedItem);
      state.taskTemplate.items = items;
    },
  },
});

export const {
  fetchTaskTemplates,
  fetchTaskTemplate,
  updateTaskTemplateTitle,
  updateTaskTemplatePropertyType,
  updateTaskTemplateAutoAdd,
  addItemToTaskTemplate,
  updateItemInTaskTemplate,
  deleteItemInTaskTemplate,
  moveItemInTaskTemplate,
} = taskTemplatesSlice.actions;

export default taskTemplatesSlice.reducer;
