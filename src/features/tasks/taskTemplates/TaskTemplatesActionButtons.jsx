import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Confirm, Dropdown, Button } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import _ from "lodash";
import {
  addTaskTemplateToDb,
  addTaskToDb,
  deleteTaskTemplateInDb,
} from "../../../app/firestore/firestoreService";
import { closeModal, openModal } from "../../../app/common/modals/modalSlice";
import { fetchTaskTemplate } from "./taskTemplatesSlice";
import { addDays, subDays } from "date-fns";

export default function TaskTemplatesItemActionButtons({
  taskTemplate,
  actionButtonMode,
}) {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleEdit() {
    dispatch(fetchTaskTemplate(taskTemplate));
    dispatch(
      openModal({
        modalType: "TaskTemplatesEdit",
        modalProps: { newTemplate: false },
      })
    );
  }

  function handleChoose() {
    if (taskTemplate?.items?.length > 0) {
      taskTemplate.items.forEach((task) => {
        let taskCopy = _.cloneDeep(task);
        taskCopy["category"] = "Task";
        taskCopy["title"] = taskCopy.name;
        taskCopy["end"] = "";
        // NEED TO DETERMINE TASK DEADLINE HERE
        if (task.dateType && task.beforeOnAfter) {
          // Get Actual Date
          let dateTypeConverted = "";
          let taskDeadlineDate = "";
          if (task.dateType) {
            dateTypeConverted =
              _.lowerCase(task.dateType.split(" ")[0]) + "DateTime";
            if (task.dateType === "Transaction Created Date") {
              dateTypeConverted = "createdAt";
            }
            taskDeadlineDate = transaction[dateTypeConverted];
          }
          if (taskDeadlineDate) {
            if (task.beforeOnAfter === "Before" && task.days) {
              taskCopy["end"] = subDays(taskDeadlineDate, task.days);
            } else if (task.beforeOnAfter === "On") {
              taskCopy["end"] = taskDeadlineDate;
            } else if (task.beforeOnAfter === "After" && task.days) {
              taskCopy["end"] = addDays(taskDeadlineDate, task.days);
            }
          }
        }
        taskCopy["start"] = taskCopy["end"];

        // Handle assignedTo field
        if (task.assignedTo && task.assignedTo.length > 0) {
          // Initialize an empty array for the assignedTo IDs
          let assignedToIds = [];

          // Process each assignee
          task.assignedTo.forEach((assignee) => {
            // Check if it's a Manager Assistant
            if (transaction?.agentProfile?.assistants?.length > 0) {
              transaction.agentProfile.assistants.forEach((assistant) => {
                if (
                  assignee.startsWith(
                    assistant?.title + ": " + assistant?.firstName
                  ) &&
                  assistant?.userId
                ) {
                  assignedToIds.push(assistant.userId);
                }
              });
            }

            if (assignee.startsWith("ManagerAssistant-")) {
              const assistantId = assignee.replace("ManagerAssistant-", "");
              assignedToIds.push(assistantId);
            } else {
              // Convert role name to actual user ID
              switch (assignee) {
                case "Agent":
                  if (transaction?.agentProfile?.id || transaction?.agentId) {
                    assignedToIds.push(
                      transaction?.agentProfile?.id || transaction?.agentId
                    );
                  }
                  break;
                case "Co-Agent":
                  if (transaction?.coAgent?.userId || transaction.coAgentId) {
                    assignedToIds.push(
                      transaction.coAgent?.userId || transaction.coAgentId
                    );
                  }
                  break;
                case "Transaction Coordinator":
                  if (
                    transaction?.transactionCoordinator?.userId ||
                    transaction?.tcId
                  ) {
                    assignedToIds.push(
                      transaction.transactionCoordinator?.userId ||
                        transaction.tcId
                    );
                  }
                  break;
                case "Manager":
                  if (transaction?.managerId) {
                    assignedToIds.push(transaction.managerId);
                  }
                  break;
                default:
                  if (assignee) {
                    assignedToIds.push(assignee);
                  }
                  break;
              }
            }
          });

          // Assign the collected IDs to the task
          taskCopy["assignedTo"] = assignedToIds;
        } else {
          taskCopy["assignedTo"] = [];
        }

        if (task.visibleTo && task.visibleTo.length > 0) {
          const visibleToIds = [];
          // Convert role name to actual user ID

          // Process each visibility entry
          task.visibleTo.forEach((person) => {
            if (transaction?.agentProfile?.assistants?.length > 0) {
              transaction.agentProfile.assistants.forEach((assistant) => {
                if (
                  person.startsWith(
                    assistant?.title + ": " + assistant?.firstName
                  ) &&
                  assistant?.userId
                ) {
                  visibleToIds.push(assistant.userId);
                }
              });
            }
            switch (person) {
              case "Agent":
                if (transaction?.agentProfile?.id || transaction?.agentId) {
                  visibleToIds.push(
                    transaction?.agentProfile?.id || transaction?.agentId
                  );
                }
                break;
              case "Co-Agent":
                if (transaction?.coAgent?.userId || transaction.coAgentId) {
                  visibleToIds.push(
                    transaction.coAgent?.userId || transaction.coAgentId
                  );
                }
                break;
              case "Transaction Coordinator":
                if (
                  transaction?.transactionCoordinator?.userId ||
                  transaction?.tcId
                ) {
                  visibleToIds.push(
                    transaction.transactionCoordinator?.userId ||
                      transaction.tcId
                  );
                }
                break;
              case "Manager":
                if (transaction?.managerId) {
                  visibleToIds.push(transaction.managerId);
                }
                break;
              default:
                if (person) {
                  visibleToIds.push(person);
                }
                break;
            }
          });

          // Assign the collected IDs to the task
          taskCopy["visibleTo"] = visibleToIds;
        } else {
          taskCopy["visibleTo"] = [];
        }

        addTaskToDb(taskCopy, transaction);
      });
    }
    dispatch(
      closeModal({
        modalType: "TaskTemplatesEdit",
      })
    );
  }

  function handleCopy() {
    addTaskTemplateToDb(taskTemplate);
  }

  function handleDelete() {
    deleteTaskTemplateInDb(taskTemplate.id);
  }

  if (actionButtonMode === "apply") {
    return (
      <div className={isMobile ? null : "text align right"}>
        <Button primary onClick={() => handleChoose()}>
          Choose
        </Button>
      </div>
    );
  } else {
    return (
      <div className={isMobile ? null : "text align right"}>
        <Dropdown
          button
          icon="chevron down"
          text={isMobile ? "Actions " : null}
          className={isMobile ? "button icon" : "button mini icon"}
          direction={isMobile ? "right" : "left"}
        >
          <Dropdown.Menu>
            <Dropdown.Item onClick={() => handleEdit()}>Edit</Dropdown.Item>
            <Dropdown.Item onClick={() => handleCopy()}>Copy</Dropdown.Item>
            <Dropdown.Item onClick={() => setConfirmOpen(true)}>
              Delete
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
        <Confirm
          open={confirmOpen}
          onCancel={() => setConfirmOpen(false)}
          onConfirm={() => handleDelete()}
        ></Confirm>
      </div>
    );
  }
}
