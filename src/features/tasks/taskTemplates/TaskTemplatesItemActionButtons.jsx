import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { Confirm, Dropdown } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import {
  addItemToTaskTemplate,
  deleteItemInTaskTemplate,
} from "./taskTemplatesSlice";

export default function TaskTemplatesActionButtons({
  item,
  index,
  setInitialValues,
  setItemModal,
}) {
  const dispatch = useDispatch();

  const [confirmOpen, setConfirmOpen] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleEdit() {
    setInitialValues(item);
    setItemModal({ open: true, new: false, editingId: index });
  }

  function handleCopy() {
    dispatch(addItemToTaskTemplate(item));
  }

  function handleDelete() {
    dispatch(deleteItemInTaskTemplate(index));
    setConfirmOpen(false);
  }

  return (
    <div className={isMobile ? null : "text align right"}>
      <Dropdown
        button
        icon="chevron down"
        text={isMobile ? "Actions " : null}
        className={isMobile ? "button icon" : "button mini icon"}
        direction={isMobile ? "right" : "left"}
      >
        <Dropdown.Menu>
          <Dropdown.Item onClick={() => handleEdit()}>Edit</Dropdown.Item>
          <Dropdown.Item onClick={() => handleCopy()}>Copy</Dropdown.Item>
          <Dropdown.Item onClick={() => setConfirmOpen(true)}>
            Delete
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
      <Confirm
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={() => handleDelete()}
      ></Confirm>
    </div>
  );
}
