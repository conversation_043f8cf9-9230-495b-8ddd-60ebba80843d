import React from "react";
import { useDispatch } from "react-redux";
import { Table } from "semantic-ui-react";
import { sortTasksTransActive } from "../../taskSlice";
import TaskTransActiveWithDateListItem from "./TaskTransListWithDateListItem";

export default function TaskTransActiveWithDateList({
  tasks,
  column,
  direction,
}) {
  const dispatch = useDispatch();

  return (
    <>
      <Table compact sortable className="mini top margin">
        <Table.Header className="mobile hidden">
          <Table.Row className="small-header">
            <Table.HeaderCell
              sorted={column === "end" ? direction : null}
              onClick={() => dispatch(sortTasksTransActive("end"))}
            >
              Deadline
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "title" ? direction : null}
              onClick={() => dispatch(sortTasksTransActive("title"))}
            >
              Name
            </Table.HeaderCell>
            <Table.HeaderCell>Assigned To</Table.HeaderCell>
            <Table.HeaderCell>Date Marked complete</Table.HeaderCell>

            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {tasks.length !== 0 &&
            tasks.map((task) => (
              <TaskTransActiveWithDateListItem task={task} key={task.id} />
            ))}
        </Table.Body>
      </Table>
    </>
  );
}
