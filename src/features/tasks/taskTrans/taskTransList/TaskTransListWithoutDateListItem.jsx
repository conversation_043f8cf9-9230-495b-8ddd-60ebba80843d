import React from "react";
import { Table, Popup, Icon } from "semantic-ui-react";
import TaskActionButtons from "../../taskComponents/TaskActionButtons";
import { useSelector } from "react-redux";
import { dateFormatDeadline } from "../../../../app/common/util/util";

export default function TaskTransActiveWithoutDateListItem({ task, complete }) {
  const { transaction } = useSelector((state) => state.transaction);

  function getAssignedToList(assignedTo) {
    if (!assignedTo || assignedTo.length === 0) return;

    let listOfAssignments = [];
    if (
      (transaction.agentId?.length > 0 &&
        assignedTo.includes(transaction.agentId)) ||
      (transaction.agentProfile?.id?.length > 0 &&
        assignedTo.includes(transaction.agentProfile.id)) ||
      (transaction.agentProfile?.userId?.length > 0 &&
        assignedTo.includes(transaction.agentProfile.userId))
    )
      listOfAssignments.push("Agent");

    if (
      (transaction.coAgentId?.length > 0 &&
        assignedTo.includes(transaction.coAgentId)) ||
      (transaction?.coAgent?.userId?.length > 0 &&
        assignedTo.includes(transaction.coAgent.userId))
    ) {
      listOfAssignments.push("CoAgent");
    }

    if (
      (transaction.transactionCoordinator?.userId?.length > 0 &&
        assignedTo.includes(transaction.transactionCoordinator.userId)) ||
      (transaction.tcId?.length > 0 && assignedTo.includes(transaction.tcId))
    ) {
      listOfAssignments.push("TC");
    }
    if (
      transaction.managerId?.length > 0 &&
      assignedTo.includes(transaction.managerId)
    ) {
      listOfAssignments.push("Manager");
    }
    if (assignedTo.includes(transaction.assistantId))
      listOfAssignments.push("Assistant");

    // Check for Manager Assistants
    if (
      transaction.agentProfile?.managerAssistants &&
      transaction.agentProfile.managerAssistants.length > 0
    ) {
      transaction.agentProfile.managerAssistants.forEach((assistant) => {
        if (assistant.userId && assignedTo.includes(assistant.userId)) {
          listOfAssignments.push(assistant.title || "Manager Assistant");
        }
      });
    }

    return listOfAssignments.join(", ");
  }

  return (
    <Table.Row
      key={task.id}
      className={(task.status === "Complete" && "background-green") || ""}
    >
      <Table.Cell>
        {task.title}{" "}
        {task.description && (
          <Popup
            trigger={
              <Icon
                name="info"
                color="blue"
                circular
                size="small"
                style={{ marginLeft: "3px", marginBottom: "3px" }}
              />
            }
          >
            {task.description}
          </Popup>
        )}
      </Table.Cell>
      <Table.Cell>
        {task.assignedTo?.length > 0 && getAssignedToList(task.assignedTo)}
      </Table.Cell>
      {complete && (
        <Table.Cell>
          {task.dateMarkedComplete
            ? dateFormatDeadline(task.dateMarkedComplete)
            : ""}
        </Table.Cell>
      )}

      <Table.Cell>
        <TaskActionButtons task={task} />
      </Table.Cell>
    </Table.Row>
  );
}
