import React from "react";
import { isSameDay } from "date-fns";
import { Table, Popup, Icon } from "semantic-ui-react";
import { dateFormatDeadline } from "../../../../app/common/util/util";
import TaskActionButtons from "../../taskComponents/TaskActionButtons";
import { useSelector } from "react-redux";

export default function TaskTransActiveWithDateListItem({ task }) {
  const { transaction } = useSelector((state) => state.transaction);

  function getAssignedToList(assignedTo) {
    if (!assignedTo || assignedTo.length === 0) return;

    let listOfAssignments = [];
    if (
      assignedTo.includes(transaction.agentId) ||
      assignedTo.includes(transaction.agentProfile?.id)
    )
      listOfAssignments.push("Agent");
    if (transaction.coAgentId && assignedTo.includes(transaction.coAgentId))
      listOfAssignments.push("CoAgent");
    if (
      transaction.transactionCoordinator?.userId &&
      assignedTo.includes(transaction.transactionCoordinator?.userId)
    )
      listOfAssignments.push("TC");
    else if (transaction.tcId && assignedTo.includes(transaction.tcId)) {
      listOfAssignments.push("TC");
    }
    if (transaction.managerId && assignedTo.includes(transaction.managerId))
      listOfAssignments.push("Manager");
    if (assignedTo.includes(transaction.assistantId))
      listOfAssignments.push("Assistant");

    // Check for Manager Assistants
    if (
      transaction.agentProfile?.managerAssistants &&
      transaction.agentProfile.managerAssistants.length > 0
    ) {
      transaction.agentProfile.managerAssistants.forEach((assistant) => {
        if (assistant.userId && assignedTo.includes(assistant.userId)) {
          listOfAssignments.push(assistant.title || "Manager Assistant");
        }
      });
    }

    return listOfAssignments.join(", ");
  }

  return (
    <Table.Row
      key={task.id}
      className={
        task.status === "Complete"
          ? "background-green"
          : isSameDay(task.end, new Date())
          ? "background-blue"
          : // : isPast(task.end)
            // ? "background-yellow"
            ""
      }
    >
      <Table.Cell>{task.end && dateFormatDeadline(task.end)}</Table.Cell>
      <Table.Cell>
        {task.title}{" "}
        {task.description && (
          <Popup
            trigger={
              <Icon
                name="info"
                color="blue"
                circular
                size="small"
                style={{ marginLeft: "3px", marginBottom: "3px" }}
              />
            }
          >
            {task.description}
          </Popup>
        )}
      </Table.Cell>
      <Table.Cell>
        {task.assignedTo?.length > 0 && getAssignedToList(task.assignedTo)}
      </Table.Cell>
      <Table.Cell>
        {task.dateMarkedComplete
          ? dateFormatDeadline(task.dateMarkedComplete)
          : ""}
      </Table.Cell>
      <Table.Cell>
        <TaskActionButtons task={task} />
      </Table.Cell>
    </Table.Row>
  );
}
