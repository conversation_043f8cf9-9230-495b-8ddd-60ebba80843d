import React from "react";
import { isSameDay } from "date-fns";
import { Table } from "semantic-ui-react";
import TaskActionButtons from "./TaskActionButtons";
import { dateFormatDeadline } from "../../../app/common/util/util";
import { isPast } from "date-fns";

export default function TaskTransTaskListItem({ task }) {
  return (
    <Table.Row
      key={task.id}
      className={
        (isSameDay(task.end, new Date()) && "background-blue") ||
        (isPast(task.end) && "background-yellow") ||
        ""
      }
    >
      <Table.Cell>{task.title}</Table.Cell>
      <Table.Cell>{task.description}</Table.Cell>
      <Table.Cell>{task.archived ? "Archived" : task.status}</Table.Cell>
      <Table.Cell>{task.end && dateFormatDeadline(task.end)}</Table.Cell>
      <Table.Cell>{task.assignedTo}</Table.Cell>
      <Table.Cell>
        <TaskActionButtons task={task} />
      </Table.Cell>
    </Table.Row>
  );
}
