import { Formik, Form } from "formik";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import * as Yup from "yup";
import MyTextInput from "../../../app/common/form/MyTextInput";
import {
  addTaskToDb,
  updateTaskInDb,
} from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import MyDateInput from "../../../app/common/form/MyDateInput";
import { useMediaQuery } from "react-responsive";

export default function TaskDeadlineForm({ deadline }) {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  let initialValues = deadline
    ? deadline
    : {
        category: "Deadline",
        title: "",
        end: "",
        assignedTo:
          transaction.agentRepresents === "Buyer"
            ? "Buyer Agent"
            : "Listing Agent",
      };

  const validationSchema = Yup.object({
    title: Yup.string().required("You must provide a name"),
    end: Yup.string().required("You must provide a due date"),
  });

  return (
    <ModalWrapper size="tiny">
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            validateOnChange={false}
            validateOnBlur={false}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                values.start = values.end;
                deadline
                  ? await updateTaskInDb(deadline.id, values)
                  : await addTaskToDb(values, transaction);
                setSubmitting(false);
                deadline
                  ? toast.success("Deadline successfully updated")
                  : toast.success("Deadline successfully created");
                dispatch(
                  closeModal({
                    modalType: "TaskDeadlineForm",
                  })
                );
              } catch (error) {
                toast.error(error.message);
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, dirty }) => (
              <Form className="ui form" autoComplete="off">
                <Header size="large" color="blue">
                  {deadline ? `Edit Deadline` : `Add Deadline`}
                </Header>
                <Divider />
                <h4 className="zero top margin tiny bottom margin">Name</h4>
                <MyTextInput name="title" data-test="task-name-input" />

                <h4 className="zero top margin tiny bottom margin">Deadline</h4>
                <MyDateInput
                  name="end"
                  placeholder="End date"
                  dateFormat="MMMM d, yyyy"
                  data-test="task-date-input"
                />
                <Divider className="medium top margin medium bottom margin" />
                <div>
                  <Button
                    primary
                    loading={isSubmitting}
                    disabled={!dirty || isSubmitting}
                    type="submit"
                    floated={isMobile ? null : "right"}
                    content="Submit"
                    className={isMobile ? "fluid medium bottom margin" : null}
                    data-test="task-submit-btn"
                  />
                  <Button
                    type="button"
                    disabled={isSubmitting}
                    onClick={() =>
                      dispatch(
                        closeModal({
                          modalType: "TaskDeadlineForm",
                        })
                      )
                    }
                    floated={isMobile ? null : "right"}
                    content="Cancel"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Segment>
    </ModalWrapper>
  );
}
