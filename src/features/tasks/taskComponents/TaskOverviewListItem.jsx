import { isPast, isSameDay } from "date-fns";
import React from "react";
import { Table } from "semantic-ui-react";
import { dateFormatDeadline } from "../../../app/common/util/util";
import TaskActionButtons from "./TaskActionButtons";

export default function TaskOverviewListItem({ task }) {
  return (
    <Table.Row
      key={task.id}
      className={
        (isSameDay(task.end, new Date()) && "background-blue") ||
        (isPast(task.end) && "background-yellow") ||
        ""
      }
    >
      <Table.Cell>{task.title}</Table.Cell>
      <Table.Cell>{task.transactionTitle}</Table.Cell>
      <Table.Cell>{dateFormatDeadline(task.end)}</Table.Cell>
      <Table.Cell>
        <TaskActionButtons task={task} />
      </Table.Cell>
    </Table.Row>
  );
}
