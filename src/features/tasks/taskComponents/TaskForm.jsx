import { Formik, Form } from "formik";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import * as Yup from "yup";
import MyTextInput from "../../../app/common/form/MyTextInput";
import {
  addTaskToDb,
  updateTaskInDb,
} from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import MyDateInput from "../../../app/common/form/MyDateInput";
import { useMediaQuery } from "react-responsive";
import MyTextArea from "../../../app/common/form/MyTextArea";
import MySelectInput from "../../../app/common/form/MySelectInput";
import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function TaskForm({ task }) {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const { currentUserProfile } = useSelector((state) => state.profile);

  if (!transaction) {
    return <LoadingComponent />;
  }

  let initialValues = task
    ? task
    : {
        category: "Task",
        title: "",
        description: "",
        start: null,
        end: null,
        assignedTo: [],
        visibleTo: [],
        dateMarkedComplete: "",
      };

  const assignToOptions = getAssignToOptions();
  const visibleToOptions = getAssignToOptions();

  function getAgentOptionText() {
    return (
      "Agent" +
      (transaction?.agentProfile?.lastName
        ? ": " +
          transaction?.agentProfile?.firstName +
          " " +
          transaction?.agentProfile?.lastName
        : "")
    );
  }
  function getCoAgentOptionText() {
    return (
      "Co-Agent" +
      (transaction?.coAgent?.lastName
        ? ": " +
          transaction?.coAgent?.firstName +
          " " +
          transaction?.coAgent?.lastName
        : "")
    );
  }
  function getTcOptionText() {
    return (
      "TC" +
      (transaction?.transactionCoordinator?.lastName
        ? ": " +
          transaction?.transactionCoordinator?.firstName +
          " " +
          transaction?.transactionCoordinator?.lastName
        : "")
    );
  }

  function getManagerAssistantOptionText(managerAssistant) {
    return (
      (managerAssistant.title || "Manager Assistant") +
      (managerAssistant.lastName
        ? ": " + managerAssistant.firstName + " " + managerAssistant.lastName
        : "")
    );
  }

  function getAssignToOptions() {
    let options = [
      {
        key: "",
        value: "",
        text: "",
      },
      {
        key: "agent",
        value: transaction?.agentProfile?.id || transaction?.agentId || "",
        text: getAgentOptionText(),
      },
    ];
    if (
      transaction?.coAgent?.userId?.length > 0 ||
      transaction.coAgentId?.length > 0
    ) {
      options.push({
        key: "coagent",
        value: transaction.coAgent?.userId || transaction.coAgentId || "",
        text: getCoAgentOptionText(),
      });
    }
    if (
      transaction?.transactionCoordinator?.userId?.length > 0 ||
      transaction?.tcId?.length > 1
    ) {
      options.push({
        key: "tc",
        value:
          transaction.transactionCoordinator?.userId || transaction.tcId || "",
        text: getTcOptionText(),
      });
    }
    if (transaction?.managerId?.length > 1) {
      options.push({
        key: "manager",
        value: transaction.managerId || "",
        text: "Manager",
      });
    }

    // Add Manager Assistants if they exist
    if (
      transaction?.agentProfile?.managerAssistants &&
      transaction.agentProfile.managerAssistants.length > 0
    ) {
      transaction.agentProfile.managerAssistants.forEach((assistant, index) => {
        if (assistant.userId) {
          options.push({
            key: `managerassistant-${index}`,
            value: assistant.userId,
            text: getManagerAssistantOptionText(assistant),
          });
        }
      });
    }

    // Add the current user if they are a Manager Assistant
    if (
      currentUserProfile?.role === "managerassistant" &&
      currentUserProfile?.userId &&
      currentUserProfile?.authCustomClaims?.m
    ) {
      options.push({
        key: "currentManagerAssistant",
        value: currentUserProfile.userId,
        text: `${currentUserProfile.title || "Manager Assistant"}: ${
          currentUserProfile.firstName || ""
        } ${currentUserProfile.lastName || ""}`,
      });
    }

    return options;
  }

  const validationSchema = Yup.object({
    title: Yup.string().required("You must provide a name"),
  });

  return (
    <ModalWrapper size="tiny">
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            validateOnChange={false}
            validateOnBlur={false}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                values.start = values.end;
                task
                  ? await updateTaskInDb(task.id, values)
                  : await addTaskToDb(values, transaction);
                setSubmitting(false);
                task
                  ? toast.success("Deadline successfully updated")
                  : toast.success("Deadline successfully created");
                dispatch(
                  closeModal({
                    modalType: "TaskForm",
                  })
                );
              } catch (error) {
                toast.error(error.message);
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, dirty }) => (
              <Form className="ui form" autoComplete="off">
                <Header size="large" color="blue">
                  {task ? `Edit Task` : `Add Task`}
                </Header>
                <Divider />
                <h4 className="zero top margin tiny bottom margin">Name</h4>
                <MyTextInput name="title" data-test="task-name-input" />
                <h4 className="zero top margin tiny bottom margin">
                  Description (optional)
                </h4>
                <MyTextArea rows={4} name="description" />
                <h4 className="zero top margin tiny bottom margin">
                  Deadline (optional)
                </h4>
                <MyDateInput
                  name="end"
                  placeholder="End date"
                  dateFormat="MMMM d, yyyy"
                  data-test="task-date-input"
                />
                <MySelectInput
                  name={"assignedTo"}
                  label="Assign To"
                  options={assignToOptions}
                  multiple={true}
                ></MySelectInput>

                <MySelectInput
                  name={"visibleTo"}
                  label="Visible To (leave empty for everyone)"
                  options={visibleToOptions}
                  multiple={true}
                ></MySelectInput>

                <Divider className="medium top margin medium bottom margin" />
                <div>
                  <Button
                    primary
                    loading={isSubmitting}
                    disabled={!dirty || isSubmitting}
                    type="submit"
                    floated={isMobile ? null : "right"}
                    content="Submit"
                    className={isMobile ? "fluid medium bottom margin" : null}
                    data-test="task-submit-btn"
                  />
                  <Button
                    type="button"
                    disabled={isSubmitting}
                    onClick={() =>
                      dispatch(
                        closeModal({
                          modalType: "TaskForm",
                        })
                      )
                    }
                    floated={isMobile ? null : "right"}
                    content="Cancel"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Segment>
    </ModalWrapper>
  );
}
