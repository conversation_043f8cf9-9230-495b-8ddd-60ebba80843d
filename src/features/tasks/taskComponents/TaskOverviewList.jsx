import React from "react";
import { Table } from "semantic-ui-react";
import TaskOverviewListItem from "./TaskOverviewListItem";

export default function TaskOverviewList({ tasks }) {
  return (
    <>
      <Table compact>
        <Table.Header className="mobile hidden">
          <Table.Row className="small-header">
            <Table.HeaderCell>Name</Table.HeaderCell>
            <Table.HeaderCell>Transaction</Table.HeaderCell>
            <Table.HeaderCell>Deadline</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {tasks.length !== 0 &&
            tasks.map((task) => (
              <TaskOverviewListItem task={task} key={task.id} />
            ))}
        </Table.Body>
      </Table>
    </>
  );
}
