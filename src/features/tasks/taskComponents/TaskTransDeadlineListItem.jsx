import React from "react";
import { isSameDay } from "date-fns";
import { Table, Popup, Label } from "semantic-ui-react";
import TaskActionButtons from "./TaskActionButtons";
import {
  convertFullName,
  convertRoleToSharingDisplay,
  convertSharingWithToColor,
  dateFormatDeadline,
  partyIsAgent,
  convertPartiesAbleToShare,
} from "../../../app/common/util/util";
import { isPast } from "date-fns";
import { useDispatch, useSelector } from "react-redux";
import {
  updateTaskAddSharingInDb,
  updateTaskInDb,
  updateTaskRemoveSharingInDb,
  updateTransAddSharingInDb,
} from "../../../app/firestore/firestoreService";
import _ from "lodash";
import { openModal } from "../../../app/common/modals/modalSlice";
import { functionShareTaskWithAgent } from "../../../app/firestore/functionsService";
import { serverTimestamp } from "firebase/firestore";

export default function TaskTransDeadlineListItem({ deadline }) {
  const dispatch = useDispatch();
  const { transaction, allParties } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);

  function handleSharingClick(party) {
    const partyRoleCamel = _.camelCase(party.role);
    if (deadline.sharingWithRole?.[partyRoleCamel]) {
      try {
        updateTaskRemoveSharingInDb(deadline.id, party, transaction);
      } catch (error) {
        throw error;
      }
    } else {
      if (
        partyIsAgent(party.role) &&
        party.isUser &&
        !party.isLinked &&
        !party.declinedLinking
      ) {
        dispatch(
          openModal({
            modalType: "TaskShareAgentWarning",
            modalProps: {
              task: deadline,
              party: party,
              transaction: transaction,
            },
          })
        );
      } else {
        try {
          updateTaskAddSharingInDb(deadline.id, party, transaction);
          updateTransAddSharingInDb(transaction.id, party, transaction);
        } catch (error) {
          throw error;
        }
        if (partyIsAgent(party.role) && party.isUser && party.isLinked) {
          updateTaskInDb(deadline.id, {
            sentToAgent: true,
            sentToAgentId: party.id,
            sentToAgentAt: serverTimestamp(),
          });
          const sharedByName = convertFullName(currentUserProfile);
          functionShareTaskWithAgent({
            deadline: deadline,
            party: party,
            sharedBy: sharedByName,
          });
        }
      }
    }
  }

  return (
    <Table.Row
      key={deadline.id}
      className={
        (isSameDay(deadline.end, new Date()) && "background-blue") ||
        (isPast(deadline.end) && "background-yellow") ||
        ""
      }
    >
      <Table.Cell>{deadline.title}</Table.Cell>
      <Table.Cell>
        {deadline.archived ? "Archived" : deadline.status}
      </Table.Cell>
      <Table.Cell>{dateFormatDeadline(deadline.end)}</Table.Cell>
      <Table.Cell>
        {convertPartiesAbleToShare(allParties, transaction.agentRepresents).map(
          (party) => (
            <React.Fragment key={party.role}>
              {party.email && (
                <Popup
                  trigger={
                    <Label
                      key={party.role}
                      style={{ cursor: "pointer", marginBottom: "2px" }}
                      color={convertSharingWithToColor(deadline, party)}
                      onClick={() => handleSharingClick(party)}
                    >
                      {convertRoleToSharingDisplay(party.role)}
                    </Label>
                  }
                >
                  <p>
                    {party.role}:
                    <br />
                    {convertFullName(party)}
                  </p>
                </Popup>
              )}
            </React.Fragment>
          )
        )}
      </Table.Cell>
      <Table.Cell>
        <TaskActionButtons task={deadline} />
      </Table.Cell>
    </Table.Row>
  );
}
