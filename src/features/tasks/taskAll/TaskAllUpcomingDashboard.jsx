import React from "react";
import { Grid, Input } from "semantic-ui-react";
import { useSelector } from "react-redux";
import { useState } from "react";
import { searchFilter } from "../../../app/common/util/util";
import TaskAllUpcomingList from "./TaskAllUpcomingList";

export default function TaskAllUpcomingDashboard() {
  const { tasksUpcoming } = useSelector((state) => state.task);
  const [searchTerms, setSearchTerms] = useState("");
  const tasks = searchFilter(tasksUpcoming.tasks, searchTerms);

  return (
    <div className="main-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={8}>
          <Input
            type="text"
            fluid
            placeholder="Search by name or status"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={16}>
          <h3>Upcoming Deadlines</h3>
          {tasks?.length > 0 ? (
            <TaskAllUpcomingList
              tasks={tasks}
              column={tasksUpcoming.column}
              direction={tasksUpcoming.direction}
            />
          ) : (
            <p>There are no upcoming deadlines</p>
          )}
        </Grid.Column>
      </Grid>
    </div>
  );
}
