import React from "react";
import { useDispatch } from "react-redux";
import { Table } from "semantic-ui-react";
import { sortTasksUpcoming } from "../taskSlice";
import TaskAllUpcomingListItem from "./TaskAllUpcomingListItem";

export default function TaskAllUpcomingList({ tasks, column, direction }) {
  const dispatch = useDispatch();

  return (
    <>
      <Table compact sortable>
        <Table.Header className="mobile hidden">
          <Table.Row className="small-header">
            <Table.HeaderCell
              sorted={column === "title" ? direction : null}
              onClick={() => dispatch(sortTasksUpcoming("title"))}
            >
              Title
            </Table.HeaderCell>
            {/* <Table.HeaderCell
              sorted={column === "assignedTo" ? direction : null}
              onClick={() => dispatch(sortTasksUpcoming("assignedTo"))}
            >
              Assigned To
            </Table.HeaderCell> */}
            <Table.HeaderCell
              sorted={column === "transactionTitle" ? direction : null}
              onClick={() => dispatch(sortTasksUpcoming("transactionTitle"))}
            >
              Transaction
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "status" ? direction : null}
              onClick={() => dispatch(sortTasksUpcoming("status"))}
            >
              Status
            </Table.HeaderCell>
            <Table.HeaderCell
              sorted={column === "end" ? direction : null}
              onClick={() => dispatch(sortTasksUpcoming("end"))}
            >
              Due
            </Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {tasks.length !== 0 &&
            tasks.map((task) => (
              <TaskAllUpcomingListItem task={task} key={task.id} />
            ))}
        </Table.Body>
      </Table>
    </>
  );
}
