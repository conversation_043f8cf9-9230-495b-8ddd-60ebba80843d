// /* global google */
// import React from "react";
// import { Segment, Icon } from "semantic-ui-react";
// import GoogleMapReact from "google-map-react";

// function Marker() {
//   return <Icon name="marker" size="big" color="red" />;
// }

// function apiIsLoaded(map) {
//   const panorama = new google.maps.StreetViewPanorama(
//     document.getElementById("pano"),
//     {
//       position: { lat: 34.184524, lng: -118.838467 },
//       pov: {
//         heading: 134,
//         pitch: 10,
//       },
//     }
//   );

//   map.setStreetView(panorama);
// }

// export default function TransactionPropertyMap({ latLng }) {
//   const zoom = 17;
//   return (
//     <Segment attached="bottom" style={{ padding: 0 }}>
//       <div id="pano" style={{ height: 240, width: "100%" }}>
//         <GoogleMapReact
//           //   ref={inputRef}
//           bootstrapURLKeys={{ key: process.env.REACT_APP_MAPS_KEY }}
//           center={latLng}
//           zoom={zoom}
//           onGoogleApiLoaded={({ map }) => apiIsLoaded(map)}
//         >
//           <Marker lat={latLng.lat} lng={latLng.lng} />
//         </GoogleMapReact>
//       </div>
//     </Segment>
//   );
// }
