import { Formik, Form } from "formik";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { addTransClientInDb } from "../../../app/firestore/firestoreService";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import MySelectInput from "../../../app/common/form/MySelectInput";
import {
  createClientRoleOptions,
  createRestrictedEmails,
  partyIsSeller,
} from "../../../app/common/util/util";
import FormAddress from "../../../app/common/form/FormAddress";
import FormPeopleAutofill from "../../../app/common/form/FormPeopleAutofill";
import MyCheckbox from "../../../app/common/form/MyCheckbox";
import MyTextInput from "../../../app/common/form/MyTextInput";
import { useMediaQuery } from "react-responsive";
import { Popup, Icon } from "semantic-ui-react";
import { useFormikContext } from "formik";
import { useEffect } from "react";

// Helper function to create signer name format
const createSignerName = (firstName, middleName, lastName, authorityTitle) => {
  if (!firstName && !lastName) return "";

  let name = "";
  if (firstName) name += firstName;
  if (middleName) name += (name ? " " : "") + middleName;
  if (lastName) name += (name ? " " : "") + lastName;
  if (authorityTitle) name += ", " + authorityTitle;

  return name;
};

export default function TransactionClientAddForm({
  client,
  transaction,
  people,
}) {
  const dispatch = useDispatch();
  const { allParties } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  const clientRoleOptions = createClientRoleOptions(client, transaction);

  const restrictedEmails = createRestrictedEmails(
    currentUserProfile,
    transaction,
    allParties
  );

  let initialValues = {
    type: "Client",
    role: "",
    firstName: "",
    middleName: "",
    lastName: "",
    email: "",
    phone: "",
    address: {
      street: "",
      unit: "",
      city: "",
      state: "",
      zipcode: "",
    },
    isTrust: false,
    entityName: "",
    authorityTitle: "",
    signerName: "",
    personSelected: "",
    userId: transaction.userId,
    hasPoa: false,
    poaFullName: "",
    poaTitle: "",
    poaDisplayOption: "client_by_poa",
    poaCustomDisplay: "",
  };

  const [isTrustField, setIsTrustField] = useState(
    initialValues.isTrust ? true : false
  );
  const [hasPoaField, setHasPoaField] = useState(
    initialValues.hasPoa ? true : false
  );
  const [selectedRole, setSelectedRole] = useState(initialValues.role);

  // Watcher component for role changes
  function RoleWatcher({ role }) {
    React.useEffect(() => {
      setSelectedRole(role);
    }, [role]);
    return null;
  }

  // Watcher component for signer name updates
  function SignerNameWatcher() {
    const { values, setFieldValue } = useFormikContext();

    useEffect(() => {
      if (values.isTrust) {
        const signerName = createSignerName(
          values.firstName,
          values.middleName,
          values.lastName,
          values.authorityTitle || ""
        );
        setFieldValue("signerName", signerName);
      } else {
        setFieldValue("signerName", "");
      }
    }, [
      values.firstName,
      values.middleName,
      values.lastName,
      values.authorityTitle,
      values.isTrust,
      setFieldValue,
    ]);

    return null;
  }

  const validationSchema = Yup.object({
    lastName: Yup.string().required("You must provide a last name"),
    email: Yup.string()
      .email("Must be a valid email")
      .notOneOf(restrictedEmails, "Can't use same email for different parties"),
    role: Yup.string().required("You must provide a role"),
    poaFullName: Yup.string().when("hasPoa", {
      is: true,
      then: (schema) => schema.required("POA full name is required"),
    }),
    poaTitle: Yup.string().when("hasPoa", {
      is: true,
      then: (schema) => schema.required("POA title is required"),
    }),
    poaCustomDisplay: Yup.string().when(["hasPoa", "poaDisplayOption"], {
      is: (hasPoa, poaDisplayOption) => hasPoa && poaDisplayOption === "custom",
      then: (schema) => schema.required("Custom display text is required"),
    }),
  });

  const poaDisplayOptions = [
    {
      key: "client_by_poa",
      value: "client_by_poa",
      text: "Client's name, by POA's name, as Attorney-in-Fact",
    },
    {
      key: "poa_for_client",
      value: "poa_for_client",
      text: "POA's name, as Attorney-in-Fact for Client's name",
    },
    {
      key: "custom",
      value: "custom",
      text: "Custom display (enter below)",
    },
  ];

  return (
    <ModalWrapper>
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            validateOnChange={false}
            validateOnBlur={false}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                await addTransClientInDb(transaction, values);
                setSubmitting(false);
                toast.success("Client successfully updated");
                dispatch(
                  closeModal({
                    modalType: "TransactionClientAddForm",
                  })
                );
              } catch (error) {
                toast.error(error.message);
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, dirty, isValid, values }) => (
              <Form className="ui form">
                <RoleWatcher role={values.role} />
                <SignerNameWatcher />
                <Header size="large" color="blue">
                  Add Client
                </Header>
                <Divider />
                <Grid>
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="role"
                      placeholder="Role"
                      options={clientRoleOptions}
                    />
                  </Grid.Column>
                </Grid>
                <Divider />
                <FormPeopleAutofill
                  people={people}
                  type={"Client"}
                  setIsTrustField={setIsTrustField}
                />
                <br />
                <MyCheckbox
                  name="isTrust"
                  label="This client is a company, trust, or other entity"
                  onClick={() => setIsTrustField(!isTrustField)}
                />
                {isTrustField && (
                  <>
                    <MyTextInput
                      name="entityName"
                      label="Company, trust, or entity name"
                    />
                    <MyTextInput
                      name="authorityTitle"
                      label="Authority title (e.g., Manager, Trustee, President, Member)"
                      placeholder="Manager"
                    />
                    <MyTextInput
                      name="signerName"
                      label="Signer name (auto-generated)"
                      readOnly
                      style={{ backgroundColor: "#f8f8f8" }}
                    />
                  </>
                )}

                {/* POA Section - Only show for Seller roles */}
                {partyIsSeller(selectedRole) && (
                  <>
                    <br />
                    <MyCheckbox
                      name="hasPoa"
                      label="This client will be represented by a Power of Attorney (POA)"
                      onClick={() => setHasPoaField(!hasPoaField)}
                    />
                    {hasPoaField && (
                      <>
                        <Grid>
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={8}>
                              <MyTextInput
                                name="poaFullName"
                                label="POA's Full Name"
                                placeholder="Enter POA's full name"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={8}>
                              <MyTextInput
                                name="poaTitle"
                                label="POA's Title"
                                placeholder="Enter POA's title"
                              />
                            </Grid.Column>
                          </Grid.Row>
                          <Grid.Row>
                            <Grid.Column width={16}>
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  marginBottom: "8px",
                                }}
                              >
                                <label style={{ marginRight: "8px" }}>
                                  Signature Display Option
                                </label>
                                <Popup
                                  trigger={
                                    <Icon name="info circle" color="blue" />
                                  }
                                  content={
                                    <div>
                                      <p>
                                        <strong>
                                          Examples of how signatures will
                                          display:
                                        </strong>
                                      </p>
                                      <p>
                                        <strong>Option A:</strong> John Q.
                                        Principal, by Jane R. Agent, as
                                        Attorney-in-Fact
                                      </p>
                                      <p>
                                        <strong>Option B:</strong> Jane R.
                                        Agent, as Attorney-in-Fact for John Q.
                                        Principal
                                      </p>
                                      <p>
                                        <strong>Custom:</strong> You can enter
                                        your own display format
                                      </p>
                                      <br />
                                      <p>
                                        <strong>Note:</strong> The email for
                                        this client should be the POA's email
                                        address.
                                      </p>
                                      <p>
                                        <strong>
                                          Initials will display as:
                                        </strong>{" "}
                                        Client's initials by POA's initials
                                        (AIF)
                                      </p>
                                      <p>
                                        <strong>Example:</strong> JQP by JRA
                                        (AIF)
                                      </p>
                                    </div>
                                  }
                                  wide="very"
                                  position="top center"
                                />
                              </div>
                              <MySelectInput
                                name="poaDisplayOption"
                                options={poaDisplayOptions}
                                placeholder="Select display option"
                              />
                            </Grid.Column>
                          </Grid.Row>
                          {values.poaDisplayOption === "custom" && (
                            <Grid.Row>
                              <Grid.Column width={16}>
                                <MyTextInput
                                  name="poaCustomDisplay"
                                  label="Custom Signature Display"
                                  placeholder="Enter how you want the signature to display"
                                />
                              </Grid.Column>
                            </Grid.Row>
                          )}
                        </Grid>
                      </>
                    )}
                  </>
                )}

                <Divider />
                <FormAddress />
                <Divider className="medium top margin" />
                <Button
                  loading={isSubmitting}
                  disabled={!dirty || isSubmitting}
                  type="submit"
                  floated={isMobile ? null : "right"}
                  primary
                  content="Submit"
                  className={isMobile ? "fluid medium bottom margin" : null}
                />
                <Button
                  disabled={isSubmitting}
                  onClick={() =>
                    dispatch(
                      closeModal({
                        modalType: "TransactionClientAddForm",
                      })
                    )
                  }
                  to="#"
                  type="button"
                  floated={isMobile ? null : "right"}
                  content="Cancel"
                  className={isMobile ? "fluid medium" : null}
                />
              </Form>
            )}
          </Formik>
        </div>
      </Segment>
    </ModalWrapper>
  );
}
