import React, { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import { Button, Popup, Icon, Grid } from "semantic-ui-react";
import MyTextInput from "../../../app/common/form/MyTextInput";
import { functionFetchOwnerData } from "../../../app/firestore/functionsService";

export default function TransactionImportOwnersForm({ setOwnerData, address }) {
  const [importOwnerStatus, setImportOwnerStatus] = useState("Initial");
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  async function handleOwnerImport() {
    if (
      address &&
      address.street &&
      address.city &&
      address.state &&
      address.zipcode
    ) {
      setImportOwnerStatus("Loading");
      try {
        const returnedOwnerData = await functionFetchOwnerData(address);
        setOwnerData(returnedOwnerData);
        setImportOwnerStatus("Complete");
        toast.success("Owner imported successfully");
      } catch (error) {
        setImportOwnerStatus("Error");
        toast.error(
          `Error when importing owner data. Please check the address and try again or manually enter the owner details.`
        );
      }
    } else {
      toast.error("Please ensure the full address is entered.");
    }
  }

  return (
    <>
      <Grid>
        <Grid.Row className="small vertical padding">
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput name="owner.firstName" label="Owner First Name" />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput name="owner.middleName" label="Owner Middle Name" />
          </Grid.Column>

          <Grid.Column mobile={16} computer={4}>
            <MyTextInput name="owner.lastName" label="Owner Last Name" />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput
              name="owner.entityName"
              label="Owner Entity Name (Trust or LLC)"
            />
          </Grid.Column>
        </Grid.Row>
        <Grid.Row className="mini top padding small bottom padding">
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput
              name="owner2.firstName"
              label="Second Owner First Name"
            />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput
              name="owner2.middleName"
              label="Second Owner Middle Name"
            />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput
              name="owner2.lastName"
              label="Second Owner Last Name"
            />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4}>
            <MyTextInput name="owner2.entityName" label="2nd Entity Name" />
          </Grid.Column>
        </Grid.Row>
        <Grid.Row>
          <Grid.Column width={16}>
            <Button
              primary
              loading={importOwnerStatus === "Loading"}
              disabled={importOwnerStatus === "Loading"}
              className={isMobile ? "fluid" : null}
              onClick={(e) => {
                e.preventDefault();
                handleOwnerImport();
              }}
            >
              {importOwnerStatus === "Initial" || importOwnerStatus === "Error"
                ? "Import Owner Details"
                : `Import ${importOwnerStatus}`}
            </Button>
            <Popup
              flowing
              size="large"
              trigger={
                <Icon
                  name="info"
                  color="blue"
                  circular
                  inverted
                  size="small"
                  style={{ marginLeft: "3px", marginBottom: "3px" }}
                />
              }
            >
              <p className="bold text blue mini bottom margin">
                Owner Details Import
              </p>
              <p className="text-dark text-normal mini bottom margin">
                You can import the owner details from public records by entering
                the full address to enable the import button.
              </p>
              <p className="text-dark text-normal mini bottom margin">
                Be sure to verify the seller names and/or trust or corporation.
              </p>
            </Popup>
          </Grid.Column>
        </Grid.Row>
      </Grid>
    </>
  );
}
