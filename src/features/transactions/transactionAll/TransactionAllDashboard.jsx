import React, { useEffect } from "react";
import { Button, Grid, Input } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { Link, useParams } from "react-router-dom";
import TransactionAllList from "./TransactionAllList";
import { convertSearchTerms } from "../../../app/common/util/util";
import { fetchTransAll, resetTransAll } from "../transactionSlice";
import { getDocs } from "firebase/firestore";
import {
  dataFromSnapshot,
  fetchTransactionsAllFromDb,
} from "../../../app/firestore/firestoreService";
import { useMediaQuery } from "react-responsive";
import { openModal } from "../../../app/common/modals/modalSlice";

export default function TransactionAllDashboard() {
  const limit = 50;
  const dispatch = useDispatch();
  const { transAll } = useSelector((state) => state.transaction);
  const { currentUserProfile, agentUserProfile } = useSelector(
    (state) => state.profile
  );
  let { agentId } = useParams();
  const [searchTermsRaw, setSearchTermsRaw] = useState("");
  const [searchTerms, setSearchTerms] = useState("");
  const isMobile = useMediaQuery({ query: "(max-width:850px)" });

  // Reset transAll state when component mounts to avoid stale data from other dashboards
  useEffect(() => {
    dispatch(resetTransAll());
  }, [dispatch]);

  useEffect(() => {
    async function fetchData(limit) {
      return await getDocs(
        fetchTransactionsAllFromDb(
          limit,
          "initial",
          null, // Always start fresh for all transactions
          searchTerms,
          agentId
        )
      );
    }
    fetchData(limit).then((snapshot) => {
      let firstTransaction = snapshot.docs[0];
      const firstVisible = snapshot.docs[0];
      const lastVisible = snapshot.docs[snapshot.docs.length - 1];
      const moreTransactions = snapshot.docs.length >= limit;
      const transactions = snapshot.docs.map((doc) => dataFromSnapshot(doc));
      dispatch(
        fetchTransAll({
          transactions,
          moreTransactions,
          firstTransaction,
          firstVisible,
          lastVisible,
        })
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerms, dispatch]);

  async function handlePageClick(direction) {
    let snapshot = null;
    if (direction === "next") {
      snapshot = await getDocs(
        fetchTransactionsAllFromDb(
          limit,
          "next",
          transAll.lastVisible,
          searchTerms,
          agentId
        )
      );
    } else {
      snapshot = await getDocs(
        fetchTransactionsAllFromDb(
          limit,
          "previous",
          transAll.firstVisible,
          searchTerms,
          agentId
        )
      );
    }
    const firstVisible = snapshot.docs[0];
    const lastVisible = snapshot.docs[snapshot.docs.length - 1];
    const moreTransactions = snapshot.docs.length >= limit;
    const transactions = snapshot.docs.map((doc) => dataFromSnapshot(doc));
    dispatch(
      fetchTransAll({
        transactions,
        moreTransactions,
        firstVisible,
        lastVisible,
      })
    );
  }

  async function handleSearch(eventKey) {
    if (eventKey === "Enter") {
      const searchTermsConverted = convertSearchTerms(searchTermsRaw);
      setSearchTerms(searchTermsConverted);
    }
  }

  return (
    <div className="main-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={5} tablet={16}>
          <Input
            action={{
              icon: "search",
              onClick: () => handleSearch("Enter"),
            }}
            onKeyDown={(event) => {
              handleSearch(event.key);
            }}
            type="text"
            fluid
            size="small"
            placeholder="Search by client name or address"
            value={searchTermsRaw}
            onChange={(e) => setSearchTermsRaw(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={5} tablet={8}>
          <Button.Group fluid size="small">
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactions`
                  : "/transactions"
              }
            >
              Active
            </Button>
            <Button active as={Link} to="">
              All
            </Button>
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactionsComplete`
                  : "/transactionsComplete"
              }
            >
              Complete
            </Button>
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactionsArchived`
                  : "/transactionsArchived"
              }
            >
              Archived
            </Button>
          </Button.Group>
        </Grid.Column>
        <Grid.Column computer={4} tablet={4}>
          {(currentUserProfile.role === "agent" ||
            currentUserProfile.role === "assistant" ||
            (currentUserProfile.role === "manager" && agentUserProfile) ||
            (currentUserProfile.role === "managerassistant" &&
              agentUserProfile)) && (
            <Button
              color="blue"
              to="#"
              floated={isMobile ? null : "right"}
              icon="plus"
              size="small"
              className={isMobile ? "fluid" : null}
              onClick={() =>
                dispatch(
                  openModal({
                    modalType: "TransactionForm",
                  })
                )
              }
              content="New Transaction"
            />
          )}
        </Grid.Column>
        <Grid.Column computer={16}>
          <h3>All Transactions</h3>
          {transAll?.transactions?.length !== 0 ? (
            <TransactionAllList transactions={transAll.transactions} />
          ) : (
            <p>There are no transactions</p>
          )}

          {transAll.transactions[0] &&
            (!transAll?.firstTransaction ||
              transAll.transactions[0].id !== transAll.firstTransaction.id) && (
              <Button primary onClick={() => handlePageClick("previous")}>
                Previous
              </Button>
            )}
          <div style={{ float: "right" }}>
            {transAll?.moreTransactions && (
              <Button primary onClick={() => handlePageClick("next")}>
                Next
              </Button>
            )}
          </div>
        </Grid.Column>
      </Grid>
    </div>
  );
}
