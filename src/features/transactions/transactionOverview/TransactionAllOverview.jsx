import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON>ton, Grid, Message, MessageHeader, Image } from "semantic-ui-react";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import {
  filterRecentTrans,
  filterUpcomingDocs,
} from "../../../app/common/util/util";
import { openModal } from "../../../app/common/modals/modalSlice";
import TransactionCardList from "../transactionComponents/TransactionCardList";
import { useMediaQuery } from "react-responsive";
import DocAllAwaitingSignatureList from "../../docs/docAll/DocAllAwaitingSignatureList";
import { TaskAllCalendarWeekly } from "../../tasks/taskAll/TaskAllCalendarWeekly";

export default function TransactionAllOverview() {
  const dispatch = useDispatch();
  const { currentUserProfile, agentUserProfile } = useSelector(
    (state) => state.profile
  );
  const { transActive } = useSelector((state) => state.transaction);
  const { docsUpcoming } = useSelector((state) => state.doc);
  const { tasksUpcoming } = useSelector((state) => state.task);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  if (!transActive || !docsUpcoming || !tasksUpcoming?.tasks)
    return <LoadingComponent content="Loading details..." />;

  const transActiveFiltered = filterRecentTrans(transActive);
  const docsUpcomingFiltered = filterUpcomingDocs(docsUpcoming);

  return (
    <>
      {currentUserProfile?.isTester === true && (
        <div className="text-center">
          <Message color="blue" className="zero margin">
            <MessageHeader>
              This is a test account. No emails will be sent out when sharing or
              sending documents for signing.
            </MessageHeader>
          </Message>
        </div>
      )}

      {!currentUserProfile?.isTester &&
        currentUserProfile?.type === "user" &&
        currentUserProfile?.payments &&
        new Date(
          Math.max(
            ...currentUserProfile?.payments.map(
              (item) => new Date(item.dateExpires.seconds * 1000)
            )
          )
        ) < new Date() && (
          <div className="text-center">
            <Message color="violet" className="zero margin">
              <MessageHeader>
                <center>
                  <Image
                    src="/assets/logo-original-with-text-75H.png"
                    style={{ align: "center" }}
                  />
                </center>
                <br />
                Thank you for using TransActioner!
                <br />
                <h2>Your account has expired.</h2>
                <br />  <br />
                Please pay either a monthly subscription or an annual
                payment to continue using TransActioner.
                <br /> &nbsp; <br />
              </MessageHeader>

                {currentUserProfile?.paymentsDiscount === "VIPx5" && (
          <>
          <h2>VIP Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As a VIP, you have the option to set up recurring annual payments for a VIP discount.
                <br/>
                You can cancel your recurring payments at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/9B628q4WG26geqe6cB5kk0i"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay $195 for 1 year and sets up automatic recurring payments on an annual basis - guaranteed price until 2030!
              </a>{" "}
            </p>
            </>
            )}
          {currentUserProfile?.paymentsDiscount === "MULTIAGENT5" && (
          <>
          <h2>VIP Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As a VIP, you have the option to set up recurring annual payments for a VIP discount.
                <br/>
                You can cancel your recurring payments at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/3cIbJ0ah04eo6XM0Sh5kk0g"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Pay $175 for 1 year and sets up automatic recurring payments on an annual basis.
              </a>{" "}
            </p>
            </>
            )}
          {currentUserProfile?.brokerageForms === "SHERPA" && currentUserProfile.paymentsDiscount === "SHERPA" && (
          <>
          <h2>Sherpa Brokerage Discount - Recurring Annual Plan</h2>
            
            <p color="grey">
              <i>
                As an agent with Sherpa Real Estate, you have the option to set up recurring annual payments for a discount.
                <br/>
                You can cancel your recurring payment on Stripe at any time.
                <br />
                <i>
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
            <p color="grey">
              <a
                href="https://buy.stripe.com/4gMbJ04WG26ggymcAZ5kk0k"
                rel="noopener noreferrer"
                target="_blank"
              >
                Link to Subscribe for $165 per year and sets up automatic recurring payments on an annual basis.
              </a>{" "}
            </p>
            </>
            )}
            <h2>One-Time Payment Annual Plan</h2>

            <p color="grey">
              <i>
                For the regular annual plan, you can pay a one-time amount and then will need to pay again each year.
                <br />
                There is no price guarantee.
                <i> 
                  Renewal payments will automatically add 12 months onto your
                  expiration date.
                </i>
              </i>
            </p>
                <a
                  href="https://buy.stripe.com/7sI3cB7ZUg3L6TC4gh"
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  Click <u>here</u> to Pay $199 for 1 year.
                </a>
                <br /> &nbsp; <br />
                            <h2>Monthly Payment Subscription</h2>

                <a
                  href="https://buy.stripe.com/3cs7sR2FAbNv3Hq3ce"
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  Click <u>here</u> to Subscribe for $19/month.
                </a>
                <br /> &nbsp; <br />
                <i>
                  <small>
                    Changes will be reflected within 24 hours after payment.
                  </small>
                </i>
            </Message>
          </div>
        )}
      <div className="main-page-wrapper">
        <>
          <Grid stackable className="small bottom margin">
            <Grid.Column computer={8} className="small bottom margin">
              <h3
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Active Transactions
              </h3>
            </Grid.Column>
            <Grid.Column
              computer={8}
              mobile={16}
              className="small bottom padding"
            >
              {(currentUserProfile.role === "agent" ||
                currentUserProfile.role === "assistant" ||
                (currentUserProfile.role === "manager" &&
                  agentUserProfile)) && (
                <Button
                  color="blue"
                  to="#"
                  floated={isMobile ? "left" : "right"}
                  icon="plus"
                  size="small"
                  className={isMobile ? "fluid" : null}
                  onClick={() =>
                    dispatch(
                      openModal({
                        modalType: "TransactionForm",
                      })
                    )
                  }
                  content="New Transaction"
                  data-test="newTransaction"
                />
              )}
            </Grid.Column>
            <Grid.Column computer={16}>
              {transActive.length > 0 ? (
                <TransactionCardList transactions={transActiveFiltered} />
              ) : (
                <p>There are no recent transactions.</p>
              )}
            </Grid.Column>
          </Grid>
          <Grid className="medium bottom margin">
            <Grid.Column computer={16}>
              <h3 style={{ marginBottom: "36px" }}>Calendar</h3>
              <div className="small horizontal padding">
                <TaskAllCalendarWeekly />
              </div>
            </Grid.Column>
          </Grid>
          <Grid computer={16}>
            <Grid.Column>
              <h3 className="mini bottom margin">
                Documents Awaiting Signature
              </h3>
              {docsUpcomingFiltered.length > 0 ? (
                <DocAllAwaitingSignatureList docs={docsUpcomingFiltered} />
              ) : (
                <p>There are no documents awaiting signature</p>
              )}
            </Grid.Column>
          </Grid>
        </>
      </div>
    </>
  );
}
