import React, { useState } from "react";
import { Grid, Input, Button } from "semantic-ui-react";
import { useSelector } from "react-redux";
import HistoryList from "./historyComponents/HistoryList";
import { searchFilter, downloadHistoryAsPdf } from "../../app/common/util/util";
import { toast } from "react-toastify";
import { useMediaQuery } from "react-responsive";

export default function HistoryDashboard() {
  const { historyAll } = useSelector((state) => state.history);
  const { transaction } = useSelector((state) => state.transaction);
  const [searchTerms, setSearchTerms] = useState("");
  const [downloadingHistory, setDownloadingHistory] = useState(false);
  const historyFiltered = searchFilter(historyAll?.history, searchTerms);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  async function handleDownloadHistory() {
    if (!historyFiltered || historyFiltered.length === 0) {
      toast.error("No history data available to download");
      return;
    }

    setDownloadingHistory(true);
    try {
      downloadHistoryAsPdf(historyFiltered, transaction);
      toast.success("History PDF downloaded successfully!");
    } catch (error) {
      console.error("Error downloading history:", error);
      toast.error(error.message || "Failed to download history");
    } finally {
      setDownloadingHistory(false);
    }
  }

  return (
    <div className="secondary-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={8}>
          <Input
            type="text"
            fluid
            placeholder="Search by role, name, action, or document"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={8} tablet={8}>
          <div className={isMobile ? null : "float-right"}>
            <Button
              icon="download"
              size="small"
              className={isMobile ? "fluid" : null}
              onClick={handleDownloadHistory}
              content="Download History"
              color="teal"
              loading={downloadingHistory}
              disabled={
                downloadingHistory ||
                !historyFiltered ||
                historyFiltered.length === 0
              }
            />
          </div>
        </Grid.Column>
        <Grid.Column computer={16}>
          <h3>History</h3>
          {historyAll?.history?.length > 0 ? (
            <HistoryList
              history={historyFiltered}
              column={historyAll.column}
              direction={historyAll.direction}
            />
          ) : (
            <p>There is no history for this transaction</p>
          )}
        </Grid.Column>
      </Grid>
    </div>
  );
}
