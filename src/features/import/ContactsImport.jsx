import React, { useCallback, useState } from "react";
// import { useSelector } from "react-redux";
import { 
  <PERSON><PERSON>, 
  <PERSON>rid, 
  Header, 
  Icon, 
  Message, 
  Progress,
  List,
  Segment,
  Table
} from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import LoadingComponent from "../../app/layout/LoadingComponent";

import { addPersonToDb } from "../../app/firestore/firestoreService";

const dropzoneStyles = {
  border: "dashed 3px #eee",
  borderColor: "#eee",
  borderRadius: "5px",
  paddingTop: "30px",
  paddingBottom: "30px",
  textAlign: "center",
  cursor: "pointer",
  backgroundColor: "#fafafa",
};

const dropzoneActive = {
  borderColor: "#2185d0",
  backgroundColor: "#f0f8ff",
};

// CSV column mapping to person object fields
const CSV_COLUMN_MAPPING = {
  "First Name1": "firstName",  // CTM
  "First Name": "firstName",
  "Middle Name1": "middleName", // CTM
  "Middle Name": "middleName", 
  "Last Name1": "lastName",   // CTM
  "Last Name": "lastName",
  "Email1": "email",          // CTM
  "Email": "email",
  "PhCell1": "phone",         // CTM
  "Phone": "phone",
  "Cell": "phone",
  "Company Name": "entityName",  // CTM
  "Entity Name": "entityName",  
  "Company": "companyName",  
  "Type": "type",
  "Role": "type", // Alternative column name for type
  "Street": "address.street",
  "Address": "address.street", // Alternative column name
  "Address St. Name": "address.street", // Alternative column name
  "Unit": "address.unit",
  "City": "address.city",
  "Address City": "address.city", // Alternative column name
  "State": "address.state",
  "Address State": "address.state", // Alternative column name
  "Zipcode": "address.zipcode",
  "Zip": "address.zipcode", // Alternative column name
  "Address Zipcode": "address.zipcode", // Alternative column name
  "Address Zip": "address.zipcode", // Alternative column name
  "License Number": "brokerLicenseNumber",
  "Broker License Number": "brokerLicenseNumber",
  "Agent License Number": "brokerLicenseNumber",
  "Brokerage": "brokerageName",
  "Brokerage Name": "brokerageName",
  "Brokerage License Number": "brokerageLicenseNumber",
  "Assistant First Name": "assistant.firstName",
  "Assistant Last Name": "assistant.lastName",
  "Assistant Email": "assistant.email",
  "Assistant Phone": "assistant.phone",
  "Authority Title": "authorityTitle",
  // "Signer Name": "signerName",
};

export default function ContactsImport() {

  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState(null);
  const [progress, setProgress] = useState(0);
  const [previewData, setPreviewData] = useState(null);
  const [parsedData, setParsedData] = useState(null);
  const [importResults, setImportResults] = useState([]);

  const onDrop = useCallback(async (acceptedFiles) => {
    if (!acceptedFiles || acceptedFiles.length === 0) {
      return;
    }

    const file = acceptedFiles[0]; // Only process first file

    if (!isValidCsvFile(file)) {
      setError("Please upload a CSV file (.csv extension)");
      return;
    }

    setLoading(true);
    setError(null);
    setMessage(null);
    setProgress(0);
    setPreviewData(null);
    setParsedData(null);
    setImportResults([]);

    try {
      // Parse CSV file
      setProgress(50);
      const csvData = await parseCsvFile(file);

      // Show preview of first 5 rows
      const preview = csvData
        .slice(0, 5)
        .map((row) => transformCsvRowToPerson(row));
      setPreviewData({
        headers: Object.keys(csvData[0] || {}),
        preview: preview,
        totalRows: csvData.length,
      });
      setParsedData(csvData);
      setProgress(100);
    } catch (error) {
      console.error("Error parsing CSV:", error);
      setError(error.message);
      toast.error("Failed to parse CSV file");
    } finally {
      setLoading(false);
    }
  }, []);

  const handleImportConfirm = useCallback(async () => {
    if (!parsedData) return;

    setImporting(true);
    setError(null);
    setMessage(null);
    setProgress(0);
    setImportResults([]);

    try {
      const results = [];
      const batchSize = 10; // Process in batches for better performance

      for (let i = 0; i < parsedData.length; i += batchSize) {
        const batch = parsedData.slice(i, i + batchSize);

        // Process batch in parallel
        const batchPromises = batch.map(async (row, batchIndex) => {
          const actualIndex = i + batchIndex;
          try {
            const personData = transformCsvRowToPerson(row);

            // Validate required fields
            if (!personData.lastName) {
              return {
                row: actualIndex + 1,
                success: false,
                error: "Missing last name",
                name: `Row ${actualIndex + 1}`,
              };
            }

            await addPersonToDb(personData);

            return {
              row: actualIndex + 1,
              success: true,
              error: null,
              name:
                `${personData.firstName} ${personData.lastName}`.trim() ||
                `Row ${actualIndex + 1}`,
            };
          } catch (error) {
            console.error(`Error importing row ${actualIndex + 1}:`, error);
            return {
              row: actualIndex + 1,
              success: false,
              error: error.message,
              name: `Row ${actualIndex + 1}`,
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Update progress
        setProgress(Math.floor(((i + batch.length) / parsedData.length) * 100));
      }

      setImportResults(results);

      const successCount = results.filter((r) => r.success).length;
      const errorCount = results.filter((r) => !r.success).length;

      if (successCount > 0) {
        setMessage(
          `Successfully imported ${successCount} contact${
            successCount > 1 ? "s" : ""
          }`
        );
        toast.success(
          `Successfully imported ${successCount} contact${
            successCount > 1 ? "s" : ""
          }`
        );
      }

      if (errorCount > 0) {
        toast.warning(
          `${errorCount} contact${
            errorCount > 1 ? "s" : ""
          } could not be imported`
        );
      }
    } catch (error) {
      console.error("Error importing contacts:", error);
      setError(error.message);
      toast.error("Failed to import contacts");
    } finally {
      setImporting(false);
    }
  }, [parsedData]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "text/csv": [".csv"],
      "application/vnd.ms-excel": [".csv"],
    },
    multiple: false,
  });

  const downloadSampleCsv = () => {
    const sampleData = [
      [
        "First Name",
        "Middle Name",
        "Last Name",
        "Email",
        "Phone",
        "Entity Name",
        "Authority Title",
        "Company",
        "Street",
        "City",
        "State",
        "Zipcode",
        "Type",
        "Broker License Number",
        "Brokerage Name",
        "Brokerage License Number",
        "Assistant First Name",
        "Assistant Last Name",
        "Assistant Email",
        "Assistant Phone",
      ],
      [
        "John",
        "Michael",
        "Doe",
        "<EMAIL>",
        "************",
        "Doe Family Trust",
        "Trustee",
        "",
        "123 Main St",
        "Denver",
        "CO",
        "80202",
        "Client",
        "",
        "",
        "",
      ],
      [
        "Jane",
        "E",
        "Smith",
        "<EMAIL>",
        "************",
        "",
        "",
        "",
        "456 Oak Ave",
        "Boulder",
        "CO",
        "80301",
        "Agent",
        "FA123456",
        "ABC Brokerage Realty",
        "EC123456",
      ],
      [
        "Terri",
        "",
        "Johnson",
        "<EMAIL>",
        "************",
        "",
        "",
        "Test Title Company",
        "456 Oak Ave",
        "Boulder",
        "CO",
        "80301",
        "Title",
        "",
        "",
        "",
      ],
      [
        "Lenny",
        "",
        "Lerron",
        "<EMAIL>",
        "************",
        "",
        "",
        "Lending Company",
        "456 Oak Ave",
        "Boulder",
        "CO",
        "80301",
        "Lender",
        "",
        "",
        "",
      ],
      [
        "Jojo",
        "",
        "Shaw",
        "<EMAIL>",
        "************",
        "",
        "",
        "XYZ Law Firm",
        "456 Oak Ave",
        "Boulder",
        "CO",
        "80301",
        "Attorney",
        "",
        "",
        "",
      ],
    ];

    const csvContent = sampleData.map((row) => row.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "contacts_sample.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Grid>
      <Grid.Column width={16}>
        <Header as="h3" color="blue">
          Import Contacts Database
        </Header>
        <p>
          Upload a CSV file containing your contacts to populate your People
          list. The system will automatically map common column names to the
          appropriate fields.
          <br />
          <strong>Required columns:</strong> First Name, Last Name, Email.
          'Type' defaults to Client unless otherwise specified (Agent, Title,
          Lender, Attorney, TC, Escrow)
          <br />
          <i>
            Note: CTMe data is imported from the CTMe file you downloaded from
            CTMe and uploaded here.
          </i>
        </p>
        <p>See sample CSV for example column headings.</p>
        <Button
          icon="download"
          content="Download Sample CSV"
          onClick={downloadSampleCsv}
          style={{ marginBottom: "10px" }}
        />

        <div
          {...getRootProps()}
          style={
            isDragActive
              ? { ...dropzoneStyles, ...dropzoneActive }
              : dropzoneStyles
          }
        >
          <input {...getInputProps()} />
          <Icon name="file excel outline" size="huge" color="grey" />
          <Header color="grey" style={{ marginTop: "6px" }}>
            Drag and drop a CSV file here
            <br />
            or click to select file
          </Header>
          <p style={{ color: "#666", fontSize: "0.9em", marginTop: "10px" }}>
            CSV files only • Max 10MB
          </p>
        </div>

        {loading && (
          <Segment style={{ marginTop: "10px" }}>
            <div style={{ textAlign: "center", marginBottom: "20px" }}>
              <LoadingComponent />
            </div>
            {progress > 0 && (
              <Progress
                percent={progress}
                indicating
                progress
                label="Parsing CSV file..."
              />
            )}
          </Segment>
        )}

        {importing && (
          <Segment style={{ marginTop: "10px" }}>
            <div style={{ textAlign: "center", marginBottom: "20px" }}>
              <Header as="h4" color="blue">
                <Icon name="spinner" loading />
                Importing Contacts...
              </Header>
            </div>
            {progress > 0 && (
              <Progress
                percent={progress}
                indicating
                progress
                label={`Importing contacts... ${Math.round(progress)}%`}
              />
            )}
          </Segment>
        )}

        {(error || message) && (
          <Message
            negative={!!error}
            positive={!!message}
            style={{ marginTop: "10px" }}
          >
            <Message.Header>{error || message}</Message.Header>
          </Message>
        )}

        {previewData && !importing && importResults.length === 0 && (
          <Segment style={{ marginTop: "10px" }}>
            <Header as="h4">
              Import Preview ({previewData.totalRows} total contacts)
            </Header>
            <p>First 5 contacts that will be imported:</p>
            <Table celled>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Name</Table.HeaderCell>
                  <Table.HeaderCell>Email</Table.HeaderCell>
                  <Table.HeaderCell>Phone</Table.HeaderCell>
                  <Table.HeaderCell>Type</Table.HeaderCell>
                  <Table.HeaderCell>Entity Name</Table.HeaderCell>
                  <Table.HeaderCell>Company</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {previewData.preview.map((person, index) => (
                  <Table.Row key={index}>
                    <Table.Cell>
                      {person.firstName} {person.middleName} {person.lastName}
                    </Table.Cell>
                    <Table.Cell>{person.email}</Table.Cell>
                    <Table.Cell>{person.phone}</Table.Cell>
                    <Table.Cell>{person.type}</Table.Cell>
                    <Table.Cell>{person.entityName}</Table.Cell>
                    <Table.Cell>{person.companyName}</Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
            <div style={{ textAlign: "center", marginTop: "20px" }}>
              <Button
                primary
                size="large"
                onClick={handleImportConfirm}
                disabled={importing}
                icon="upload"
                content={`Import ${previewData.totalRows} Contacts`}
              />
              <Button
                basic
                size="large"
                onClick={() => {
                  setPreviewData(null);
                  setParsedData(null);
                  setError(null);
                  setMessage(null);
                }}
                style={{ marginLeft: "10px" }}
                content="Cancel"
              />
            </div>
          </Segment>
        )}

        {importResults.length > 0 && (
          <Segment style={{ marginTop: "10px" }}>
            <Header as="h4">Import Results</Header>
            <List divided>
              {importResults.slice(0, 20).map((result, index) => (
                <List.Item key={index}>
                  <List.Content>
                    <List.Header>
                      <Icon
                        name={result.success ? "check circle" : "times circle"}
                        color={result.success ? "green" : "red"}
                      />
                      {result.name}
                    </List.Header>
                    {!result.success && (
                      <List.Description style={{ color: "red" }}>
                        {result.error}
                      </List.Description>
                    )}
                  </List.Content>
                </List.Item>
              ))}
              {importResults.length > 20 && (
                <List.Item>
                  <List.Content>
                    <List.Description>
                      ... and {importResults.length - 20} more contacts
                    </List.Description>
                  </List.Content>
                </List.Item>
              )}
            </List>
          </Segment>
        )}
      </Grid.Column>
    </Grid>
  );
}

// Helper functions
function isValidCsvFile(file) {
  if (!file) return false;
  
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith('.csv')) return false;
  
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) return false;
  
  return true;
}

function parseCsvFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target.result;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          reject(new Error('CSV file must contain at least a header row and one data row'));
          return;
        }

        // Parse CSV with better handling of quoted values
        const parseCSVLine = (line) => {
          const result = [];
          let current = '';
          let inQuotes = false;

          for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              result.push(current.trim());
              current = '';
            } else {
              current += char;
            }
          }
          result.push(current.trim());
          return result;
        };

        const headers = parseCSVLine(lines[0]);
        const data = [];

        for (let i = 1; i < lines.length; i++) {
          const values = parseCSVLine(lines[i]);
          const row = {};

          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });

          data.push(row);
        }

        resolve(data);
      } catch (error) {
        reject(new Error('Failed to parse CSV file: ' + error.message));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}

function transformCsvRowToPerson(row) {
  const person = {
    type: "Client",
    firstName: "",
    middleName: "",
    lastName: "",
    email: "",
    phone: "",
    companyName: "",
    address: {
      street: "",
      unit: "",
      city: "",
      state: "",
      zipcode: "",
    },
    hasAssistant: false,
    assistant: {},
    isTrust: false,
    entityName: "",
    authorityTitle: "",
    signerName: "",
    brokerLicenseNumber: "",
    brokerageName: "",
    brokerageId: "",
    brokerageLicenseNumber: "",
  };

  // Map CSV columns to person fields
  Object.entries(CSV_COLUMN_MAPPING).forEach(([csvColumn, personField]) => {
    if (row[csvColumn]) {
      if (personField.includes('.')) {
        // Handle nested fields like address.street
        const [parent, child] = personField.split('.');
        if (!person[parent]) person[parent] = {};
        person[parent][child] = row[csvColumn];
      } else {
        person[personField] = row[csvColumn];
      }
    }
    
  });
  if (person.entityName?.length > 0) {
    person.isTrust = true;
  }
  if (person.assistant?.email?.length > 0) {
    person.hasAssistant = true;
  }

  return person;
}
