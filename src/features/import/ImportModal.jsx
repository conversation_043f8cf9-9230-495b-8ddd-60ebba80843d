import React from "react";
import {
  Divider,
  Header,
  Segment,
  Tab
} from "semantic-ui-react";
import ModalWrapper from "../../app/common/modals/modalWrapper";
import ContactsImport from "./ContactsImport";

export default function ImportModal() {
  const panes = [
    // {
    //   menuItem: {
    //     key: "agency",
    //     icon: "file pdf outline",
    //     content: "Agency Agreements"
    //   },
    //   render: () => (
    //     <Tab.Pane>
    //       <AgencyAgreementUpload />
    //     </Tab.Pane>
    //   ),
    // },
    {
      menuItem: {
        key: "contacts",
        icon: "users",
        content: "Contacts Database",
      },
      render: () => (
        <Tab.Pane>
          <ContactsImport />
        </Tab.Pane>
      ),
    },
  ];

  return (
    <ModalWrapper size="large">
      <Segment clearing>
        <Header size="huge" color="blue">
          Import Data
        </Header>
        <Divider />
        <p>
          Import your contacts database to populate your People list.
          {/* agency agreements to automatically create transactions, or import */}
        </p>
        <Tab
          panes={panes}
          defaultActiveIndex={0}
          menu={{ secondary: true, pointing: true }}
        />
      </Segment>
    </ModalWrapper>
  );
}
