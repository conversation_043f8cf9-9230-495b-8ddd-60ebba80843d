import React from "react";
import {
  Divider,
  Header,
  Segment,
} from "semantic-ui-react";
import ModalWrapper from "../../app/common/modals/modalWrapper";
import TransactionSpreadsheetUpload from "../auth/TransactionSpreadsheetUpload";

export default function BulkTransactionUploadModal() {
  return (
    <ModalWrapper size="fullscreen">
      <Segment clearing>
        <Header size="huge" color="blue">
          Bulk Transaction Upload
        </Header>
        <Divider />
        <p>
          Upload a CSV file to create multiple transactions at once. The system will validate agent emails, 
          parse transaction data, and allow you to review before creating transactions.
        </p>
        <TransactionSpreadsheetUpload />
      </Segment>
    </ModalWrapper>
  );
}
