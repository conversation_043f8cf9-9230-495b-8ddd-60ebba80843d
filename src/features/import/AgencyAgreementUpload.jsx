import React, { useCallback, useState } from "react";
import { useSelector } from "react-redux";
import { 
  <PERSON><PERSON>, 
  Grid, 
  Header, 
  Icon, 
  Message, 
  Progress,
  List,
  Segment
} from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import LoadingComponent from "../../app/layout/LoadingComponent";

import { 
  extractTextFromMultiplePdfs, 
  filterValidPdfFiles 
} from "../../app/services/pdfTextExtraction";
import { 
  createTransactionFromListingAgreement,
  createTransactionFromBuyerAgreement 
} from "../../app/services/agencyAgreementProcessor";

const dropzoneStyles = {
  border: "dashed 3px #eee",
  borderColor: "#eee",
  borderRadius: "5px",
  paddingTop: "30px",
  paddingBottom: "30px",
  textAlign: "center",
  cursor: "pointer",
  backgroundColor: "#fafafa",
};

const dropzoneActive = {
  borderColor: "#2185d0",
  backgroundColor: "#f0f8ff",
};

export default function AgencyAgreementUpload() {
  const { currentUserProfile } = useSelector((state) => state.profile);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState(null);
  const [progress, setProgress] = useState(0);
  const [processingResults, setProcessingResults] = useState([]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      if (!acceptedFiles || acceptedFiles.length === 0) {
        return;
      }

      setLoading(true);
      setError(null);
      setMessage(null);
      setProgress(0);
      setProcessingResults([]);

      try {
        // Filter valid PDF files
        const validPdfFiles = filterValidPdfFiles(acceptedFiles);
        
        if (validPdfFiles.length === 0) {
          throw new Error('No valid PDF files found. Please upload PDF files only.');
        }

        if (validPdfFiles.length !== acceptedFiles.length) {
          toast.warning(`${acceptedFiles.length - validPdfFiles.length} files were skipped (not valid PDFs)`);
        }



        // Extract text from all PDFs
        setProgress(10);
        const extractionResults = await extractTextFromMultiplePdfs(validPdfFiles);
        setProgress(30);

        // Process each extracted text to create transactions
        const results = [];
        for (let i = 0; i < extractionResults.length; i++) {
          const result = extractionResults[i];
          const file = validPdfFiles[i];
          
          try {
            if (!result.success) {
              results.push({
                filename: result.filename,
                success: false,
                error: result.error,
                transactionId: null
              });
              continue;
            }

            // Determine document type and create appropriate transaction
            let transactionResult;
            if (isListingAgreement(result.text)) {
              transactionResult = await createTransactionFromListingAgreement(
                result.text, 
                file, 
                currentUserProfile
              );
            } else if (isBuyerAgreement(result.text)) {
              transactionResult = await createTransactionFromBuyerAgreement(
                result.text, 
                file, 
                currentUserProfile
              );
            } else {
              throw new Error('Unable to identify document type. Please ensure this is a Colorado Agency Agreement.');
            }

            results.push({
              filename: result.filename,
              success: true,
              error: null,
              transactionId: transactionResult.transactionId,
              transactionType: transactionResult.type
            });

          } catch (error) {
            console.error(`Error processing ${result.filename}:`, error);
            results.push({
              filename: result.filename,
              success: false,
              error: error.message,
              transactionId: null
            });
          }

          // Update progress
          setProgress(30 + ((i + 1) / extractionResults.length) * 60);
        }

        setProcessingResults(results);
        setProgress(100);

        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
          setMessage(`Successfully created ${successCount} transaction${successCount > 1 ? 's' : ''}`);
          if (errorCount === 0) {
            toast.success(`Successfully imported ${successCount} agency agreement${successCount > 1 ? 's' : ''}`);
          }
        }

        if (errorCount > 0) {
          toast.error(`Failed to process ${errorCount} file${errorCount > 1 ? 's' : ''}`);
        }

      } catch (error) {
        console.error('Error processing agency agreements:', error);
        setError(error.message);
        toast.error('Failed to process agency agreements');
      } finally {
        setLoading(false);
      }
    },
    [currentUserProfile]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: true
  });

  // Helper functions to identify document types
  function isListingAgreement(text) {
    return text.toLowerCase().includes('exclusive right to sell') ||
           text.toLowerCase().includes('listing contract') ||
           text.toLowerCase().includes('seller:');
  }

  function isBuyerAgreement(text) {
    return text.toLowerCase().includes('exclusive right to buy') ||
           text.toLowerCase().includes('buyer agency') ||
           text.toLowerCase().includes('buyer:');
  }

  return (
    <Grid>
      <Grid.Column width={16}>
        <Header as="h3" color="blue">
          Upload Colorado Agency Agreements
        </Header>
        <p>
          Upload Colorado Exclusive Right to Sell Listing agreements or Buyer Agency agreements. 
          Each document will be processed to extract client information and automatically create a new transaction.
        </p>
        
        <div
          {...getRootProps()}
          style={
            isDragActive
              ? { ...dropzoneStyles, ...dropzoneActive }
              : dropzoneStyles
          }
        >
          <input {...getInputProps()} />
          <Icon name="file pdf outline" size="huge" color="grey" />
          <Header color="grey" style={{ marginTop: "6px" }}>
            Drag and drop Colorado Agency Agreement PDFs here
            <br />
            or click to select files
          </Header>
          <p style={{ color: "#666", fontSize: "0.9em", marginTop: "10px" }}>
            Supports multiple file upload • PDF files only • Max 50MB per file
          </p>
        </div>

        {loading && (
          <Segment>
            <LoadingComponent />
            {progress > 0 && (
              <Progress 
                percent={progress} 
                indicating 
                progress
                label="Processing agency agreements..."
              />
            )}
          </Segment>
        )}

        {(error || message) && (
          <Message
            negative={!!error}
            positive={!!message}
            style={{ marginTop: "10px" }}
          >
            <Message.Header>{error || message}</Message.Header>
          </Message>
        )}

        {processingResults.length > 0 && (
          <Segment style={{ marginTop: "10px" }}>
            <Header as="h4">Processing Results</Header>
            <List divided>
              {processingResults.map((result, index) => (
                <List.Item key={index}>
                  <List.Content>
                    <List.Header>
                      <Icon 
                        name={result.success ? "check circle" : "times circle"} 
                        color={result.success ? "green" : "red"} 
                      />
                      {result.filename}
                    </List.Header>
                    <List.Description>
                      {result.success ? (
                        <>
                          Successfully created {result.transactionType} transaction
                          {result.transactionId && (
                            <Button 
                              as="a" 
                              href={`/transactions/${result.transactionId}/documents`}
                              size="mini" 
                              primary 
                              style={{ marginLeft: "10px" }}
                            >
                              View Transaction
                            </Button>
                          )}
                        </>
                      ) : (
                        <span style={{ color: "red" }}>{result.error}</span>
                      )}
                    </List.Description>
                  </List.Content>
                </List.Item>
              ))}
            </List>
          </Segment>
        )}
      </Grid.Column>
    </Grid>
  );
}
