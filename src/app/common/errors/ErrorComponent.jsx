import React from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";

export default function ErrorComponent() {
  const { error } = useSelector((state) => state.async);

  return (
    <Segment placeholder>
      <Header
        textAlign="center"
        content={error?.message || "Opps - we have an error"}
      />
      <Button
        as={Link}
        to="/events"
        primary
        style={{ marginTop: 20 }}
        content="Return to events page"
      />
    </Segment>
  );
}
