// import { format } from "date-fns";

// Timezone-safe date formatting utilities
// These functions ensure dates are formatted consistently regardless of user's timezone

/**
* Format a date consistently by using UTC date components to avoid timezone shifts
* @param {Date|Timestamp} date - The date to format (can be JS Date or Firestore Timestamp)
* @param {string} formatString - The format string (default: "MM/dd/yyyy")
* @returns {string} - Formatted date string using UTC components
*/
export function formatDateUTC(date, formatString = "MM/dd/yyyy") {
if (!date) return "";
// Handle Firestore Timestamp
if (date.toDate && typeof date.toDate === 'function') {
date = date.toDate();
}
// Handle Date object or timestamp
if (!(date instanceof Date)) {
date = new Date(date);
}
// Create a new date using UTC components to avoid timezone shifts
const utcDate = new Date(
date.getUTCFullYear(),
date.getUTCMonth(),
date.getUTCDate(),
date.getUTCHours(),
date.getUTCMinutes(),
date.getUTCSeconds(),
date.getUTCMilliseconds()
);
// Format using the UTC-based date
// console.log("    getUTCDate: ", date.getUTCDate(), "  date.getDate(): ", date.getDate());
// console.log("    getUTCDate: ", utcDate.getUTCDate(), "  utcDate.getDate(): ", utcDate.getDate());

// return (date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear();
return (utcDate.getMonth() + 1) + "/" + utcDate.getDate() + "/" + utcDate.getFullYear();

// return format(utcDate, formatString);
}

/**
* Format a date for form fields - ensures consistent display regardless of timezone
* @param {Date|Timestamp} date - The date to format
* @returns {string} - Formatted date string (M/d/yyyy format)
*/
export function formatFormFieldDate(date) {
return formatDateUTC(date, "M/d/yyyy");
}

/**
* Format a date for annotations - ensures consistent display regardless of timezone
* @param {Date|Timestamp} date - The date to format
* @returns {string} - Formatted date string (MM/dd/yyyy format)
*/
export function formatAnnotationDate(date) {
return formatDateUTC(date, "MM/dd/yyyy");
}

/**
* Get current date formatted consistently in UTC
* @param {string} formatString - The format string (default: "MM/dd/yyyy")
* @returns {string} - Current date formatted in UTC
*/
export function getCurrentDateUTC(formatString = "MM/dd/yyyy") {
return formatDateUTC(new Date(), formatString);
}