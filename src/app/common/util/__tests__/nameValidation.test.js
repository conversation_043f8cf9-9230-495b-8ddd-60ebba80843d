// Test the validation functions directly without importing the full util file
const isValidName = (name) => {
  if (!name || typeof name !== 'string') return false;
  // Normalize multiple spaces to single spaces first
  const normalizedName = name.replace(/\s+/g, ' ');
  const validNameRegex = /^[a-zA-Z0-9_ ()-]+$/;
  return validNameRegex.test(normalizedName);
};

const sanitizeName = (name) => {
  if (!name || typeof name !== 'string') return '';
  // First replace all whitespace characters (tabs, newlines, etc.) with spaces
  let sanitized = name.replace(/\s/g, ' ');
  // Then replace any character that is not alphanumeric, underscore, parentheses, dash, or space with empty string
  sanitized = sanitized.replace(/[^a-zA-Z0-9_() -]/g, '');
  // Finally replace multiple consecutive spaces with single space
  sanitized = sanitized.replace(/\s+/g, ' ');
  return sanitized;
};

describe('Name Validation Utilities', () => {
  describe('isValidName', () => {
    test('should return true for valid names with letters only', () => {
      expect(isValidName('TestName')).toBe(true);
      expect(isValidName('test')).toBe(true);
      expect(isValidName('NAME')).toBe(true);
    });

    test('should return true for valid names with numbers', () => {
      expect(isValidName('Test123')).toBe(true);
      expect(isValidName('123Test')).toBe(true);
      expect(isValidName('123')).toBe(true);
    });

    test('should return true for valid names with underscores', () => {
      expect(isValidName('Test_Name')).toBe(true);
      expect(isValidName('_test')).toBe(true);
      expect(isValidName('test_')).toBe(true);
      expect(isValidName('_')).toBe(true);
    });

    test('should return true for valid names with parentheses', () => {
      expect(isValidName('Test(Name)')).toBe(true);
      expect(isValidName('(test)')).toBe(true);
      expect(isValidName('test(')).toBe(true);
      expect(isValidName(')test')).toBe(true);
      expect(isValidName('()')).toBe(true);
    });

    test('should return true for valid names with dashes', () => {
      expect(isValidName('Test-Name')).toBe(true);
      expect(isValidName('-test')).toBe(true);
      expect(isValidName('test-')).toBe(true);
      expect(isValidName('-')).toBe(true);
      expect(isValidName('Test-Name-123')).toBe(true);
    });

    test('should return true for valid names with spaces', () => {
      expect(isValidName('Test Name')).toBe(true);
      expect(isValidName(' test')).toBe(true);
      expect(isValidName('test ')).toBe(true);
      expect(isValidName(' ')).toBe(true);
      expect(isValidName('Test Name 123')).toBe(true);
    });

    test('should normalize multiple spaces to single spaces', () => {
      expect(isValidName('Test  Name')).toBe(true); // Multiple spaces should be normalized
      expect(isValidName('Test   Name   123')).toBe(true);
      expect(isValidName('   Test    Name   ')).toBe(true);
    });

    test('should return true for valid names with mixed characters', () => {
      expect(isValidName('Test_Name_123')).toBe(true);
      expect(isValidName('_Test123_')).toBe(true);
      expect(isValidName('123_Test_Name')).toBe(true);
      expect(isValidName('Test_(Name)_123')).toBe(true);
      expect(isValidName('(Test_123)')).toBe(true);
      expect(isValidName('Test-Name_123(Copy)')).toBe(true);
      expect(isValidName('_Test-(Name)_123')).toBe(true);
      expect(isValidName('Test Name_123(Copy)')).toBe(true);
      expect(isValidName('My Document-Template_123')).toBe(true);
    });

    test('should return false for names with special characters', () => {
      expect(isValidName('Test@Name')).toBe(false);
      expect(isValidName('Test.Name')).toBe(false);
      expect(isValidName('Test!Name')).toBe(false);
      expect(isValidName('Test#Name')).toBe(false);
      expect(isValidName('Test$Name')).toBe(false);
      expect(isValidName('Test%Name')).toBe(false);
      expect(isValidName('Test&Name')).toBe(false);
      expect(isValidName('Test*Name')).toBe(false);
      expect(isValidName('Test+Name')).toBe(false);
      expect(isValidName('Test=Name')).toBe(false);
      expect(isValidName('Test?Name')).toBe(false);
      expect(isValidName('Test/Name')).toBe(false);
      expect(isValidName('Test\\Name')).toBe(false);
      expect(isValidName('Test|Name')).toBe(false);
      expect(isValidName('Test<Name')).toBe(false);
      expect(isValidName('Test>Name')).toBe(false);
      expect(isValidName('Test,Name')).toBe(false);
      expect(isValidName('Test;Name')).toBe(false);
      expect(isValidName('Test:Name')).toBe(false);
      expect(isValidName('Test"Name')).toBe(false);
      expect(isValidName("Test'Name")).toBe(false);
      expect(isValidName('Test[Name')).toBe(false);
      expect(isValidName('Test]Name')).toBe(false);
      expect(isValidName('Test{Name')).toBe(false);
      expect(isValidName('Test}Name')).toBe(false);

    });

    test('should return false for empty or invalid inputs', () => {
      expect(isValidName('')).toBe(false);
      expect(isValidName(null)).toBe(false);
      expect(isValidName(undefined)).toBe(false);
      expect(isValidName(123)).toBe(false);
      expect(isValidName({})).toBe(false);
      expect(isValidName([])).toBe(false);
    });
  });

  describe('sanitizeName', () => {
    test('should keep valid characters', () => {
      expect(sanitizeName('TestName123_')).toBe('TestName123_');
      expect(sanitizeName('_Test_123')).toBe('_Test_123');
      expect(sanitizeName('Test(Name)123')).toBe('Test(Name)123');
      expect(sanitizeName('(Test_123)')).toBe('(Test_123)');
      expect(sanitizeName('Test-Name-123')).toBe('Test-Name-123');
      expect(sanitizeName('Test_(Name)-123')).toBe('Test_(Name)-123');
      expect(sanitizeName('Test Name 123')).toBe('Test Name 123');
      expect(sanitizeName('My Document Template')).toBe('My Document Template');
    });

    test('should remove invalid characters', () => {
      expect(sanitizeName('Test@Name#123')).toBe('TestName123');
      expect(sanitizeName('Test.Name!@#$%^&*')).toBe('TestName');
      expect(sanitizeName('Test(Name)!@#')).toBe('Test(Name)');
      expect(sanitizeName('Test-Name!@#')).toBe('Test-Name');
      expect(sanitizeName('Test Name!@#')).toBe('Test Name');
    });

    test('should normalize multiple spaces to single spaces', () => {
      expect(sanitizeName('Test  Name')).toBe('Test Name');
      expect(sanitizeName('Test   Name   123')).toBe('Test Name 123');
      expect(sanitizeName('   Test    Name   ')).toBe(' Test Name ');
      expect(sanitizeName('Test\t\nName')).toBe('Test Name'); // Tabs and newlines become single space
    });

    test('should handle empty or invalid inputs', () => {
      expect(sanitizeName('')).toBe('');
      expect(sanitizeName(null)).toBe('');
      expect(sanitizeName(undefined)).toBe('');
      expect(sanitizeName(123)).toBe('');
      expect(sanitizeName({})).toBe('');
      expect(sanitizeName([])).toBe('');
    });

    test('should handle strings with only invalid characters', () => {
      expect(sanitizeName('!@#$%^&*')).toBe('');
      expect(sanitizeName('!@#$%^&*()')).toBe('()'); // Parentheses are now valid
      expect(sanitizeName('!@#$%^&*-')).toBe('-'); // Dashes are now valid
      expect(sanitizeName('!@#$%^&*()-')).toBe('()-'); // Parentheses and dashes are valid
      expect(sanitizeName('   ')).toBe(' '); // Multiple spaces become single space
      expect(sanitizeName('!@#$%^&* ')).toBe(' '); // Spaces are now valid
    });
  });
});
