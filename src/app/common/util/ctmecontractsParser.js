/**
 * Parses and decodes CTMecontracts.com email links to extract the actual PDF URL
 * @param {string} emailLink - The complex email link from CTMecontracts.com
 * @returns {object} - Object containing the decoded URL and filename
 */
export function parseCTMecontractsEmailLink(emailLink) {
  try {
    // Check if it's a SafeLinks URL
    if (emailLink.includes('safelinks.protection.outlook.com')) {
      // Extract the URL parameter from SafeLinks
      const urlMatch = emailLink.match(/url=([^&]+)/);
      if (urlMatch) {
        const encodedUrl = urlMatch[1];
        const decodedUrl = decodeURIComponent(encodedUrl);
        return parseCTMecontractsDirectLink(decodedUrl);
      }
    }
    
    // If it's already a direct CTMecontracts link
    if (emailLink.includes('ctmecontracts.com')) {
      return parseCTMecontractsDirectLink(emailLink);
    }
    
    throw new Error('Invalid CTMecontracts.com email link format');
  } catch (error) {
    console.error('Error parsing CTMecontracts email link:', error);
    throw new Error('Failed to parse CTMecontracts.com email link');
  }
}

/**
 * Parses a direct CTMecontracts.com PDF link
 * @param {string} directLink - Direct link to CTMecontracts.com PDF
 * @returns {object} - Object containing the URL and filename
 */
function parseCTMecontractsDirectLink(directLink) {
  try {
    const url = new URL(directLink);
    
    // Validate it's a CTMecontracts.com domain
    if (!url.hostname.includes('ctmecontracts.com')) {
      throw new Error('Not a valid CTMecontracts.com URL');
    }
    
    // Extract filename from the 'fn' parameter
    const fnParam = url.searchParams.get('fn');
    let filename = 'document.pdf';
    
    if (fnParam) {
      // Decode and clean the filename
      filename = decodeURIComponent(fnParam)
        .replace(/\+/g, ' ')
        .replace(/[^\w\s\-_.()]/g, '')
        .trim();
      
      // Ensure it has a .pdf extension
      if (!filename.toLowerCase().endsWith('.pdf')) {
        filename += '.pdf';
      }
    }
    
    return {
      url: directLink,
      filename: filename,
      isValid: true
    };
  } catch (error) {
    console.error('Error parsing direct CTMecontracts link:', error);
    throw new Error('Invalid CTMecontracts.com URL format');
  }
}

/**
 * Validates if a URL is a CTMecontracts.com email link
 * @param {string} url - URL to validate
 * @returns {boolean} - True if valid CTMecontracts.com email link
 */
export function isValidCTMecontractsEmailLink(url) {
  try {
    if (!url || typeof url !== 'string') return false;
    
    // Check for SafeLinks format
    if (url.includes('safelinks.protection.outlook.com') && url.includes('ctmecontracts.com')) {
      return true;
    }
    
    // Check for direct CTMecontracts link
    if (url.includes('ctmecontracts.com') && url.includes('.pdf')) {
      return true;
    }
    
    return false;
  } catch (error) {
    return false;
  }
}
