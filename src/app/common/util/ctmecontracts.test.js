import {
  parseCTMecontractsEmailLink,
  isValidCTMecontractsEmailLink
} from './ctmecontractsParser';

describe('CTMecontracts URL Parsing', () => {
  const sampleSafeLinksUrl = 'https://nam12.safelinks.protection.outlook.com/?url=https%3A%2F%2Fwww.ctmecontracts.com%2Ffiles%2FPDFConvert%2F7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf%3Ffn%3D2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf%26dwn%3D1%26ST%3D&data=05%7C02%7Celizabeth.boese%40cbrealty.com%7C99e3f08dfe6241b9930108dd880214d8%7C28743320645e48408154b4babd41162c%7C1%7C1%7C638816264723318600%7CUnknown%7CTWFpbGZsb3d8eyJFbXB0eU1hcGkiOnRydWUsIlYiOiIwLjAuMDAwMCIsIlAiOiJXaW4zMiIsIkFOIjoiTWFpbCIsIldUIjoyfQ%3D%3D%7C0%7C%7C%7C&sdata=q4J1VHSToTCVjhcex6xbKNEIDBbfpmm4XDoVDN59zJY%3D&reserved=0';
  
  const sampleDirectUrl = 'https://www.ctmecontracts.com/files/PDFConvert/7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf?fn=2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf&dwn=1&ST=';

  describe('isValidCTMecontractsEmailLink', () => {
    test('should validate SafeLinks URLs', () => {
      expect(isValidCTMecontractsEmailLink(sampleSafeLinksUrl)).toBe(true);
    });

    test('should validate direct CTMecontracts URLs', () => {
      expect(isValidCTMecontractsEmailLink(sampleDirectUrl)).toBe(true);
    });

    test('should reject invalid URLs', () => {
      expect(isValidCTMecontractsEmailLink('https://google.com')).toBe(false);
      expect(isValidCTMecontractsEmailLink('not-a-url')).toBe(false);
      expect(isValidCTMecontractsEmailLink('')).toBe(false);
      expect(isValidCTMecontractsEmailLink(null)).toBe(false);
    });
  });

  describe('parseCTMecontractsEmailLink', () => {
    test('should parse SafeLinks URLs correctly', () => {
      const result = parseCTMecontractsEmailLink(sampleSafeLinksUrl);
      
      expect(result.isValid).toBe(true);
      expect(result.url).toContain('ctmecontracts.com');
      expect(result.filename).toContain('2802 Sundown LN');
      expect(result.filename).toContain('CONTRACT TO BUY AND SELL REAL ESTATE');
      expect(result.filename.endsWith('.pdf')).toBe(true);
    });

    test('should parse direct URLs correctly', () => {
      const result = parseCTMecontractsEmailLink(sampleDirectUrl);
      
      expect(result.isValid).toBe(true);
      expect(result.url).toBe(sampleDirectUrl);
      expect(result.filename).toContain('2802 Sundown LN');
      expect(result.filename.endsWith('.pdf')).toBe(true);
    });

    test('should handle URLs without filename parameter', () => {
      const urlWithoutFn = 'https://www.ctmecontracts.com/files/PDFConvert/test.pdf';
      const result = parseCTMecontractsEmailLink(urlWithoutFn);
      
      expect(result.isValid).toBe(true);
      expect(result.filename).toBe('document.pdf');
    });

    test('should throw error for invalid URLs', () => {
      expect(() => {
        parseCTMecontractsEmailLink('https://google.com');
      }).toThrow();

      expect(() => {
        parseCTMecontractsEmailLink('not-a-url');
      }).toThrow();
    });

    test('should clean filename properly', () => {
      const urlWithSpecialChars = 'https://www.ctmecontracts.com/files/PDFConvert/test.pdf?fn=Test%20Document%20%26%20More%21%40%23%24%25.pdf';
      const result = parseCTMecontractsEmailLink(urlWithSpecialChars);
      
      expect(result.filename).not.toContain('&');
      expect(result.filename).not.toContain('!');
      expect(result.filename).not.toContain('@');
      expect(result.filename.endsWith('.pdf')).toBe(true);
    });
  });
});
