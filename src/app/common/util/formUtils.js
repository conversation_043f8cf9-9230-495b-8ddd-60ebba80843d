// Field conversion mapping options for auto-populating form fields
// Currently ONLY used for creating new forms (or using the edit form)
// DO NOT USE on old existing forms that are not set up for these field names 2025.12
export const fieldConversionMappings = [
  { pdfFieldName: "Logo", convertTo: "transaction_brokerageLogoRef" },

  { pdfFieldName: "Date", convertTo: "transaction_currentDate" },
  { pdfFieldName: "Contract Date Text", convertTo: "transaction_contractDateText"},
  { pdfFieldName: "Date Contract Date", convertTo: "transaction_contractDateTime" },
  { pdfFieldName: "Closing Date Text", convertTo: "transaction_closingDateText"},
  { pdfFieldName: "Date Closing Date", convertTo: "transaction_closingDateTime" },

  { pdfFieldName: "Seller", convertTo: "transaction_sellerLegalName" },
  { pdfFieldName: "Buyer", convertTo: "transaction_buyerLegalName" },

  { pdfFieldName: "Address Full", convertTo: "transaction_addressFull" },
  { pdfFieldName: "Address Street", convertTo: "transaction_addressStreetAndUnit" },
  { pdfFieldName: "Address City State Zip", convertTo: "transaction_addressCityStateZip" },
  { pdfFieldName: "Address City", convertTo: "transaction_addressCity" },
  { pdfFieldName: "Address State", convertTo: "transaction_addressState" },
  { pdfFieldName: "Address Zip", convertTo: "transaction_addressZipcode" },
  { pdfFieldName: "Address Legal Description", convertTo: "transaction_addressLegal" },
  { pdfFieldName: "Address County", convertTo: "transaction_addressCounty" },

  { pdfFieldName: "Seller 1 Name", convertTo: "seller_legalName" },
  { pdfFieldName: "Seller 2 Name", convertTo: "seller2_legalName" },
  { pdfFieldName: "Seller 3 Name", convertTo: "seller3_legalName" },
  { pdfFieldName: "Buyer 1 Name", convertTo: "buyer_legalName" },
  { pdfFieldName: "Buyer 2 Name", convertTo: "buyer2_legalName" },
  { pdfFieldName: "Buyer 3 Name", convertTo: "buyer3_legalName" },
 
  { pdfFieldName: "Seller Broker Name", convertTo: "brokerSeller_fullname" },
  { pdfFieldName: "Seller Broker Phone", convertTo: "brokerSeller_phone" },
  { pdfFieldName: "Seller Broker Email", convertTo: "brokerSeller_email" },
  { pdfFieldName: "Seller Broker License", convertTo: "brokerSeller_brokerLicenseNumber" },
  { pdfFieldName: "Seller Brokerage Firm Name", convertTo: "transaction_sellerBrokerageName" },
  { pdfFieldName: "Seller Brokerage Firm License", convertTo: "brokerSeller_brokerageLicenseNumber" },
  { pdfFieldName: "Seller Brokerage Address", convertTo: "brokerSeller_addressFull" },
  { pdfFieldName: "Seller Brokerage Address Street", convertTo: "brokerSeller_addressStreetAndUnit" },
  { pdfFieldName: "Seller Brokerage Address City State Zip", convertTo: "brokerSeller_addressCityStateZip" },
  { pdfFieldName: "Seller CoAgent Name", convertTo: "coAgentSeller_fullname" },
 
  { pdfFieldName: "Buyer Broker Name", convertTo: "brokerBuyer_fullname" },
  { pdfFieldName: "Buyer Broker Phone", convertTo: "brokerBuyer_phone" },
  { pdfFieldName: "Buyer Broker Email", convertTo: "brokerBuyer_email" },
  { pdfFieldName: "Buyer Broker License", convertTo: "brokerBuyer_brokerLicenseNumber" },
  { pdfFieldName: "Buyer Brokerage Firm Name", convertTo: "transaction_buyerBrokerageName" },
  { pdfFieldName: "Buyer Brokerage Firm License", convertTo: "brokerBuyer_brokerageLicenseNumber" },
  { pdfFieldName: "Buyer Brokerage Address", convertTo: "brokerBuyer_addressFull" },
  { pdfFieldName: "Buyer Brokerage Address Street", convertTo: "brokerBuyer_addressStreetAndUnit" },
  { pdfFieldName: "Buyer Brokerage Address City State Zip", convertTo: "brokerBuyer_addressCityStateZip" },
  { pdfFieldName: "Buyer CoAgent Name", convertTo: "coAgentBuyer_fullname" },

  { pdfFieldName: "Client Names Legal", convertTo: "transaction_clientFullNames" },
  { pdfFieldName: "Client Emails", convertTo: "transaction_clientAllEmails" },
  { pdfFieldName: "Client 1 Name Legal", convertTo: "transaction_client1FullName" },
  { pdfFieldName: "Client 1 Name Signer", convertTo: "transaction_client1SignerFullName" },
  { pdfFieldName: "Client 1 Email", convertTo: "transaction_client1Email" },
  { pdfFieldName: "Client 2 Name Legal", convertTo: "transaction_client2FullName" },
  { pdfFieldName: "Client 2 Name Signer", convertTo: "transaction_client2SignerFullName" },
  { pdfFieldName: "Client 2 Email", convertTo: "transaction_client2Email" },
  { pdfFieldName: "Client 3 Name Legal", convertTo: "transaction_client3FullName" },
  { pdfFieldName: "Client 3 Name Signer", convertTo: "transaction_client3SignerFullName" },
  { pdfFieldName: "Client 3 Email", convertTo: "transaction_client3Email" },

  { pdfFieldName: "Title Company Name", convertTo: "titleCompany_companyName" },
  { pdfFieldName: "Title Company Address Full", convertTo: "titleCompany_addressFull" },
  { pdfFieldName: "Title Company Address Street", convertTo: "titleCompany_addressStreetAndUnit" },
  { pdfFieldName: "Title Company Address City State Zip", convertTo: "titleCompany_addressCityStateZip" },
  { pdfFieldName: "Title Company Phone", convertTo: "titleCompany_phone" },
  { pdfFieldName: "Title Company Email", convertTo: "titleCompany_email" },

  { pdfFieldName: "Listing Inclusions", convertTo: "transaction_inclusions" },
  { pdfFieldName: "Listing Exclusions", convertTo: "transaction_exclusions" },
  { pdfFieldName: "Listing Year Built", convertTo: "transaction_yearBuilt" },
];