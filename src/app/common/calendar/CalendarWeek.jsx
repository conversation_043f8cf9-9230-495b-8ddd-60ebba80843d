import { addDays, format, isSameDay } from "date-fns";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { Grid } from "semantic-ui-react";
import { openModal } from "../modals/modalSlice";

export function CalendarWeek({ events, hideNavigate }) {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [filteredEvents, setFilteredEvents] = useState([]);
  const { id } = useParams();

  useEffect(() => {
    if (events) {
      const visibleEvents = events.filter(
        (event) =>
          !event.visibleTo ||
          event.visibleTo.length === 0 ||
          event.visibleTo.includes(currentUserProfile.userId)
      );
      setFilteredEvents(visibleEvents);
    }
  }, [events, currentUserProfile]);

  let eventsByDay = [];
  let days = [0, 1, 2, 3];

  days.forEach((day) => {
    eventsByDay[day] = filteredEvents?.filter((event) =>
      isSameDay(event.end, addDays(new Date(), day))
    );
  });

  function handleClick(event) {
    dispatch(
      openModal({
        modalType: "TaskViewModal",
        modalProps: { task: event, hideNavigate: hideNavigate },
      })
    );
  }

  return (
    <Grid style={{ minHeight: "100px" }} stackable columns={4}>
      {days.map((day) => (
        <Grid.Column
          key={day}
          width={4}
          className={day === 0 ? "background-blue" : "background-white"}
          style={{
            paddingTop: "4px",
            boxShadow: "0 0 0 1px #d4d4d5",
          }}
        >
          <p className="inline tiny bottom margin">
            {format(addDays(new Date(), day), "EEEE")}
          </p>
          <span className="float-right">
            {format(addDays(new Date(), day), "d")}
          </span>
          {eventsByDay[day]?.map((event) => (
            <div
              className="tiny bottom margin"
              onClick={() => handleClick(event)}
              key={event.id}
            >
              <div
                className={
                  event.status === "Complete"
                    ? "calendar-event-complete"
                    : "calendar-event"
                }
              >
                {/* {truncateText(event.title, 20)} */}
                {event.title}
                {event.category === "Calendar Entry" && (
                  <>&nbsp;{format(event.start, "h:mm aaaaa'm'")}</>
                )}
                {!id && (
                  <>&nbsp;-&nbsp;{event.transactionTitle.split(" -")[0]}</>
                )}
              </div>
            </div>
          ))}
        </Grid.Column>
      ))}
    </Grid>
  );
}
