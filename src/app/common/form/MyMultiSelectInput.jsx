import { useField } from "formik";
import React from "react";
import { Dropdown, FormField, Label } from "semantic-ui-react";

export default function MyMultiSelectInput({ label, ...props }) {
  const [field, meta, helpers] = useField(props);
  return (
    <FormField error={meta.touched && !!meta.error}>
      <label>{label}</label>
      <Dropdown
        fluid
        multiple
        selection
        value={field.value || []}
        onChange={(e, d) => helpers.setValue(d.value)}
        onBlur={() => helpers.setTouched(true)}
        {...props}
      />
      {meta.touched && meta.error ? (
        <Label basic size="large" color="red">
          {meta.error}
        </Label>
      ) : null}
    </FormField>
  );
}
