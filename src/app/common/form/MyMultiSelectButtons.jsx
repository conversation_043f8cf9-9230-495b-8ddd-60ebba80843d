import { Field, useField } from "formik";
import React from "react";
import { FormField } from "semantic-ui-react";

export default function MyMultiSelectButtons({ label, ...props }) {
  const [meta] = useField(props);

  return props.options.map((o) => {
    return (
      <FormField
        key={o.value}
        error={meta.touched && !!meta.error}
        className="inline-flex"
      >
        <>
          <Field
            type="checkbox"
            name={props.name}
            id={o.value}
            value={o.value}
            hidden
          />
          <label htmlFor={o.value}>{o.value}</label>
          &nbsp;&nbsp;&nbsp;
        </>
      </FormField>
    );
  });
}
