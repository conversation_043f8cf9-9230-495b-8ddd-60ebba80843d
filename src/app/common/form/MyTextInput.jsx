import { useField } from "formik";
import React from "react";
import { FormField, Label } from "semantic-ui-react";

export default function MyTextInput({ label, ...props }) {
  const [field, meta, helpers] = useField(props);

  function handleChange(e) {
    helpers.setValue(e.target.value?.trimStart());
  }

  return (
    <FormField error={meta.touched && !!meta.error}>
      <label>{label}</label>
      <input
        {...field}
        {...props}
        onChange={(e) => {
          handleChange(e);
        }}
      />
      {meta.touched && meta.error ? (
        <Label basic size="large" color="red">
          {meta.error}
        </Label>
      ) : null}
    </FormField>
  );
}
