import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { asyncActionError, asyncActionFinish, asyncActionStart } from "../async/asyncSlice";
import { dataFromSnapshot } from "../firestore/firestoreService";
import { onSnapshot } from '@firebase/firestore';

export default function useFirestoreDoc({query, data, deps}) {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(asyncActionStart());
    const unsubscribe = onSnapshot(query(),
      snapshot => {
        if (!snapshot.exists) {
          dispatch(asyncActionError({
            code: "not-found",
            message: "Could not find document"
          }));
          return;
        }
        data(dataFromSnapshot(snapshot));
        dispatch(asyncActionFinish());
      },
      error => {
        // Handle permission-denied errors gracefully for non-user agents
        if (error.code === 'permission-denied') {
          console.warn('Firestore permission denied - this is expected for non-user agents:', error.message);
          // Don't dispatch error for permission-denied, just finish the async action
          dispatch(asyncActionFinish());
        } else {
          // For other errors, dispatch the error as usual
          dispatch(asyncActionError(error));
        }
      }
    );
    return () => {
      unsubscribe()
    }
  }, deps) // eslint-disable-line react-hooks/exhaustive-deps
}