import React from "react";
import { useDispatch, useSelector } from "react-redux";
import useFirestoreCollection from "../hooks/useFirestoreCollection";
import { fetchAgentsForManager } from "../../features/profiles/profileSlice";
import {
  fetchStateFormsFromDb,
  fetchAgentsForManagerFromDb,
  fetchTasksUpcomingForManagerFromDb,
  fetchTransActiveForManagerFromDb,
  fetchTransClosedForManagerFromDb,
  fetchBrokerageFormsFromDb,
} from "./firestoreService";
import {
  fetchTransActiveForManager,
  fetchTransClosedForManager,
} from "../../features/transactions/transactionSlice";
import { fetchTasksUpcomingForManager } from "../../features/tasks/taskSlice";
import { fetchStateForms } from "../../features/docs/docSlice";
import { fetchBrokerageForms } from "../../features/managers/managerBrokerage/brokerageCustomizationsSlice";

export default function ManagerData() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);

  useFirestoreCollection({
    query: () => fetchAgentsForManagerFromDb(),
    data: (agents) =>
      dispatch(
        fetchAgentsForManager({
          agents: agents,
          managerId:
            currentUserProfile.role === "manager"
              ? currentUserProfile.userId
              : currentUserProfile.role === "managerassistant"
              ? currentUserProfile.authCustomClaims?.m?.[0]
              : "",
        })
      ),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchTransActiveForManagerFromDb(),
    data: (transactions) => dispatch(fetchTransActiveForManager(transactions)),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchTransClosedForManagerFromDb(),
    data: (transactions) => dispatch(fetchTransClosedForManager(transactions)),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchTasksUpcomingForManagerFromDb(),
    data: (tasks) => dispatch(fetchTasksUpcomingForManager(tasks)),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchStateFormsFromDb(),
    data: (forms) => dispatch(fetchStateForms(forms)),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () =>
      fetchBrokerageFormsFromDb(
        currentUserProfile.state,
        currentUserProfile.brokerageForms
      ),
    data: (brokerageForms) => dispatch(fetchBrokerageForms(brokerageForms)),
    deps: [
      dispatch,
      currentUserProfile.state,
      currentUserProfile.brokerageForms,
    ],
  });

  return <></>;
}
