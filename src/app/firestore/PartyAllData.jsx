import React from "react";
import { useDispatch } from "react-redux";
import useFirestoreCollection from "../hooks/useFirestoreCollection";
import {
  fetchTransactionsForUserFromDb,
  fetchPartiesForUserFromDb,
  fetchDocsNeedingSignatureForUserFromDb,
} from "./firestoreService";
import {
  fetchPartiesForUser,
  fetchTransactionsForUser,
  fetchDocsNeedingSignature,
} from "../../features/parties/partySlice";

export default function PartyAllData() {
  const dispatch = useDispatch();

  useFirestoreCollection({
    query: () => fetchPartiesForUserFromDb(),
    data: (parties) => dispatch(fetchPartiesForUser({ parties: parties })),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchTransactionsForUserFromDb(),
    data: (transactions) => dispatch(fetchTransactionsForUser(transactions)),
    deps: [dispatch],
  });

  useFirestoreCollection({
    query: () => fetchDocsNeedingSignatureForUserFromDb(),
    data: (docs) => {
      dispatch(fetchDocsNeedingSignature(docs));
    },
    deps: [dispatch],
  });

  return <></>;
}