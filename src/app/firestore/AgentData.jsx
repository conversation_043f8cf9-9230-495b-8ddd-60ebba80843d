import React from "react";
import { useDispatch } from "react-redux";
import useFirestoreCollection from "../hooks/useFirestoreCollection";
import {
  fetchTransClosedForAgentFromDb,
} from "./firestoreService";
import {
  fetchTransClosedForAgent,
} from "../../features/transactions/transactionSlice";

export default function AgentData() {
  const dispatch = useDispatch();

  useFirestoreCollection({
    query: () => {
      return fetchTransClosedForAgentFromDb();
    },
    data: (transactions) => {
      dispatch(fetchTransClosedForAgent(transactions));
    },
    deps: [dispatch],
  });

  return <></>;
}
