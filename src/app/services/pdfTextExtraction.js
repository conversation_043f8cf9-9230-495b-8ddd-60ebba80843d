/**
 * Extracts text from a PDF file
 * NOTE: This is a placeholder implementation. In production, this should be replaced
 * with a proper PDF text extraction service, possibly using a backend API call
 * to Firebase Functions that can use PDFParser or similar server-side libraries.
 *
 * @param {File} file - The PDF file to extract text from
 * @returns {Promise<string>} - The extracted text content
 */
export async function extractTextFromPdf(file) {
  try {
    if (!file) {
      throw new Error('No file provided');
    }

    if (file.type !== 'application/pdf') {
      throw new Error('File must be a PDF');
    }

    // TODO: Replace this with actual PDF text extraction
    // For now, return a mock response to allow testing of the UI
    console.warn('PDF text extraction is not yet implemented. Using mock data for testing.');

    // Mock extracted text for testing purposes
    const mockText = `
    3.1. Seller: <PERSON> and <PERSON>

    3.4. Property. The real property described as:
    Lot 1, Block 2, Sample Subdivision
    County of Jefferson, Colorado:
    123 Main Street, Denver, CO 80202
    known as No. 123 Main Street, Denver, CO 80202

    3.7. Listing Period. This Agreement shall begin on 01/01/2024, at 11:59 p.m. and shall continue until the earlier of: (1) completion of the sale or exchange of the Property, or (2) 06/30/2024, at 11:59 p.m.

    Seller: <PERSON>
    Date: 01/01/2024

    Seller: Jane Doe
    Date: 01/01/2024
    `;

    // Normalize the text for better parsing
    const normalizedText = normalizeExtractedText(mockText);

    return normalizedText;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw new Error(`Failed to extract text from PDF: ${error.message}`);
  }
}

/**
 * Normalizes extracted PDF text for more reliable regex parsing
 * @param {string} text - Raw text extracted from PDF
 * @returns {string} - Normalized text
 */
function normalizeExtractedText(text) {
  if (!text) return '';

  return text
    .replace(/\r/g, '') // Remove carriage returns
    .replace(/\u00a0/g, ' ') // Replace non-breaking spaces with regular spaces
    .replace(/[ ]{2,}/g, ' ') // Collapse multiple spaces into single space
    .replace(/\n{3,}/g, '\n\n') // Collapse multiple newlines
    .trim(); // Remove leading/trailing whitespace
}

/**
 * Extracts text from multiple PDF files
 * @param {FileList|Array} files - Array of PDF files
 * @returns {Promise<Array>} - Array of objects with filename and extracted text
 */
export async function extractTextFromMultiplePdfs(files) {
  const results = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      const text = await extractTextFromPdf(file);
      results.push({
        filename: file.name,
        text: text,
        success: true,
        error: null
      });
    } catch (error) {
      results.push({
        filename: file.name,
        text: null,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

/**
 * Validates if a file is a PDF and can be processed
 * @param {File} file - File to validate
 * @returns {boolean} - True if file is valid PDF
 */
export function isValidPdfFile(file) {
  if (!file) return false;
  
  // Check file type
  if (file.type !== 'application/pdf') return false;
  
  // Check file extension
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith('.pdf')) return false;
  
  // Check file size (max 50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB in bytes
  if (file.size > maxSize) return false;
  
  return true;
}

/**
 * Filters an array of files to only include valid PDFs
 * @param {FileList|Array} files - Files to filter
 * @returns {Array} - Array of valid PDF files
 */
export function filterValidPdfFiles(files) {
  const fileArray = Array.from(files);
  return fileArray.filter(file => isValidPdfFile(file));
}
