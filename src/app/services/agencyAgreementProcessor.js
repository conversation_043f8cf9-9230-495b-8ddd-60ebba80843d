// Import the extraction functions - we'll need to copy them to the src directory
// or create wrapper functions since they're in the functions directory
import { extractColoradoListingData } from "./coloradoDataExtractors";
import { extractColoradoBuyerAgencyData } from "./coloradoDataExtractors";
import { addTransactionToDb } from "../firestore/firestoreService";
import { addUploadedDocToTransaction } from "../firestore/firebaseService";

/**
 * Creates a new Seller transaction from Colorado Exclusive Right to Sell Listing agreement
 * @param {string} pdfText - Extracted text from the PDF
 * @param {File} pdfFile - The original PDF file
 * @param {Object} currentUserProfile - Current user profile
 * @returns {Promise<Object>} - Object with transactionId and type
 */
export async function createTransactionFromListingAgreement(pdfText, pdfFile, currentUserProfile) {
  try {
    // Extract data using the existing function
    const extractedData = extractColoradoListingData(pdfText);
    
    if (!extractedData.seller_3_1) {
      throw new Error('Unable to extract seller information from the listing agreement');
    }

    // Parse seller names
    const sellerNames = parseSellerNames(extractedData.seller_3_1, extractedData.signerSellers);
    
    // Create transaction data structure
    const transactionData = {
      active: true,
      agentRepresents: "Seller",
      client: sellerNames.primary,
      clientSecondary: sellerNames.secondary || { exists: false },
      clientThird: { exists: false },
      hasAssistant: false,
      assistant: {},
      managerId: currentUserProfile.managerId || "",
      title: generateTransactionTitle(sellerNames.primary, extractedData.knownAsNo_3_4),
      address: parsePropertyAddress(extractedData.knownAsNo_3_4),
      county: extractedData.county_3_4 || "",
      legalDescription: extractedData.legalDescription_3_4 || "",
      timeZone: "America/Denver", // Colorado default
      listingBegins: parseDate(extractedData.listingBegins_3_7),
      listingEnds: parseDate(extractedData.listingEnds_3_7),
    };

    // Create the transaction
    const transactionId = await addTransactionToDb(
      transactionData,
      [], // people
      [], // forms
      currentUserProfile,
      null, // mlsData
      [], // agentsForAssistant
      null, // formTemplates
      [] // taskTemplates
    );

    // Add the PDF to the transaction documents
    await addUploadedDocToTransaction(
      URL.createObjectURL(pdfFile),
      pdfFile.name,
      pdfFile.type,
      pdfFile.size,
      { id: transactionId, userId: currentUserProfile.userId }
    );

    return {
      transactionId,
      type: "Seller"
    };

  } catch (error) {
    console.error('Error creating transaction from listing agreement:', error);
    throw new Error(`Failed to create transaction: ${error.message}`);
  }
}

/**
 * Creates a new Buyer transaction from Colorado Buyer Agency agreement
 * @param {string} pdfText - Extracted text from the PDF
 * @param {File} pdfFile - The original PDF file
 * @param {Object} currentUserProfile - Current user profile
 * @returns {Promise<Object>} - Object with transactionId and type
 */
export async function createTransactionFromBuyerAgreement(pdfText, pdfFile, currentUserProfile) {
  try {
    // Extract data using the existing function
    const extractedData = extractColoradoBuyerAgencyData(pdfText);
    
    if (!extractedData.buyer_3_1) {
      throw new Error('Unable to extract buyer information from the buyer agency agreement');
    }

    // Parse buyer names
    const buyerNames = parseBuyerNames(extractedData.buyer_3_1, extractedData.buyerSigners);
    
    // Create transaction data structure
    const transactionData = {
      active: true,
      agentRepresents: "Buyer",
      client: buyerNames.primary,
      clientSecondary: buyerNames.secondary || { exists: false },
      clientThird: { exists: false },
      hasAssistant: false,
      assistant: {},
      managerId: currentUserProfile.managerId || "",
      title: generateTransactionTitle(buyerNames.primary, extractedData.propertyDescription_3_4),
      address: parsePropertyDescription(extractedData.propertyDescription_3_4),
      county: extractCountyFromDescription(extractedData.propertyDescription_3_4),
      legalDescription: extractedData.propertyDescription_3_4 || "",
      timeZone: "America/Denver", // Colorado default
      listingBegins: parseDate(extractedData.listingBegins_3_6),
      listingEnds: parseDate(extractedData.listingEnds_3_6),
    };

    // Create the transaction
    const transactionId = await addTransactionToDb(
      transactionData,
      [], // people
      [], // forms
      currentUserProfile,
      null, // mlsData
      [], // agentsForAssistant
      null, // formTemplates
      [] // taskTemplates
    );

    // Add the PDF to the transaction documents
    await addUploadedDocToTransaction(
      URL.createObjectURL(pdfFile),
      pdfFile.name,
      pdfFile.type,
      pdfFile.size,
      { id: transactionId, userId: currentUserProfile.userId }
    );

    return {
      transactionId,
      type: "Buyer"
    };

  } catch (error) {
    console.error('Error creating transaction from buyer agreement:', error);
    throw new Error(`Failed to create transaction: ${error.message}`);
  }
}

/**
 * Parses seller names from extracted data
 * @param {string} seller_3_1 - Raw seller line from section 3.1
 * @param {Array} signerSellers - Array of seller signature names
 * @returns {Object} - Object with primary and secondary seller info
 */
function parseSellerNames(seller_3_1, signerSellers = []) {
  const names = parseNames(seller_3_1);
  
  return {
    primary: names.primary,
    secondary: names.secondary
  };
}

/**
 * Parses buyer names from extracted data
 * @param {string} buyer_3_1 - Raw buyer line from section 3.1
 * @param {Array} buyerSigners - Array of buyer signature names
 * @returns {Object} - Object with primary and secondary buyer info
 */
function parseBuyerNames(buyer_3_1, buyerSigners = []) {
  const names = parseNames(buyer_3_1);
  
  return {
    primary: names.primary,
    secondary: names.secondary
  };
}

/**
 * Generic name parsing function
 * @param {string} nameString - Raw name string
 * @returns {Object} - Object with primary and secondary name info
 */
function parseNames(nameString) {
  if (!nameString) {
    return {
      primary: { firstName: "", lastName: "", email: "", phone: "", isTrust: false },
      secondary: null
    };
  }

  // Check if it's a trust or entity
  const isTrust = nameString.toLowerCase().includes('trust') || 
                  nameString.toLowerCase().includes('llc') ||
                  nameString.toLowerCase().includes('inc') ||
                  nameString.toLowerCase().includes('corp');

  if (isTrust) {
    return {
      primary: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        isTrust: true,
        entityName: nameString.trim(),
        authorityTitle: "",
        signerName: ""
      },
      secondary: null
    };
  }

  // Split names by "and" or "&"
  const nameParts = nameString.split(/\s+and\s+|\s*&\s+/i);
  
  const primary = parseIndividualName(nameParts[0]);
  const secondary = nameParts.length > 1 ? parseIndividualName(nameParts[1]) : null;

  return { primary, secondary };
}

/**
 * Parses an individual name string
 * @param {string} nameString - Individual name string
 * @returns {Object} - Name object
 */
function parseIndividualName(nameString) {
  if (!nameString) {
    return { firstName: "", lastName: "", email: "", phone: "", isTrust: false };
  }

  const cleanName = nameString.trim();
  const nameParts = cleanName.split(/\s+/);
  
  let firstName = "";
  let lastName = "";
  
  if (nameParts.length === 1) {
    lastName = nameParts[0];
  } else if (nameParts.length === 2) {
    firstName = nameParts[0];
    lastName = nameParts[1];
  } else if (nameParts.length >= 3) {
    firstName = nameParts[0];
    // Take the last part as last name, middle parts as middle name
    lastName = nameParts[nameParts.length - 1];
  }

  return {
    firstName,
    lastName,
    email: "",
    phone: "",
    isTrust: false
  };
}

/**
 * Generates a transaction title
 * @param {Object} primaryClient - Primary client object
 * @param {string} propertyInfo - Property information
 * @returns {string} - Generated title
 */
function generateTransactionTitle(primaryClient, propertyInfo) {
  const clientName = primaryClient.isTrust ? 
    primaryClient.entityName : 
    `${primaryClient.firstName} ${primaryClient.lastName}`.trim();
  
  const property = propertyInfo ? ` - ${propertyInfo.substring(0, 50)}` : "";
  
  return `${clientName}${property}`;
}

/**
 * Parses property address from known address string
 * @param {string} knownAsNo - Known address string
 * @returns {Object} - Address object
 */
function parsePropertyAddress(knownAsNo) {
  if (!knownAsNo) {
    return { street: "", unit: "", city: "", state: "CO", zipcode: "" };
  }

  // Basic address parsing - this could be enhanced
  return {
    street: knownAsNo.trim(),
    unit: "",
    city: "",
    state: "CO",
    zipcode: ""
  };
}

/**
 * Parses property description for buyer agreements
 * @param {string} propertyDescription - Property description
 * @returns {Object} - Address object
 */
function parsePropertyDescription(propertyDescription) {
  if (!propertyDescription) {
    return { street: "", unit: "", city: "", state: "CO", zipcode: "" };
  }

  return {
    street: propertyDescription.substring(0, 100), // Truncate if too long
    unit: "",
    city: "",
    state: "CO",
    zipcode: ""
  };
}

/**
 * Extracts county from property description
 * @param {string} propertyDescription - Property description
 * @returns {string} - County name
 */
function extractCountyFromDescription(propertyDescription) {
  if (!propertyDescription) return "";
  
  const countyMatch = propertyDescription.match(/(\w+)\s+County/i);
  return countyMatch ? countyMatch[1] : "";
}

/**
 * Parses date string to Date object
 * @param {string} dateString - Date string (MM/DD/YYYY format)
 * @returns {Date|null} - Parsed date or null
 */
function parseDate(dateString) {
  if (!dateString) return null;
  
  try {
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const month = parseInt(parts[0], 10);
      const day = parseInt(parts[1], 10);
      const year = parseInt(parts[2], 10);
      
      if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1900) {
        return new Date(year, month - 1, day);
      }
    }
  } catch (error) {
    console.warn('Error parsing date:', dateString, error);
  }
  
  return null;
}
