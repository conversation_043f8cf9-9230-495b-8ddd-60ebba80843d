// Colorado data extraction functions
// These are copies of the functions from the functions directory to be used in the frontend

export function extractColoradoListingData(pdfTextRaw) {
  const text = pdfTextRaw
    .replace(/\r/g, "")
    .replace(/\u00a0/g, " ")
    .replace(/[ ]{2,}/g, " ");

  const matchOne = (regex) => {
    const m = text.match(regex);
    return m && m[1] ? m[1].trim() : null;
  };

  const seller_3_1 = matchOne(/3\.1\.\s*Seller:\s*(.+)/i);
  const county_3_4 = matchOne(/County of\s+([A-Za-z\s]+?),\s*[\n ]*Colorado:/i);
  const legalDescription_3_4 = matchOne(
    /Colorado:\s*([\s\S]*?)\s*known as No\./i
  );
  const knownAsNo_3_4 = matchOne(
    /known as No\.\s*([^\n,]+(?:,?\s*[A-Za-z0-9#. ]+)*)\s*,/i
  );
  const listingBegins_3_7 = matchOne(
    /3\.7\.\s*Listing Period[\s\S]*?begins on\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})\s*,/i
  );
  const listingEnds_3_7 = matchOne(
    /3\.7\.\s*Listing Period[\s\S]*?\(2\)\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})\s*,/i
  );

  const signerSellers = [];
  const sellerRegex = /Seller:\s*([^\n]+)/gi;
  let m;
  while ((m = sellerRegex.exec(text)) !== null) {
    const name = m[1].trim();
    if (name && !signerSellers.includes(name)) {
      signerSellers.push(name);
    }
  }

  return {
    seller_3_1,
    county_3_4,
    legalDescription_3_4,
    knownAsNo_3_4,
    listingBegins_3_7,
    listingEnds_3_7,
    signerSellers,
  };
}

export function extractColoradoBuyerAgencyData(pdfTextRaw) {
  // Normalize text a bit for more reliable regex
  const text = pdfTextRaw
    .replace(/\r/g, "")
    .replace(/\u00a0/g, " ") // non-breaking spaces
    .replace(/[ ]{2,}/g, " "); // collapse extra spaces

  // Helper: safe regex match
  const matchOne = (regex) => {
    const m = text.match(regex);
    return m && m[1] ? m[1].trim() : null;
  };

  // 3.1 Buyer: Meghan Phillips
  // 3.1 Buyer: Leonard W. Vessels and Kimberly S. Vessels
  const buyer_3_1 = matchOne(/3\.1\.\s*Buyer:\s*(.+)/i);

  // 3.4 Property. Property means real estate which substantially meets...
  //   1015 E. Stollsteimer or 244 Carols Curves
  //   Real Estate in Archuleta County
  //
  // Capture text from after "3.4.  Property." line down to just before "3.5."
  const propertyDescription_3_4 = matchOne(
    /3\.4\.\s*Property\.[\s\S]*?acceptable to Buyer:\s*([\s\S]*?)\s*3\.5\./i
  );

  // 3.6 Listing Period. begins on 9/21/2025 ... or (2) 12/31/2025
  const listingBegins_3_6 = matchOne(
    /3\.6\.\s*Listing Period[\s\S]*?begins on\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})/i
  );

  const listingEnds_3_6 = matchOne(
    /3\.6\.\s*Listing Period[\s\S]*?\(2\)\s*([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4})/i
  );

  // Buyer signers at the end:
  //   Buyer:
  //   Date: 10/21/2025
  //    Buyer: Leonard W. Vessels
  //   Date: 10/21/2025
  //    Buyer: Kimberly S. Vessels
  //
  // This will also pick up the 3.1 line, but that's okay — we de-duplicate.
  const buyerSigners = [];
  const buyerRegex = /Buyer:\s*([^\n]+)/gi;
  let m;
  while ((m = buyerRegex.exec(text)) !== null) {
    const name = m[1].trim();
    if (name && !buyerSigners.includes(name)) {
      buyerSigners.push(name);
    }
  }

  return {
    buyer_3_1,               // raw Buyer line (individual(s) or entity/trust/LLC)
    propertyDescription_3_4,  // any criteria text in §3.4
    listingBegins_3_6,        // start date of the agreement
    listingEnds_3_6,          // end date of the agreement
    buyerSigners,             // array of Buyer signature names at the bottom
  };
}
